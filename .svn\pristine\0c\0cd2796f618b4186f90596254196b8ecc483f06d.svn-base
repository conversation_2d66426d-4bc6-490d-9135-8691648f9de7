package com.ruoyi.ffsafe.scantaskapi.domain;

import com.ruoyi.ffsafe.api.domain.FfsafeApiConfig;
import lombok.Data;
import lombok.ToString;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpRequestBase;
import org.springframework.stereotype.Component;

@Data
@ToString
@Component
public class QueryTaskReportParam extends ParamBase implements RequestBase {
    private int reportId;
    private String userId;
    private Long deviceConfigId;

    public void parseParam(int reportId) {
        this.reportId = reportId;
        userId = "1";
    }

    @Override
    public HttpRequestBase getRequestBase(Long deviceId) {
        /*if (ffurl == null) {
            if (!updateFfsafeApiConfig(deviceId)) {
                return null;
            }
        }*/
        FfsafeApiConfig ffsafeApiConfig = getFfsafeApiConfig();
        String ffurl = ffsafeApiConfig.getUrl();
        String fftoken = ffsafeApiConfig.getToken();

        String fullUrl = ffurl + "/v1/report/" + userId + "/" + reportId +"?access_token=" + fftoken;

        HttpGet httpGet = new HttpGet(fullUrl);
        return httpGet;
    }
}
