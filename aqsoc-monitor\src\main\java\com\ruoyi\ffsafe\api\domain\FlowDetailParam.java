package com.ruoyi.ffsafe.api.domain;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.utils.DateUtils;
import lombok.Data;

import java.util.Date;

@Data
public class FlowDetailParam {
    private static final int TIME_INTERVAL = 60 * 1000 * 10; // 时间区间10分钟

    private String startTime;
    private String endTime;
    private int page;


    public void parseStartTime(Date startTime) {
        Date now = DateUtils.getNowDate();
        if(startTime.getTime() > now.getTime()){
            startTime = new Date(now.getTime() - 1000);
        }
        this.startTime = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, new Date(startTime.getTime() + 1000));
        Date endDate = new Date(startTime.getTime() + TIME_INTERVAL);
        if(endDate.getTime() > DateUtils.getNowDate().getTime()){
            endDate = DateUtils.getNowDate();
        }
        this.endTime = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", endDate);
        this.page = -1;    // 采用不分页
    }

    public FlowDetailParam getNextPageParam() {
        FlowDetailParam flowDetailParam = new FlowDetailParam();
        flowDetailParam.setPage(page + 1);
        flowDetailParam.setStartTime(startTime);
        flowDetailParam.setEndTime(endTime);

        return flowDetailParam;
    }

    public FlowDetailParam getNextTimeParam() {
        FlowDetailParam flowDetailParam = new FlowDetailParam();
        flowDetailParam.setPage(-1);   // 采用不分页
        flowDetailParam.startTime = this.endTime;
        Date temp = DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, flowDetailParam.startTime);
        Date endDate = new Date(temp.getTime() + TIME_INTERVAL);
        if(endDate.getTime() > DateUtils.getNowDate().getTime()){
            endDate = DateUtils.getNowDate();
        }
        flowDetailParam.endTime = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, endDate);

        return flowDetailParam;
    }

    public String toString() {
        String ret = "&start_time=";
        ret += startTime;
        ret += "&end_time=" + endTime;
        ret += "&page=" + page;

        return ret.replaceAll(" ","%20").replaceAll(":", "%3A");
    }
}
