package com.ruoyi.ffsafe.scantaskapi.event;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.exception.job.TaskException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.bean.BeanUtil;
import com.ruoyi.ffsafe.scantaskapi.domain.*;
import com.ruoyi.ffsafe.scantaskapi.service.IFfsafeScantaskSummaryService;
import com.ruoyi.ffsafe.scantaskapi.service.IWebScanService;
import com.ruoyi.quartz.domain.SysJob;
import com.ruoyi.quartz.service.ISysJobService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component("WebVulnScan")
public class WebVulnScan {
    private static final int MAX_TASK_NUM = 30;

    @Autowired
    private IWebScanService webScanService;
    @Autowired
    private IFfsafeScantaskSummaryService scantaskSummaryService;
    @Autowired
    private WebScanTaskParam webScanTaskParam;
    @Autowired
    private ScanResultMonitorEvent hostScanResultMonitorEvent;
    @Autowired
    private ISysJobService sysJobService;


    private boolean checkCreateTask() {
        List<JSONObject> currentTaskList = hostScanResultMonitorEvent.cloneWebTaskList();
        if (currentTaskList.size() >= MAX_TASK_NUM) {
            return false;
        }

        return true;
    }

    public void scan(String jobId, String scanParam) throws Exception {
        if (!checkCreateTask()) {
            Thread.sleep(2000);
            throw new Exception("系统主机扫描线程已达最大， 请稍后执行。");
        }
        SysJob sysJob = sysJobService.selectJobById(Long.valueOf(jobId));

        if (sysJob.getCurrentStatus() == SysJob.PROCESS_RUNNING) {
            throw new Exception("当前任务正在扫描中, 请忽重复调度.");
        }

        log.info("开始非凡web漏扫, jodid: " + jobId + " param: " + scanParam);
        WebScanTaskParam params = new WebScanTaskParam();
        params.parseParam(scanParam);
        CreateWebScanTaskResult createWebScanTaskResult = null;
        try {
            createWebScanTaskResult = webScanService.createWebscanTask(params);
            if ((createWebScanTaskResult == null) ||(createWebScanTaskResult.getTaskIdList() == null)) {
                throw new Exception("创建web扫描任务失败!");
            }

            int taskId = createWebScanTaskResult.getTaskIdList().get(0);
            FfsafeScantaskSummary ffsafeScantaskSummary = new FfsafeScantaskSummary();
            ffsafeScantaskSummary.setJobId(Integer.valueOf(jobId));
            ffsafeScantaskSummary.setTaskId(taskId);
            ffsafeScantaskSummary.setTaskType(HostVulnScan.WEB_SCAN);
            ffsafeScantaskSummary.setTaskStatus(ScanResultMonitorEvent.WEBTASK_SCHEDULING);
            if (scantaskSummaryService.insertFfsafeScantaskSummary(ffsafeScantaskSummary) > 0) {
                JSONObject scanTaskSummaryParams = new JSONObject();
                scanTaskSummaryParams.put("taskId", taskId);
                scanTaskSummaryParams.put("jobId", jobId);
                scanTaskSummaryParams.put("deviceConfigId", sysJob.getDeviceConfigId());
                hostScanResultMonitorEvent.addWebScanTask(scanTaskSummaryParams);
                sysJob.setCurrentStatus(SysJob.PROCESS_RUNNING);
            }
        } catch (Exception e) {
            log.warn("创建web扫描任务失败, param:  " + scanParam + " 失败原因 :" + e.getMessage());
            throw e;
        } finally {
            try {
                hostScanResultMonitorEvent.updateSysJob(sysJob);
            } catch (Exception e) {
                e.printStackTrace();
                log.warn("web漏洞扫描更新任务状态失败, param:  " + scanParam + " 失败原因 :" + e.getMessage());
            }
        }
    }

    private SysJob getSysJobByTarget(String target) throws SchedulerException, TaskException {
        if (target == null)
            return null;

        SysJob sysJob = new SysJob();
        sysJob.setJobGroup("ASSET_SCAN");
        sysJob.setJobType(SysJob.WEB_SCAN);
        sysJob.setCronExpression("* * * * * ?");
        sysJob.setJobName(target + "_" + DateUtils.dateTimeNow());
        String tempTarget = target + " " + target;
        String jobTarget = "WebVulnScan.scan('${jobId}'," + "'" + tempTarget + "')";
        sysJob.setInvokeTarget(jobTarget);
        sysJob.setCurrentStatus(0);

        return sysJob;
    }

    public boolean addWebScanJob(String target) throws SchedulerException, TaskException {
        boolean bRet = false;

        SysJob sysJob = getSysJobByTarget(target);
        if (sysJobService.insertJob(sysJob) > 0) {
            sysJob.setInvokeTarget(sysJob.getInvokeTarget().replace("${jobId}", String.valueOf(sysJob.getJobId())));
            int nRet = sysJobService.updateJob(sysJob);
            bRet = nRet > 0;
        }
        return bRet;
    }
}
