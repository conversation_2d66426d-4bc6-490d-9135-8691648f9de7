package com.ruoyi.common.utils;

import java.util.List;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.thread.ThreadUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 线程相关工具类.
 *
 * <AUTHOR>
 */
public class Threads
{
    private static final Logger logger = LoggerFactory.getLogger(Threads.class);

    /**
     * sleep等待,单位为毫秒
     */
    public static void sleep(long milliseconds)
    {
        try
        {
            Thread.sleep(milliseconds);
        }
        catch (InterruptedException e)
        {
            return;
        }
    }

    /**
     * 停止线程池
     * 先使用shutdown, 停止接收新任务并尝试完成所有已存在任务.
     * 如果超时, 则调用shutdownNow, 取消在workQueue中Pending的任务,并中断所有阻塞函数.
     * 如果仍然超時，則強制退出.
     * 另对在shutdown时线程本身被调用中断做了处理.
     */
    public static void shutdownAndAwaitTermination(ExecutorService pool)
    {
        if (pool != null && !pool.isShutdown())
        {
            pool.shutdown();
            try
            {
                if (!pool.awaitTermination(120, TimeUnit.SECONDS))
                {
                    pool.shutdownNow();
                    if (!pool.awaitTermination(120, TimeUnit.SECONDS))
                    {
                        logger.info("Pool did not terminate");
                    }
                }
            }
            catch (InterruptedException ie)
            {
                pool.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 打印线程异常信息
     */
    public static void printException(Runnable r, Throwable t)
    {
        if (t == null && r instanceof Future<?>)
        {
            try
            {
                Future<?> future = (Future<?>) r;
                if (future.isDone())
                {
                    future.get();
                }
            }
            catch (CancellationException ce)
            {
                t = ce;
            }
            catch (ExecutionException ee)
            {
                t = ee.getCause();
            }
            catch (InterruptedException ie)
            {
                Thread.currentThread().interrupt();
            }
        }
        if (t != null)
        {
            logger.error(t.getMessage(), t);
        }
    }

    /**
     * 异步批量执行并等待完成
     * @param tasks 任务列表
     */
    public static void batchAsyncExecute(List<Runnable> tasks) {
        if(CollUtil.isEmpty(tasks)){
            return;
        }
        // 创建线程池（根据任务数量调整核心线程数）
        int poolSize = tasks.size();
        if(poolSize > 100){
            poolSize = 100;
        }
        ExecutorService executor = ThreadUtil.newExecutor(poolSize, poolSize,10000);
        try {
            // 将所有任务转为CompletableFuture
            TimeInterval timer = DateUtil.timer();
            logger.info("开始执行任务,任务数:{}...", tasks.size());
            // 组合所有Future并阻塞等待
            CompletableFuture.allOf(tasks.stream()
                    .map(task -> CompletableFuture.runAsync(task, executor)).toArray(CompletableFuture[]::new)).join();

            logger.info("任务执行完成,耗时: {}", timer.intervalPretty());
        } finally {
            // 关闭线程池（根据实际情况决定是否立即关闭）
            executor.shutdown();
        }
    }
}
