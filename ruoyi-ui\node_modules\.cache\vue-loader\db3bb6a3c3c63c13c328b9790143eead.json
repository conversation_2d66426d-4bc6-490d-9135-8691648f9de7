{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\apiAlarmList.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\apiAlarmList.vue", "mtime": 1755584508951}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0Rmxvd1Jpc2tBc3NldHMsIGhhbmRsZUFsYXJtLCBiYXRjaEhhbmRsZUFsYXJtcywgZGVsRmxvd1Jpc2tBc3NldHMgfSBmcm9tICdAL2FwaS9mZnNhZmUvZmxvd1Jpc2tBc3NldHMnDQppbXBvcnQge2xpc3REZXZpY2VDb25maWd9IGZyb20gIkAvYXBpL2Zmc2FmZS9kZXZpY2VDb25maWciOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdBcGlBbGFybUxpc3QnLA0KICBkaWN0czogWydmbG93X3Jpc2tfdHlwZSddLA0KICBwcm9wczogew0KICAgIHByb3BzQWN0aXZlTmFtZTogew0KICAgICAgdHlwZTogU3RyaW5nLA0KICAgICAgZGVmYXVsdDogJycNCiAgICB9LA0KICAgIHByb3BzUXVlcnlQYXJhbXM6IHsNCiAgICAgIHR5cGU6IE9iamVjdCwNCiAgICAgIGRlZmF1bHQ6IG51bGwNCiAgICB9DQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGRldmljZUNvbmZpZ0xpc3Q6IFtdLA0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgLy8g6YCJ5Lit5pWw57uEDQogICAgICBpZHM6IFtdLA0KICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoDQogICAgICBzaW5nbGU6IHRydWUsDQogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgNCiAgICAgIG11bHRpcGxlOiB0cnVlLA0KICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2DQogICAgICBzaG93U2VhcmNoOiBmYWxzZSwNCiAgICAgIC8vIOaAu+adoeaVsA0KICAgICAgdG90YWw6IDAsDQogICAgICAvLyBBUEnlkYrorabliJfooagNCiAgICAgIGFwaUFsYXJtTGlzdDogW10sDQogICAgICAvLyDlvLnlh7rlsYLmoIfpopgNCiAgICAgIHRpdGxlOiAnJywNCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgg0KICAgICAgb3BlbjogZmFsc2UsDQogICAgICAvLyDmmK/lkKbmmL7npLrlpITnva7lvLnlh7rlsYINCiAgICAgIGRpc3Bvc2VPcGVuOiBmYWxzZSwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuaJuemHj+WkhOe9ruW8ueWHuuWxgg0KICAgICAgYmF0Y2hEaXNwb3NlT3BlbjogZmFsc2UsDQogICAgICAvLyDmmK/lkKbmmL7npLror6bmg4XlvLnlh7rlsYINCiAgICAgIGRldGFpbE9wZW46IGZhbHNlLA0KICAgICAgLy8g6K+m5oOF5pWw5o2uDQogICAgICBkZXRhaWxEYXRhOiB7fSwNCiAgICAgIC8vIOWkhOe9ruihqOWNlQ0KICAgICAgZGlzcG9zZUZvcm06IHsNCiAgICAgICAgaWQ6IG51bGwsDQogICAgICAgIGhhbmRsZVN0YXRlOiB1bmRlZmluZWQsDQogICAgICAgIGhhbmRsZURlc2M6ICcnDQogICAgICB9LA0KICAgICAgLy8g5aSE572u6KGo5Y2V6aqM6K+B6KeE5YiZDQogICAgICBkaXNwb3NlUnVsZXM6IHsNCiAgICAgICAgaGFuZGxlU3RhdGU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup5aSE572u54q25oCBJywgdHJpZ2dlcjogJ2NoYW5nZScgfQ0KICAgICAgICBdDQogICAgICB9LA0KICAgICAgLy8g5om56YeP5aSE572u6KGo5Y2VDQogICAgICBiYXRjaERpc3Bvc2VGb3JtOiB7DQogICAgICAgIGV2ZW50SWRzOiBbXSwNCiAgICAgICAgaGFuZGxlU3RhdGU6ICcnLA0KICAgICAgICBoYW5kbGVEZXNjOiAnJw0KICAgICAgfSwNCiAgICAgIC8vIOaXpeacn+iMg+WbtA0KICAgICAgZGF0ZVJhbmdlOiBbXSwNCiAgICAgIC8vIOafpeivouWPguaVsA0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgICByaXNrQXNzZXRzOiB1bmRlZmluZWQsDQogICAgICAgIHJpc2tUeXBlOiB1bmRlZmluZWQsDQogICAgICAgIGhhbmRsZVN0YXRlOiB1bmRlZmluZWQsDQogICAgICAgIHBhcmFtczogew0KICAgICAgICAgIGJlZ2luVGltZTogdW5kZWZpbmVkLA0KICAgICAgICAgIGVuZFRpbWU6IHVuZGVmaW5lZA0KICAgICAgICB9DQogICAgICB9LA0KICAgICAgLy8g5aSE572u54q25oCB5a2X5YW4DQogICAgICBoYW5kbGVTdGF0ZU9wdGlvbnM6IFsNCiAgICAgICAgeyB2YWx1ZTogMCwgbGFiZWw6ICfmnKrlpITnva4nIH0sDQogICAgICAgIHsgdmFsdWU6IDEsIGxhYmVsOiAn5bey5aSE572uJyB9LA0KICAgICAgICB7IHZhbHVlOiAyLCBsYWJlbDogJ+W/veeVpScgfSwNCiAgICAgICAgeyB2YWx1ZTogMywgbGFiZWw6ICflpITnva7kuK0nIH0NCiAgICAgIF0sDQogICAgICBoYW5kbGVTdGF0ZU9wdGlvbjogWw0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6ICflt7LlpITnva4nLA0KICAgICAgICAgIHZhbHVlOiAxDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBsYWJlbDogJ+W/veeVpScsDQogICAgICAgICAgdmFsdWU6IDINCiAgICAgICAgfQ0KICAgICAgXQ0KICAgIH0NCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICBwcm9wc0FjdGl2ZU5hbWU6IHsNCiAgICAgIGhhbmRsZXIobmV3VmFsKSB7DQogICAgICAgIGlmIChuZXdWYWwgPT09ICdhcGlBbGFybScpIHsNCiAgICAgICAgICAvLyDnoa7kv53ml7bpl7Tlj4LmlbDlt7Lorr7nva7vvIzpgb/lhY3ml6Dml7bpl7Tlj4LmlbDnmoTmn6Xor6INCiAgICAgICAgICBpZiAoIXRoaXMucXVlcnlQYXJhbXMucGFyYW1zLmJlZ2luVGltZSB8fCAhdGhpcy5xdWVyeVBhcmFtcy5wYXJhbXMuZW5kVGltZSkgew0KICAgICAgICAgICAgdGhpcy5zZXREZWZhdWx0RGF0ZVJhbmdlKCkNCiAgICAgICAgICB9DQogICAgICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICAgICAgfQ0KICAgICAgfSwNCiAgICAgIGltbWVkaWF0ZTogdHJ1ZQ0KICAgIH0sDQogICAgcHJvcHNRdWVyeVBhcmFtczogew0KICAgICAgaGFuZGxlcihuZXdWYWwpIHsNCiAgICAgICAgaWYgKG5ld1ZhbCkgew0KICAgICAgICAgIC8vIOS/neeVmeW3suiuvue9rueahOaXtumXtOiMg+WbtOWPguaVsO+8jOmBv+WFjeiiq+epuueahHBhcmFtc+imhueblg0KICAgICAgICAgIGNvbnN0IG9yaWdpbmFsQmVnaW5UaW1lID0gdGhpcy5xdWVyeVBhcmFtcy5wYXJhbXMuYmVnaW5UaW1lDQogICAgICAgICAgY29uc3Qgb3JpZ2luYWxFbmRUaW1lID0gdGhpcy5xdWVyeVBhcmFtcy5wYXJhbXMuZW5kVGltZQ0KDQogICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcyA9IHsgLi4udGhpcy5xdWVyeVBhcmFtcywgLi4ubmV3VmFsIH0NCg0KICAgICAgICAgIC8vIOWmguaenOaWsOeahOafpeivouWPguaVsOayoeacieaXtumXtOiMg+WbtO+8jOWImeaBouWkjeWOn+acieeahOaXtumXtOiMg+WbtA0KICAgICAgICAgIGlmICghbmV3VmFsLnBhcmFtcyB8fCAoIW5ld1ZhbC5wYXJhbXMuYmVnaW5UaW1lICYmICFuZXdWYWwucGFyYW1zLmVuZFRpbWUpKSB7DQogICAgICAgICAgICBpZiAob3JpZ2luYWxCZWdpblRpbWUgJiYgb3JpZ2luYWxFbmRUaW1lKSB7DQogICAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFyYW1zLmJlZ2luVGltZSA9IG9yaWdpbmFsQmVnaW5UaW1lDQogICAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFyYW1zLmVuZFRpbWUgPSBvcmlnaW5hbEVuZFRpbWUNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQoNCiAgICAgICAgICAvLyDlj6rmnInlvZPliY3moIfnrb7mmK9hcGlBbGFybeaXtuaJjeinpuWPkeafpeivou+8jOmBv+WFjemHjeWkjeafpeivog0KICAgICAgICAgIGlmICh0aGlzLnByb3BzQWN0aXZlTmFtZSA9PT0gJ2FwaUFsYXJtJykgew0KICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0sDQogICAgICBkZWVwOiB0cnVlDQogICAgfQ0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIC8vIOiuvue9rum7mOiupOafpeivouaXpeacn+iMg+WbtOS4uuacgOi/kTflpKkNCiAgICB0aGlzLnNldERlZmF1bHREYXRlUmFuZ2UoKQ0KICAgIC8vIOS4jeWcqGNyZWF0ZWTkuK3osIPnlKhnZXRMaXN077yM55Sxd2F0Y2jlpITnkIYNCiAgICB0aGlzLmdldERldmljZUNvbmZpZ0xpc3QoKTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGdldERldmljZUNvbmZpZ0xpc3QoKXsNCiAgICAgIGxpc3REZXZpY2VDb25maWcoe3F1ZXJ5QWxsRGF0YTogdHJ1ZX0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgdGhpcy5kZXZpY2VDb25maWdMaXN0ID0gcmVzLnJvd3M7DQogICAgICB9KQ0KICAgIH0sDQogICAgLyoqIOiuvue9rum7mOiupOaXpeacn+iMg+WbtCAqLw0KICAgIHNldERlZmF1bHREYXRlUmFuZ2UoKSB7DQogICAgICBjb25zdCBlbmQgPSBuZXcgRGF0ZSgpDQogICAgICBjb25zdCBzdGFydCA9IG5ldyBEYXRlKCkNCiAgICAgIHN0YXJ0LnNldFRpbWUoc3RhcnQuZ2V0VGltZSgpIC0gMzYwMCAqIDEwMDAgKiAyNCAqIDcpDQoNCiAgICAgIC8vIOehruS/neW8gOWni+aXtumXtOS4uiAwMDowMDowMO+8jOe7k+adn+aXtumXtOS4uiAyMzo1OTo1OQ0KICAgICAgc3RhcnQuc2V0SG91cnMoMCwgMCwgMCwgMCkNCiAgICAgIGVuZC5zZXRIb3VycygyMywgNTksIDU5LCA5OTkpDQoNCiAgICAgIHRoaXMuZGF0ZVJhbmdlID0gWw0KICAgICAgICB0aGlzLnBhcnNlVGltZShzdGFydCwgJ3t5fS17bX0te2R9IHtofTp7aX06e3N9JyksDQogICAgICAgIHRoaXMucGFyc2VUaW1lKGVuZCwgJ3t5fS17bX0te2R9IHtofTp7aX06e3N9JykNCiAgICAgIF0NCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFyYW1zLmJlZ2luVGltZSA9IHRoaXMuZGF0ZVJhbmdlWzBdDQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhcmFtcy5lbmRUaW1lID0gdGhpcy5kYXRlUmFuZ2VbMV0NCiAgICB9LA0KICAgIC8qKiDmn6Xor6JBUEnlkYrorabliJfooaggKi8NCiAgICBnZXRMaXN0KCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQ0KDQogICAgICAvLyDpgJrnn6XniLbnu4Tku7blkIzmraXmn6Xor6LmnaHku7blkozmjInpkq7pgInkuK3nirbmgIENCiAgICAgIHRoaXMuJGVtaXQoJ3F1ZXJ5LWNoYW5nZScsIHsNCiAgICAgICAgcmlza1R5cGU6IHRoaXMucXVlcnlQYXJhbXMucmlza1R5cGUsDQogICAgICAgIGRldmljZUNvbmZpZ0lkOiB0aGlzLnF1ZXJ5UGFyYW1zLmRldmljZUNvbmZpZ0lkDQogICAgICB9KQ0KDQogICAgICAvLyDlkIzmraXor7fmsYLnsbvlnovnu5/orqHmlbDmja4NCiAgICAgIHRoaXMuJGVtaXQoJ2dldExpc3QnLCB7IC4uLnRoaXMucXVlcnlQYXJhbXMgfSkNCiAgICAgIGxpc3RGbG93Umlza0Fzc2V0cyh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5hcGlBbGFybUxpc3QgPSByZXNwb25zZS5yb3dzDQogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbA0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8vIOaXpeacn+iMg+WbtOWPkeeUn+WPmOWMlg0KICAgIGhhbmRsZURhdGVSYW5nZUNoYW5nZSh2YWwpIHsNCiAgICAgIGlmICh2YWwgJiYgdmFsLmxlbmd0aCA9PT0gMikgew0KICAgICAgICAvLyDnoa7kv53lvIDlp4vml7bpl7TkuLogMDA6MDA6MDDvvIznu5PmnZ/ml7bpl7TkuLogMjM6NTk6NTkNCiAgICAgICAgY29uc3Qgc3RhcnREYXRlID0gbmV3IERhdGUodmFsWzBdKQ0KICAgICAgICBjb25zdCBlbmREYXRlID0gbmV3IERhdGUodmFsWzFdKQ0KDQogICAgICAgIHN0YXJ0RGF0ZS5zZXRIb3VycygwLCAwLCAwLCAwKQ0KICAgICAgICBlbmREYXRlLnNldEhvdXJzKDIzLCA1OSwgNTksIDk5OSkNCg0KICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhcmFtcy5iZWdpblRpbWUgPSB0aGlzLnBhcnNlVGltZShzdGFydERhdGUsICd7eX0te219LXtkfSB7aH06e2l9OntzfScpDQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFyYW1zLmVuZFRpbWUgPSB0aGlzLnBhcnNlVGltZShlbmREYXRlLCAne3l9LXttfS17ZH0ge2h9OntpfTp7c30nKQ0KDQogICAgICAgIC8vIOabtOaWsGRhdGVSYW5nZeaYvuekuuWAvA0KICAgICAgICB0aGlzLmRhdGVSYW5nZSA9IFsNCiAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhcmFtcy5iZWdpblRpbWUsDQogICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYXJhbXMuZW5kVGltZQ0KICAgICAgICBdDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhcmFtcy5iZWdpblRpbWUgPSB1bmRlZmluZWQNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYXJhbXMuZW5kVGltZSA9IHVuZGVmaW5lZA0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5Y+W5raI5oyJ6ZKuDQogICAgY2FuY2VsKCkgew0KICAgICAgdGhpcy5vcGVuID0gZmFsc2UNCiAgICAgIHRoaXMucmVzZXQoKQ0KICAgIH0sDQogICAgLy8g6KGo5Y2V6YeN572uDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgIGlkOiBudWxsLA0KICAgICAgICBoYW5kbGVTdGF0ZTogJycsDQogICAgICAgIGhhbmRsZURlc2M6ICcnDQogICAgICB9DQogICAgICB0aGlzLnJlc2V0Rm9ybSgnZm9ybScpDQogICAgfSwNCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICAvLyDlpoLmnpzml7bpl7TojIPlm7TkuLrnqbrvvIzorr7nva7pu5jorqTml7bpl7TojIPlm7QNCiAgICAgIGlmICghdGhpcy5xdWVyeVBhcmFtcy5wYXJhbXMuYmVnaW5UaW1lIHx8ICF0aGlzLnF1ZXJ5UGFyYW1zLnBhcmFtcy5lbmRUaW1lKSB7DQogICAgICAgIHRoaXMuc2V0RGVmYXVsdERhdGVSYW5nZSgpDQogICAgICB9DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxDQoNCiAgICAgIC8vIOmAmuefpeeItue7hOS7tuWQjOatpeafpeivouadoeS7tuWSjOaMiemSrumAieS4reeKtuaAgQ0KICAgICAgdGhpcy4kZW1pdCgncXVlcnktY2hhbmdlJywgew0KICAgICAgICByaXNrVHlwZTogdGhpcy5xdWVyeVBhcmFtcy5yaXNrVHlwZSwNCiAgICAgICAgZGV2aWNlQ29uZmlnSWQ6IHRoaXMucXVlcnlQYXJhbXMuZGV2aWNlQ29uZmlnSWQNCiAgICAgIH0pDQoNCiAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgfSwNCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMuZGF0ZVJhbmdlID0gW10NCiAgICAgIHRoaXMucmVzZXRGb3JtKCdxdWVyeUZvcm0nKQ0KICAgICAgLy8g5omL5Yqo6YeN572u5omA5pyJ5p+l6K+i5a2X5q61DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnJpc2tBc3NldHMgPSB1bmRlZmluZWQNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucmlza1R5cGUgPSB1bmRlZmluZWQNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuaGFuZGxlU3RhdGUgPSB1bmRlZmluZWQNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuZGV2aWNlQ29uZmlnSWQgPSB1bmRlZmluZWQNCiAgICAgIHRoaXMuc2V0RGVmYXVsdERhdGVSYW5nZSgpDQogICAgICAvLyDpgJrnn6XniLbnu4Tku7bph43nva7mjInpkq7pgInkuK3nirbmgIENCiAgICAgIHRoaXMuJGVtaXQoJ3Jlc2V0LWJ1dHRvbicpDQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCkNCiAgICB9LA0KICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrg0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0uaWQpDQogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT09IDENCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aA0KICAgIH0sDQogICAgLyoqIOWkhOe9ruaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZURpc3Bvc2Uocm93KSB7DQogICAgICB0aGlzLmRpc3Bvc2VGb3JtID0gew0KICAgICAgICBpZDogcm93LmlkLA0KICAgICAgICBoYW5kbGVTdGF0ZTogcm93LmhhbmRsZVN0YXRlID09PSAyID8gcm93LmhhbmRsZVN0YXRlIDogdW5kZWZpbmVkLA0KICAgICAgICBoYW5kbGVEZXNjOiByb3cuaGFuZGxlU3RhdGUgPT09IDIgPyAocm93LmhhbmRsZURlc2MgfHwgJycpIDogJycNCiAgICAgIH0NCiAgICAgIHRoaXMuZGlzcG9zZU9wZW4gPSB0cnVlDQogICAgICAvLyDmuIXpmaTooajljZXpqozor4HnirbmgIENCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgaWYgKHRoaXMuJHJlZnMuZGlzcG9zZUZvcm0pIHsNCiAgICAgICAgICB0aGlzLiRyZWZzLmRpc3Bvc2VGb3JtLmNsZWFyVmFsaWRhdGUoKQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgLyoqIOaPkOS6pOWkhOe9riAqLw0KICAgIHN1Ym1pdERpc3Bvc2UoKSB7DQogICAgICB0aGlzLiRyZWZzWydkaXNwb3NlRm9ybSddLnZhbGlkYXRlKHZhbGlkID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgaGFuZGxlQWxhcm0oew0KICAgICAgICAgICAgaWQ6IHRoaXMuZGlzcG9zZUZvcm0uaWQsDQogICAgICAgICAgICBoYW5kbGVTdGF0ZTogdGhpcy5kaXNwb3NlRm9ybS5oYW5kbGVTdGF0ZSwNCiAgICAgICAgICAgIGhhbmRsZURlc2M6IHRoaXMuZGlzcG9zZUZvcm0uaGFuZGxlRGVzYw0KICAgICAgICAgIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygn5aSE572u5oiQ5YqfJykNCiAgICAgICAgICAgIHRoaXMuZGlzcG9zZU9wZW4gPSBmYWxzZQ0KICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgLyoqIOaJuemHj+WkhOe9ruaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUJhdGNoRGlzcG9zZSgpIHsNCiAgICAgIGlmICh0aGlzLmlkcy5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoJ+ivt+iHs+WwkemAieaLqeS4gOadoeiusOW9lScpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgdGhpcy5iYXRjaERpc3Bvc2VGb3JtID0gew0KICAgICAgICBldmVudElkczogdGhpcy5pZHMsDQogICAgICAgIGhhbmRsZVN0YXRlOiAxLA0KICAgICAgICBoYW5kbGVEZXNjOiAnJw0KICAgICAgfQ0KICAgICAgdGhpcy5iYXRjaERpc3Bvc2VPcGVuID0gdHJ1ZQ0KICAgIH0sDQogICAgLyoqIOaPkOS6pOaJuemHj+WkhOe9riAqLw0KICAgIHN1Ym1pdEJhdGNoRGlzcG9zZSgpIHsNCiAgICAgIHRoaXMuJHJlZnNbJ2JhdGNoRGlzcG9zZUZvcm0nXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGNvbnN0IGV2ZW50SWRzID0gdGhpcy5iYXRjaERpc3Bvc2VGb3JtLmV2ZW50SWRzDQogICAgICAgICAgY29uc3QgaGFuZGxlU3RhdGUgPSB0aGlzLmJhdGNoRGlzcG9zZUZvcm0uaGFuZGxlU3RhdGUNCiAgICAgICAgICBjb25zdCBoYW5kbGVEZXNjID0gdGhpcy5iYXRjaERpc3Bvc2VGb3JtLmhhbmRsZURlc2MNCg0KICAgICAgICAgIGJhdGNoSGFuZGxlQWxhcm1zKHsNCiAgICAgICAgICAgIGV2ZW50SWRzOiBldmVudElkcywNCiAgICAgICAgICAgIGhhbmRsZVN0YXRlOiBoYW5kbGVTdGF0ZSwNCiAgICAgICAgICAgIGhhbmRsZURlc2M6IGhhbmRsZURlc2MNCiAgICAgICAgICB9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoJ+aJuemHj+WkhOe9ruaIkOWKnycpDQogICAgICAgICAgICB0aGlzLmJhdGNoRGlzcG9zZU9wZW4gPSBmYWxzZQ0KICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUV4cG9ydCgpIHsNCiAgICAgIHRoaXMuZG93bmxvYWQoDQogICAgICAgICdmZnNhZmUvZmxvd1Jpc2tBc3NldHMvZXhwb3J0JywNCiAgICAgICAgew0KICAgICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMNCiAgICAgICAgfSwNCiAgICAgICAgJ0FQSeWRiuitpuaVsOaNri54bHN4Jw0KICAgICAgKQ0KICAgIH0sDQogICAgLyoqIOivpuaDheaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZURldGFpbChyb3cpIHsNCiAgICAgIHRoaXMuZGV0YWlsRGF0YSA9IHsgLi4ucm93IH0NCiAgICAgIHRoaXMuZGV0YWlsT3BlbiA9IHRydWUNCiAgICB9LA0KICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVEZWxldGUocm93KSB7DQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTor6VBUEnlkYroraborrDlvZXvvJ8nKS50aGVuKCgpID0+IHsNCiAgICAgICAgcmV0dXJuIHRoaXMuZGVsZXRlRmxvd1Jpc2tBc3NldHMocm93LmlkKQ0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoJ+WIoOmZpOaIkOWKnycpDQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSkNCiAgICB9LA0KICAgIC8qKiDliKDpmaRBUEnlkYroraborrDlvZUgKi8NCiAgICBkZWxldGVGbG93Umlza0Fzc2V0cyhpZCkgew0KICAgICAgcmV0dXJuIGRlbEZsb3dSaXNrQXNzZXRzKGlkKQ0KICAgIH0sDQogICAgLyoqIOiOt+WPlumjjumZqeexu+WIq+agh+etviAqLw0KICAgIGdldFJpc2tUeXBlTGFiZWwocmlza1R5cGUpIHsNCiAgICAgIGNvbnN0IGRpY3QgPSB0aGlzLmRpY3QudHlwZS5mbG93X3Jpc2tfdHlwZS5maW5kKGQgPT4gZC52YWx1ZSA9PT0gcmlza1R5cGUpDQogICAgICByZXR1cm4gZGljdCA/IGRpY3QubGFiZWwgOiByaXNrVHlwZQ0KICAgIH0sDQogICAgLyoqIOiOt+WPluWkhOe9rueKtuaAgeagh+etviAqLw0KICAgIGdldEhhbmRsZVN0YXRlTGFiZWwoaGFuZGxlU3RhdGUpIHsNCiAgICAgIGNvbnN0IG9wdGlvbiA9IHRoaXMuaGFuZGxlU3RhdGVPcHRpb25zLmZpbmQoaXRlbSA9PiBpdGVtLnZhbHVlID09PSBoYW5kbGVTdGF0ZSkNCiAgICAgIHJldHVybiBvcHRpb24gPyBvcHRpb24ubGFiZWwgOiAn5pyq55+lJw0KICAgIH0sDQogICAgaGFuZGxlU3RhdGVGb3JtYXR0ZXIocm93LCBjb2x1bW4sIGNlbGxWYWx1ZSwgaW5kZXgpIHsNCiAgICAgIGxldCBuYW1lID0gJ+acquWkhOe9ricNCiAgICAgIGNvbnN0IG1hdGNoID0gdGhpcy5oYW5kbGVTdGF0ZU9wdGlvbnMuZmluZChpdGVtID0+IGl0ZW0udmFsdWUgPT09IGNlbGxWYWx1ZSkNCiAgICAgIGlmIChtYXRjaCkgew0KICAgICAgICBuYW1lID0gbWF0Y2gubGFiZWwNCiAgICAgIH0NCiAgICAgIHJldHVybiBuYW1lDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["apiAlarmList.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyQA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "apiAlarmList.vue", "sourceRoot": "src/views/frailty/event/component", "sourcesContent": ["<template>\r\n  <div class=\"custom-container\">\r\n    <div class=\"custom-content-container-right\">\r\n      <div class=\"custom-content-search-box\">\r\n        <el-form\r\n          ref=\"queryForm\"\r\n          :model=\"queryParams\"\r\n          size=\"small\"\r\n          label-position=\"right\"\r\n          label-width=\"70px\"\r\n          :inline=\"true\"\r\n        >\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"告警时间\" prop=\"beginTime\">\r\n                <el-date-picker\r\n                  v-model=\"dateRange\"\r\n                  type=\"datetimerange\"\r\n                  range-separator=\"至\"\r\n                  start-placeholder=\"开始日期\"\r\n                  end-placeholder=\"结束日期\"\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                  @change=\"handleDateRangeChange\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"风险资产\" prop=\"riskAssets\">\r\n                <el-input\r\n                  v-model=\"queryParams.riskAssets\"\r\n                  placeholder=\"请输入风险资产\"\r\n                  clearable\r\n                  @keyup.enter.native=\"handleQuery\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"风险类别\" prop=\"riskType\">\r\n                <el-select v-model=\"queryParams.riskType\" placeholder=\"请选择风险类别\" clearable>\r\n                  <el-option\r\n                    v-for=\"dict in dict.type.flow_risk_type\"\r\n                    :key=\"dict.value\"\r\n                    :label=\"dict.label\"\r\n                    :value=\"dict.value\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item class=\"custom-search-btn\">\r\n                <el-button\r\n                  class=\"btn1\"\r\n                  size=\"small\"\r\n                  @click=\"handleQuery\"\r\n                >查询</el-button>\r\n                <el-button\r\n                  class=\"btn2\"\r\n                  size=\"small\"\r\n                  @click=\"resetQuery\"\r\n                >重置</el-button>\r\n                <el-button\r\n                  v-if=\"!showSearch\"\r\n                  class=\"btn2\"\r\n                  size=\"small\"\r\n                  icon=\"el-icon-arrow-down\"\r\n                  @click=\"showSearch = true\"\r\n                >展开</el-button>\r\n                <el-button\r\n                  v-else\r\n                  class=\"btn2\"\r\n                  size=\"small\"\r\n                  icon=\"el-icon-arrow-up\"\r\n                  @click=\"showSearch = false\"\r\n                >收起</el-button>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row v-if=\"showSearch\" :gutter=\"10\">\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"处置状态\" prop=\"handleState\">\r\n                <el-select v-model=\"queryParams.handleState\" placeholder=\"请选择处置状态\" clearable>\r\n                  <el-option\r\n                    v-for=\"dict in handleStateOptions\"\r\n                    :key=\"dict.value\"\r\n                    :label=\"dict.label\"\r\n                    :value=\"dict.value\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"所属探针\">\r\n                <el-select v-model=\"queryParams.deviceConfigId\" filterable clearable placeholder=\"请选择\">\r\n                  <el-option\r\n                    v-for=\"item in deviceConfigList\"\r\n                    :key=\"item.id\"\r\n                    :label=\"item.deviceName\"\r\n                    :value=\"item.id\">\r\n                  </el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-form>\r\n      </div>\r\n      <div class=\"custom-content-container\">\r\n        <div class=\"common-header\">\r\n          <div><span class=\"common-head-title\">告警列表</span></div>\r\n          <div class=\"common-head-right\">\r\n            <el-row :gutter=\"10\">\r\n              <el-col :span=\"1.5\">\r\n                <el-button\r\n                  v-hasPermi=\"['ffsafe:flowRiskAssets:export']\"\r\n                  class=\"btn1\"\r\n                  size=\"small\"\r\n                  @click=\"handleExport\"\r\n                >导出</el-button>\r\n              </el-col>\r\n            </el-row>\r\n          </div>\r\n        </div>\r\n        <el-table\r\n          v-loading=\"loading\"\r\n          height=\"100%\"\r\n          :data=\"apiAlarmList\"\r\n          @selection-change=\"handleSelectionChange\"\r\n        >\r\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n          <el-table-column label=\"告警更新时间\" align=\"left\" prop=\"updateTime\" width=\"160\" sortable>\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"风险资产\" align=\"left\" prop=\"riskAssets\" />\r\n          <el-table-column label=\"风险类别\" align=\"left\" prop=\"riskType\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <dict-tag :options=\"dict.type.flow_risk_type\" :value=\"scope.row.riskType\" />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"风险信息\" align=\"left\" prop=\"riskInfo\" />\r\n          <el-table-column label=\"处置状态\" align=\"center\" prop=\"handleState\" width=\"100\" :formatter=\"handleStateFormatter\" />\r\n          <el-table-column\r\n            label=\"操作\"\r\n            align=\"center\"\r\n            class-name=\"small-padding fixed-width\"\r\n            width=\"200\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <!--   <el-button\r\n                v-hasPermi=\"['ffsafe:flowRiskAssets:edit']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"handleDetail(scope.row)\"\r\n              >详情</el-button> -->\r\n              <el-button\r\n                v-hasPermi=\"['ffsafe:flowRiskAssets:remove']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                class=\"JNPF-table-delBtn\"\r\n                @click=\"handleDelete(scope.row)\"\r\n              >删除</el-button>\r\n              <el-button\r\n                v-if=\"scope.row.handleState !== 1\"\r\n                v-hasPermi=\"['ffsafe:flowRiskAssets:handle']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"handleDispose(scope.row)\"\r\n              >处置</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :total=\"total\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 处置对话框 -->\r\n    <el-dialog title=\"快速处置\" :visible.sync=\"disposeOpen\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"disposeForm\" :model=\"disposeForm\" :rules=\"disposeRules\" label-width=\"80px\">\r\n        <el-form-item label=\"处置状态\" prop=\"handleState\">\r\n          <el-select v-model=\"disposeForm.handleState\" placeholder=\"请选择处置状态\" clearable>\r\n            <el-option\r\n              v-for=\"dict in handleStateOption\"\r\n              :key=\"dict.value\"\r\n              :label=\"dict.label\"\r\n              :value=\"dict.value\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"处置备注\" prop=\"handleDesc\">\r\n          <el-input v-model=\"disposeForm.handleDesc\" type=\"textarea\" placeholder=\"请输入处置备注\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitDispose\">确 定</el-button>\r\n        <el-button @click=\"disposeOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 批量处置对话框 -->\r\n    <el-dialog title=\"批量处置\" :visible.sync=\"batchDisposeOpen\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"batchDisposeForm\" :model=\"batchDisposeForm\" label-width=\"80px\">\r\n        <el-form-item label=\"处置状态\" prop=\"handleState\">\r\n          <el-select v-model=\"batchDisposeForm.handleState\" placeholder=\"请选择处置状态\">\r\n            <el-option\r\n              v-for=\"dict in handleStateOptions\"\r\n              :key=\"dict.value\"\r\n              :label=\"dict.label\"\r\n              :value=\"dict.value\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"处置备注\" prop=\"handleDesc\">\r\n          <el-input v-model=\"batchDisposeForm.handleDesc\" type=\"textarea\" placeholder=\"请输入处置备注\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitBatchDispose\">确 定</el-button>\r\n        <el-button @click=\"batchDisposeOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 详情对话框 -->\r\n    <el-dialog title=\"API告警详情\" :visible.sync=\"detailOpen\" width=\"600px\" append-to-body>\r\n      <el-descriptions :column=\"2\" border>\r\n        <el-descriptions-item label=\"告警更新时间\">\r\n          {{ parseTime(detailData.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"风险资产\">\r\n          {{ detailData.riskAssets }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"风险类别\">\r\n          {{ getRiskTypeLabel(detailData.riskType) }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"引擎名称\">\r\n          {{ detailData.engineName }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"处置状态\">\r\n          {{ getHandleStateLabel(detailData.handleState) }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"处置人\">\r\n          {{ detailData.disposerName || '' }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"风险信息\" :span=\"2\">\r\n          {{ detailData.riskInfo }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item v-if=\"detailData.handleDesc\" label=\"处置描述\" :span=\"2\">\r\n          {{ detailData.handleDesc }}\r\n        </el-descriptions-item>\r\n      </el-descriptions>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"detailOpen = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listFlowRiskAssets, handleAlarm, batchHandleAlarms, delFlowRiskAssets } from '@/api/ffsafe/flowRiskAssets'\r\nimport {listDeviceConfig} from \"@/api/ffsafe/deviceConfig\";\r\n\r\nexport default {\r\n  name: 'ApiAlarmList',\r\n  dicts: ['flow_risk_type'],\r\n  props: {\r\n    propsActiveName: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    propsQueryParams: {\r\n      type: Object,\r\n      default: null\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      deviceConfigList: [],\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: false,\r\n      // 总条数\r\n      total: 0,\r\n      // API告警列表\r\n      apiAlarmList: [],\r\n      // 弹出层标题\r\n      title: '',\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 是否显示处置弹出层\r\n      disposeOpen: false,\r\n      // 是否显示批量处置弹出层\r\n      batchDisposeOpen: false,\r\n      // 是否显示详情弹出层\r\n      detailOpen: false,\r\n      // 详情数据\r\n      detailData: {},\r\n      // 处置表单\r\n      disposeForm: {\r\n        id: null,\r\n        handleState: undefined,\r\n        handleDesc: ''\r\n      },\r\n      // 处置表单验证规则\r\n      disposeRules: {\r\n        handleState: [\r\n          { required: true, message: '请选择处置状态', trigger: 'change' }\r\n        ]\r\n      },\r\n      // 批量处置表单\r\n      batchDisposeForm: {\r\n        eventIds: [],\r\n        handleState: '',\r\n        handleDesc: ''\r\n      },\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        riskAssets: undefined,\r\n        riskType: undefined,\r\n        handleState: undefined,\r\n        params: {\r\n          beginTime: undefined,\r\n          endTime: undefined\r\n        }\r\n      },\r\n      // 处置状态字典\r\n      handleStateOptions: [\r\n        { value: 0, label: '未处置' },\r\n        { value: 1, label: '已处置' },\r\n        { value: 2, label: '忽略' },\r\n        { value: 3, label: '处置中' }\r\n      ],\r\n      handleStateOption: [\r\n        {\r\n          label: '已处置',\r\n          value: 1\r\n        },\r\n        {\r\n          label: '忽略',\r\n          value: 2\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  watch: {\r\n    propsActiveName: {\r\n      handler(newVal) {\r\n        if (newVal === 'apiAlarm') {\r\n          // 确保时间参数已设置，避免无时间参数的查询\r\n          if (!this.queryParams.params.beginTime || !this.queryParams.params.endTime) {\r\n            this.setDefaultDateRange()\r\n          }\r\n          this.getList()\r\n        }\r\n      },\r\n      immediate: true\r\n    },\r\n    propsQueryParams: {\r\n      handler(newVal) {\r\n        if (newVal) {\r\n          // 保留已设置的时间范围参数，避免被空的params覆盖\r\n          const originalBeginTime = this.queryParams.params.beginTime\r\n          const originalEndTime = this.queryParams.params.endTime\r\n\r\n          this.queryParams = { ...this.queryParams, ...newVal }\r\n\r\n          // 如果新的查询参数没有时间范围，则恢复原有的时间范围\r\n          if (!newVal.params || (!newVal.params.beginTime && !newVal.params.endTime)) {\r\n            if (originalBeginTime && originalEndTime) {\r\n              this.queryParams.params.beginTime = originalBeginTime\r\n              this.queryParams.params.endTime = originalEndTime\r\n            }\r\n          }\r\n\r\n          // 只有当前标签是apiAlarm时才触发查询，避免重复查询\r\n          if (this.propsActiveName === 'apiAlarm') {\r\n            this.getList()\r\n          }\r\n        }\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  created() {\r\n    // 设置默认查询日期范围为最近7天\r\n    this.setDefaultDateRange()\r\n    // 不在created中调用getList，由watch处理\r\n    this.getDeviceConfigList();\r\n  },\r\n  methods: {\r\n    getDeviceConfigList(){\r\n      listDeviceConfig({queryAllData: true}).then(res => {\r\n        this.deviceConfigList = res.rows;\r\n      })\r\n    },\r\n    /** 设置默认日期范围 */\r\n    setDefaultDateRange() {\r\n      const end = new Date()\r\n      const start = new Date()\r\n      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)\r\n\r\n      // 确保开始时间为 00:00:00，结束时间为 23:59:59\r\n      start.setHours(0, 0, 0, 0)\r\n      end.setHours(23, 59, 59, 999)\r\n\r\n      this.dateRange = [\r\n        this.parseTime(start, '{y}-{m}-{d} {h}:{i}:{s}'),\r\n        this.parseTime(end, '{y}-{m}-{d} {h}:{i}:{s}')\r\n      ]\r\n      this.queryParams.params.beginTime = this.dateRange[0]\r\n      this.queryParams.params.endTime = this.dateRange[1]\r\n    },\r\n    /** 查询API告警列表 */\r\n    getList() {\r\n      this.loading = true\r\n\r\n      // 通知父组件同步查询条件和按钮选中状态\r\n      this.$emit('query-change', {\r\n        riskType: this.queryParams.riskType,\r\n        deviceConfigId: this.queryParams.deviceConfigId\r\n      })\r\n\r\n      // 同步请求类型统计数据\r\n      this.$emit('getList', { ...this.queryParams })\r\n      listFlowRiskAssets(this.queryParams).then(response => {\r\n        this.apiAlarmList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 日期范围发生变化\r\n    handleDateRangeChange(val) {\r\n      if (val && val.length === 2) {\r\n        // 确保开始时间为 00:00:00，结束时间为 23:59:59\r\n        const startDate = new Date(val[0])\r\n        const endDate = new Date(val[1])\r\n\r\n        startDate.setHours(0, 0, 0, 0)\r\n        endDate.setHours(23, 59, 59, 999)\r\n\r\n        this.queryParams.params.beginTime = this.parseTime(startDate, '{y}-{m}-{d} {h}:{i}:{s}')\r\n        this.queryParams.params.endTime = this.parseTime(endDate, '{y}-{m}-{d} {h}:{i}:{s}')\r\n\r\n        // 更新dateRange显示值\r\n        this.dateRange = [\r\n          this.queryParams.params.beginTime,\r\n          this.queryParams.params.endTime\r\n        ]\r\n      } else {\r\n        this.queryParams.params.beginTime = undefined\r\n        this.queryParams.params.endTime = undefined\r\n      }\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        handleState: '',\r\n        handleDesc: ''\r\n      }\r\n      this.resetForm('form')\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      // 如果时间范围为空，设置默认时间范围\r\n      if (!this.queryParams.params.beginTime || !this.queryParams.params.endTime) {\r\n        this.setDefaultDateRange()\r\n      }\r\n      this.queryParams.pageNum = 1\r\n\r\n      // 通知父组件同步查询条件和按钮选中状态\r\n      this.$emit('query-change', {\r\n        riskType: this.queryParams.riskType,\r\n        deviceConfigId: this.queryParams.deviceConfigId\r\n      })\r\n\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = []\r\n      this.resetForm('queryForm')\r\n      // 手动重置所有查询字段\r\n      this.queryParams.riskAssets = undefined\r\n      this.queryParams.riskType = undefined\r\n      this.queryParams.handleState = undefined\r\n      this.queryParams.deviceConfigId = undefined\r\n      this.setDefaultDateRange()\r\n      // 通知父组件重置按钮选中状态\r\n      this.$emit('reset-button')\r\n      this.handleQuery()\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 处置按钮操作 */\r\n    handleDispose(row) {\r\n      this.disposeForm = {\r\n        id: row.id,\r\n        handleState: row.handleState === 2 ? row.handleState : undefined,\r\n        handleDesc: row.handleState === 2 ? (row.handleDesc || '') : ''\r\n      }\r\n      this.disposeOpen = true\r\n      // 清除表单验证状态\r\n      this.$nextTick(() => {\r\n        if (this.$refs.disposeForm) {\r\n          this.$refs.disposeForm.clearValidate()\r\n        }\r\n      })\r\n    },\r\n    /** 提交处置 */\r\n    submitDispose() {\r\n      this.$refs['disposeForm'].validate(valid => {\r\n        if (valid) {\r\n          handleAlarm({\r\n            id: this.disposeForm.id,\r\n            handleState: this.disposeForm.handleState,\r\n            handleDesc: this.disposeForm.handleDesc\r\n          }).then(response => {\r\n            this.$modal.msgSuccess('处置成功')\r\n            this.disposeOpen = false\r\n            this.getList()\r\n          })\r\n        }\r\n      })\r\n    },\r\n    /** 批量处置按钮操作 */\r\n    handleBatchDispose() {\r\n      if (this.ids.length === 0) {\r\n        this.$modal.msgError('请至少选择一条记录')\r\n        return\r\n      }\r\n      this.batchDisposeForm = {\r\n        eventIds: this.ids,\r\n        handleState: 1,\r\n        handleDesc: ''\r\n      }\r\n      this.batchDisposeOpen = true\r\n    },\r\n    /** 提交批量处置 */\r\n    submitBatchDispose() {\r\n      this.$refs['batchDisposeForm'].validate(valid => {\r\n        if (valid) {\r\n          const eventIds = this.batchDisposeForm.eventIds\r\n          const handleState = this.batchDisposeForm.handleState\r\n          const handleDesc = this.batchDisposeForm.handleDesc\r\n\r\n          batchHandleAlarms({\r\n            eventIds: eventIds,\r\n            handleState: handleState,\r\n            handleDesc: handleDesc\r\n          }).then(response => {\r\n            this.$modal.msgSuccess('批量处置成功')\r\n            this.batchDisposeOpen = false\r\n            this.getList()\r\n          })\r\n        }\r\n      })\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\r\n        'ffsafe/flowRiskAssets/export',\r\n        {\r\n          ...this.queryParams\r\n        },\r\n        'API告警数据.xlsx'\r\n      )\r\n    },\r\n    /** 详情按钮操作 */\r\n    handleDetail(row) {\r\n      this.detailData = { ...row }\r\n      this.detailOpen = true\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      this.$modal.confirm('是否确认删除该API告警记录？').then(() => {\r\n        return this.deleteFlowRiskAssets(row.id)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess('删除成功')\r\n      }).catch(() => {})\r\n    },\r\n    /** 删除API告警记录 */\r\n    deleteFlowRiskAssets(id) {\r\n      return delFlowRiskAssets(id)\r\n    },\r\n    /** 获取风险类别标签 */\r\n    getRiskTypeLabel(riskType) {\r\n      const dict = this.dict.type.flow_risk_type.find(d => d.value === riskType)\r\n      return dict ? dict.label : riskType\r\n    },\r\n    /** 获取处置状态标签 */\r\n    getHandleStateLabel(handleState) {\r\n      const option = this.handleStateOptions.find(item => item.value === handleState)\r\n      return option ? option.label : '未知'\r\n    },\r\n    handleStateFormatter(row, column, cellValue, index) {\r\n      let name = '未处置'\r\n      const match = this.handleStateOptions.find(item => item.value === cellValue)\r\n      if (match) {\r\n        name = match.label\r\n      }\r\n      return name\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\r\n</style>\r\n"]}]}