package com.ruoyi.ffsafe.scantaskapi.domain;

import com.ruoyi.ffsafe.api.domain.FfsafeApiConfig;
import lombok.Data;
import lombok.ToString;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.entity.StringEntity;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

@Data
@ToString
@Component
public class EdrDetailParam extends ParamBase implements RequestBase{
    private String serial;
    private Long deviceConfigId;

    public void parseParam(String serial) {
        this.serial = serial;
    }
    @Override
    public HttpRequestBase getRequestBase(Long deviceId) {
        List<NameValuePair> params = new ArrayList<NameValuePair>();
        /*if (ffurl == null) {
            if (!updateFfsafeApiConfig(deviceId)) {
                return null;
            }
        }*/
        FfsafeApiConfig ffsafeApiConfig = getFfsafeApiConfig();
        String ffurl = ffsafeApiConfig.getUrl();
        String fftoken = ffsafeApiConfig.getToken();

        HttpPost httpPost = new HttpPost(ffurl + "/v2/host_detail_by_serial");
        try {
            params.add(new BasicNameValuePair("access_token", fftoken));
            params.add(new BasicNameValuePair("serial", serial));

            StringEntity entity = new UrlEncodedFormEntity(params, StandardCharsets.UTF_8);
            httpPost.setEntity(entity);
            httpPost.setHeader("Content-type", "application/x-www-form-urlencoded");
        } catch (Exception e) {
            e.printStackTrace();
        }

        return httpPost;
    }
}
