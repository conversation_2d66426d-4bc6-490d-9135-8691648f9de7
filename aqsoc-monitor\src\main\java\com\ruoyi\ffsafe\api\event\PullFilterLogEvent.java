package com.ruoyi.ffsafe.api.event;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ExecutorBuilder;
import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.Threads;
import com.ruoyi.ffsafe.api.domain.*;
import com.ruoyi.ffsafe.api.service.IApiResultSevice;
import com.ruoyi.ffsafe.api.service.IFfsafeInterfaceConfigService;
import com.ruoyi.ffsafe.api.service.ITblDeviceConfigService;
import com.ruoyi.monitor2.changting.client.FfsafeClientService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;

@Slf4j
@Component
public class PullFilterLogEvent extends BaseEvent {

    private boolean bRun;
    private boolean bFirst = true;

    @Autowired
    private FfsafeClientService ffsafeClientService;

    @Autowired
    private IFfsafeInterfaceConfigService ffsafeInterfaceConfigService;
    @Resource
    private ITblDeviceConfigService deviceConfigService;

    private FfsafeInterfaceConfig getIpFilterInterfaceConfig() {
        FfsafeInterfaceConfig ffsafeInterfaceConfig = new FfsafeInterfaceConfig();
        ffsafeInterfaceConfig.setInterfacePath("/v2/flow-bypass-filtering-log");

        List<FfsafeInterfaceConfig> ffsafeInterfaceConfigList = ffsafeInterfaceConfigService.selectFfsafeInterfaceConfigList(ffsafeInterfaceConfig);
        if ((ffsafeInterfaceConfigList != null) && (ffsafeInterfaceConfigList.size() > 0)) {
            return ffsafeInterfaceConfigList.get(0);
        }

        return null;
    }

    private IpFilterParam getIpFilterParam() {
        FfsafeInterfaceConfig ffsafeInterfaceConfig = getIpFilterInterfaceConfig();
        if (ffsafeInterfaceConfig == null)
            return null;

        IpFilterParam ipFilterParam = new IpFilterParam();
        Date lastDataTime = ffsafeInterfaceConfig.getDataLastTime();
        if (lastDataTime == null) {
            lastDataTime = DateUtils.getNowDate();
        }
        ipFilterParam.parseStartTime(lastDataTime);

        return ipFilterParam;
    }


    @PostConstruct
    public void init() {
        log.info("开始获取旁路阻断日志线程。。。");
        bRun = true;
        startEvent();
    }

    private void startEvent() {
        Thread event = new Thread(new Runnable() {
            @Override
            public void run() {
                while (bRun){
                    TblDeviceConfig queryDeviceConfig = new TblDeviceConfig();
                    queryDeviceConfig.setStatus(1);
                    List<TblDeviceConfig> list = deviceConfigService.selectTblDeviceConfigList(queryDeviceConfig);
                    if(CollUtil.isNotEmpty(list)){
                        List<Runnable> tasks = new ArrayList<>();
                        list.forEach(deviceConfig -> {
                            tasks.add(() -> {
                                try {
                                    log.info("开始获取IP阻断日志: {}",deviceConfig.getDeviceName());
                                    if(deviceConfig.getFilterLogLastTime() == null){
                                        deviceConfig.setFilterLogLastTime(DateUtil.date());
                                    }
                                    IpFilterParam ipFilterParam = new IpFilterParam();
                                    ipFilterParam.parseStartTime(deviceConfig.getFilterLogLastTime());
                                    FfsafeApiConfig ffsafeApiConfig = deviceConfigService.getFfsafeApiConfig(deviceConfig);
                                    if(!ffsafeApiConfig.isEnable()){
                                        log.info("ffsafe未启用: {}",deviceConfig);
                                        throw new ServiceException("ffsafe未启用");
                                    }
                                    FfsafeClientService.deviceConfigThreadLocal.set(deviceConfig);
                                    boolean bRet = ffsafeClientService.pullIpFilterLog(ipFilterParam, ffsafeApiConfig);
                                    if (bRet) {
                                        ipFilterParam = ipFilterParam.getNextTimeParam();
                                        //更新最后更新日期
                                        TblDeviceConfig update = new TblDeviceConfig();
                                        update.setId(deviceConfig.getId());
                                        update.setFilterLogLastTime(DateUtil.parse(ipFilterParam.getStartTime()));
                                        deviceConfigService.updateLastTime(update);
                                    }
                                } catch (Exception e) {
                                    log.error("IP阻断日志同步失败: {},config: {}",e.getMessage(),deviceConfig);
                                } finally {
                                    FfsafeClientService.deviceConfigThreadLocal.remove();
                                }
                            });
                        });
                        Threads.batchAsyncExecute(tasks);
                    }
                    ThreadUtil.sleep(10000);
                }
            }
        });
        event.start();
    }
}
