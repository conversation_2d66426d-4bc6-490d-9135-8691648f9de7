package com.ruoyi.ffsafe.scantaskapi.event;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.exception.job.TaskException;
import com.ruoyi.common.utils.Threads;
import com.ruoyi.ffsafe.api.domain.FfsafeApiConfig;
import com.ruoyi.ffsafe.api.domain.FlowDetailParam;
import com.ruoyi.ffsafe.api.domain.TblDeviceConfig;
import com.ruoyi.ffsafe.api.service.ITblDeviceConfigService;
import com.ruoyi.ffsafe.scantaskapi.domain.*;
import com.ruoyi.ffsafe.scantaskapi.service.IFfsafeScantaskSummaryService;
import com.ruoyi.ffsafe.scantaskapi.service.IScanTaskService;
import com.ruoyi.ffsafe.scantaskapi.service.ITaskDetailResultService;
import com.ruoyi.ffsafe.scantaskapi.service.IWebScanService;
import com.ruoyi.monitor2.changting.client.FfsafeClientService;
import com.ruoyi.quartz.domain.SysJob;
import com.ruoyi.quartz.service.ISysJobService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
@Data
@Component
public class ScanResultMonitorEvent {
    public static final int SCHEDULING = 1;
    public static final int RUNNING = 2;
    public static final int EXCPTION = 3;
    public static final int FINISH = 4;

    public static final int WEBTASK_SCHEDULING = 0;
    public static final int WEBTASK_RUNNING = 1;
    public static final int WEBTASK_FINISH = 2;
    public static final int WEBTASK_FAIL = 4;
    public static final int WEBTASK_STOP = 5;

    // 最大失败次数限制
    public static final int MAX_FAILURE_COUNT = 3;

    private boolean bRun;
    private List<JSONObject> hostScanTaskList;
    private List<JSONObject> webScanTaskList;

    // 失败计数映射表
    private Map<Integer, Integer> hostScanTaskFailCounts;
    private Map<Integer, Integer> webScanTaskFailCounts;

    @Autowired
    private IScanTaskService scanTaskService;
    @Autowired
    private IWebScanService webScanService;

    @Autowired
    private IFfsafeScantaskSummaryService scantaskSummaryService;
    @Autowired
    private ITaskDetailResultService taskDetailResultService;
    @Autowired
    private TaskParam taskParam;
    @Autowired
    private TaskDetailParam taskDetailParam;
    @Autowired
    private WebTaskParam webtaskParam;
    @Autowired
    private WebTaskDetailParam webtaskDetailParam;
    @Autowired
    private ISysJobService sysJobService;


    public void addHostScanTask(JSONObject params) {
        synchronized (hostScanTaskList) {
            hostScanTaskList.add(params);
            // 初始化失败计数
            hostScanTaskFailCounts.put(params.getInteger("taskId"), 0);
        }
    }

    private void removeHostScanTask(int taskId) {
        synchronized (hostScanTaskList) {
            hostScanTaskList.remove(new Integer(taskId));
            // 清理失败计数
            hostScanTaskFailCounts.remove(taskId);
        }
    }

    /**
     * 增加主机扫描任务失败计数
     * @param taskId 任务ID
     * @return 当前失败次数
     */
    private int incrementHostScanTaskFailCount(int taskId) {
        Integer currentCount = hostScanTaskFailCounts.get(taskId);
        if (currentCount == null) {
            currentCount = 0;
        }
        currentCount++;
        hostScanTaskFailCounts.put(taskId, currentCount);
        return currentCount;
    }

    /**
     * 重置主机扫描任务失败计数
     * @param taskId 任务ID
     */
    private void resetHostScanTaskFailCount(int taskId) {
        hostScanTaskFailCounts.put(taskId, 0);
    }

    /**
     * 检查主机扫描任务是否达到最大失败次数
     * @param taskId 任务ID
     * @return true表示达到最大失败次数
     */
    private boolean isHostScanTaskMaxFailuresReached(int taskId) {
        Integer count = hostScanTaskFailCounts.get(taskId);
        return count != null && count >= MAX_FAILURE_COUNT;
    }

    public List<JSONObject> cloneTaskList() {
        List<JSONObject> tempList = new ArrayList<JSONObject>();
        synchronized (hostScanTaskList) {
            if (hostScanTaskList != null) {
                tempList.addAll(hostScanTaskList);
            }
        }

        return tempList;
    }

    public void addWebScanTask(JSONObject params) {
        synchronized (webScanTaskList) {
            webScanTaskList.add(params);
            // 初始化失败计数
            webScanTaskFailCounts.put(params.getInteger("taskId"), 0);
        }
    }

    private void removeWebScanTask(int taskId) {
        synchronized (webScanTaskList) {
            webScanTaskList.remove(new Integer(taskId));
            // 清理失败计数
            webScanTaskFailCounts.remove(taskId);
        }
    }

    /**
     * 增加Web扫描任务失败计数
     * @param taskId 任务ID
     * @return 当前失败次数
     */
    private int incrementWebScanTaskFailCount(int taskId) {
        Integer currentCount = webScanTaskFailCounts.get(taskId);
        if (currentCount == null) {
            currentCount = 0;
        }
        currentCount++;
        webScanTaskFailCounts.put(taskId, currentCount);
        return currentCount;
    }

    /**
     * 重置Web扫描任务失败计数
     * @param taskId 任务ID
     */
    private void resetWebScanTaskFailCount(int taskId) {
        webScanTaskFailCounts.put(taskId, 0);
    }

    /**
     * 检查Web扫描任务是否达到最大失败次数
     * @param taskId 任务ID
     * @return true表示达到最大失败次数
     */
    private boolean isWebScanTaskMaxFailuresReached(int taskId) {
        Integer count = webScanTaskFailCounts.get(taskId);
        return count != null && count >= MAX_FAILURE_COUNT;
    }

    public List<JSONObject> cloneWebTaskList() {
        List<JSONObject> tempList = new ArrayList<JSONObject>();
        synchronized (webScanTaskList) {
            if (webScanTaskList != null) {
                tempList.addAll(webScanTaskList);
            }
        }

        return tempList;
    }

    public void updateSysJob(SysJob sysJob) throws SchedulerException, TaskException {
        if (sysJob.getCronExpression().equals("* * * * * ?")) {        // 如果是立即扫描把状态设置无效。防止重复调用
            sysJob.setStatus(SysJob.INVALID);
        }
        int row = sysJobService.updateJob(sysJob);
        if (row == 0) {
            log.warn("更新sysjob失败: jobId: " + sysJob.getJobId() + "job status: " + sysJob.getStatus() + "job currentStatus: " + sysJob.getCurrentStatus());
        }
    }

    private void initHostScanTaskList() {
        hostScanTaskList = new ArrayList<JSONObject>();
        hostScanTaskFailCounts = new ConcurrentHashMap<Integer, Integer>();
        FfsafeScantaskSummary scantaskSummary = new FfsafeScantaskSummary();
        scantaskSummary.setTaskType(HostVulnScan.HOST_SCAN);
        List<FfsafeScantaskSummary> ffsafeScantaskSummaryList = scantaskSummaryService.selectFfsafeScantaskSummaryList(scantaskSummary);
        for (FfsafeScantaskSummary summary : ffsafeScantaskSummaryList) {
            int taskStatus = summary.getTaskStatus().intValue();
            if ((taskStatus == SCHEDULING )|| (taskStatus == RUNNING)) {
                if(summary.getTaskId() == 0){
                    continue;
                }
                JSONObject params = new JSONObject();
                params.put("taskId", summary.getTaskId());
                params.put("jobId", summary.getJobId());
                SysJob curJob = sysJobService.selectJobById(summary.getJobId().longValue());
                if(curJob == null){
                    //任务不存在
                    continue;
                }
                params.put("deviceConfigId", curJob.getDeviceConfigId());
                addHostScanTask(params);
            }
        }
    }

    private void initWebScanTaskList() {
        webScanTaskList = new ArrayList<JSONObject>();
        webScanTaskFailCounts = new ConcurrentHashMap<Integer, Integer>();
        FfsafeScantaskSummary scantaskSummary = new FfsafeScantaskSummary();
        scantaskSummary.setTaskType(HostVulnScan.WEB_SCAN);
        List<FfsafeScantaskSummary> ffsafeScantaskSummaryList = scantaskSummaryService.selectFfsafeScantaskSummaryList(scantaskSummary);
        for (FfsafeScantaskSummary summary : ffsafeScantaskSummaryList) {
            int taskStatus = summary.getTaskStatus().intValue();
            if ((taskStatus == WEBTASK_SCHEDULING )|| (taskStatus == WEBTASK_RUNNING)) {
                if(summary.getTaskId() == 0){
                    continue;
                }
                JSONObject params = new JSONObject();
                params.put("taskId", summary.getTaskId());
                params.put("jobId", summary.getJobId());
                SysJob curJob = sysJobService.selectJobById(summary.getJobId().longValue());
                if(curJob == null){
                    //任务不存在
                    continue;
                }
                params.put("deviceConfigId", curJob.getDeviceConfigId());
                addWebScanTask(params);
            }
        }
    }

    @PostConstruct
    public void init() {
        initHostScanTaskList();
        initWebScanTaskList();
        log.info("开始主机扫描结果监控线程!");
        bRun = true;
        startEvent();
    }

    private HostScanTaskSummaryResult getHostScanTaskSummary(int taskId) {
        HostScanTaskSummaryResult taskSummaryResult = null;
        taskParam.parseParam(taskId);
        taskSummaryResult = scanTaskService.getTaskSummary(taskParam);

        return taskSummaryResult;
    }

    private WebScanTaskSummaryResult getWebScanTaskSummary(int taskId) {
        WebScanTaskSummaryResult taskSummaryResult = null;
        webtaskParam.parseParam(taskId);
        taskSummaryResult = webScanService.getWebscanTaskSummary(webtaskParam);

        return taskSummaryResult;
    }

    private FfsafeScantaskSummary parseTaskSummary(HostScanTaskSummaryResult hostScanTaskSummaryResult) {
        FfsafeScantaskSummary scantaskSummary = null;

        // 检查参数是否为null，避免空指针异常
        if (hostScanTaskSummaryResult == null) {
            log.warn("parseTaskSummary 参数为null，无法解析任务摘要");
            return null;
        }

        List<HostScanTaskSummaryResult.TaskSummary> taskSummaryList = hostScanTaskSummaryResult.getTaskSummaryList();
        if (taskSummaryList != null && taskSummaryList.size() > 0) {
            HostScanTaskSummaryResult.TaskSummary taskSummary = taskSummaryList.get(0);
            scantaskSummary = new FfsafeScantaskSummary();
            scantaskSummary.setTaskType(HostVulnScan.HOST_SCAN);
            scantaskSummary.setTaskId(taskSummary.getTaskId());
            scantaskSummary.setTaskStatus(taskSummary.getStatus());
            scantaskSummary.setFinishRate(taskSummary.getFinishRate());
            scantaskSummary.setHighRiskNum(taskSummary.getHighRisk());
            scantaskSummary.setMiddleRiskNum(taskSummary.getMiddleRisk());
            scantaskSummary.setLowRiskNum(taskSummary.getLowRisk());
            scantaskSummary.setStartTime(taskSummary.getStartTime());
            scantaskSummary.setEndTime(taskSummary.getEndTime());
        }

        return scantaskSummary;
    }

    /**
     *
     * @param taskId
     * @param hostScanTaskSummaryResult
     * @return  0: 入库失败  1: 入库成功 且任务状态为FINISH或EXCPTION  2： 入库成功 且任务状态为RUNNING 3:  任务状态为 SCHEDULING
     */
    private int dealFfsafeScanTaskSummary(int taskId, HostScanTaskSummaryResult hostScanTaskSummaryResult) throws SchedulerException, TaskException  {
        FfsafeScantaskSummary ffsafeScantaskSummary = parseTaskSummary(hostScanTaskSummaryResult);
        if (ffsafeScantaskSummary == null)
            return 0;

        if ((ffsafeScantaskSummary.getTaskStatus() ==  FINISH)
            ||(ffsafeScantaskSummary.getTaskStatus() == EXCPTION)) {  // 任务完成
            taskDetailParam.setTaskId(ffsafeScantaskSummary.getTaskId());
            taskDetailParam.setNeedEvidence(1);
            taskDetailParam.setNeedCpe(0);
            HostScanTaskDetailResult taskDetailResult = scanTaskService.getTaskDetail(taskDetailParam);

            SysJob sysJob = getSysJogByTaskId(taskId, HostVulnScan.HOST_SCAN);
            if (sysJob == null) {
                return 0;
            }

            // 检查taskDetailResult是否为null，避免空指针异常,如果任务状态为FINISH，则直接入库
            if (taskDetailResult == null) {
                log.warn("获取主机扫描任务详情失败，taskId: " + taskId + "，无法进行详情入库");
                if(ffsafeScantaskSummary.getTaskStatus() == FINISH){
                    sysJob.setCurrentStatus(SysJob.PROCESS_FINISHED);
                    updateSysJob(sysJob);
                    taskDetailResultService.dealTaskSummaryResult(hostScanTaskSummaryResult.toFfsafeScantaskSummary());
                    return 1;
                }
                return 0;
            }

            boolean bRet = taskDetailResultService.dealTaskDetailResult(taskId, hostScanTaskSummaryResult, taskDetailResult, sysJob);
            if (bRet) {
                sysJob.setCurrentStatus(SysJob.PROCESS_FINISHED);
                updateSysJob(sysJob);
            }
            return bRet? 1:0;
        }
        if (ffsafeScantaskSummary.getTaskStatus() == RUNNING) {
            boolean bRet = taskDetailResultService.dealTaskSummaryResult(hostScanTaskSummaryResult.toFfsafeScantaskSummary());
            return bRet? 2:0;
        }
        return 3;
    }

    private SysJob getSysJogByTaskId(int taskId, int taskType) {

        FfsafeScantaskSummary ffsafeScantaskSummary = new FfsafeScantaskSummary();
        ffsafeScantaskSummary.setTaskId(taskId);
        ffsafeScantaskSummary.setTaskType(taskType);
        List<FfsafeScantaskSummary> ffsafeScantaskSummaryList = scantaskSummaryService.selectFfsafeScantaskSummaryList(ffsafeScantaskSummary);
        if ((ffsafeScantaskSummaryList == null) || (ffsafeScantaskSummaryList.size() == 0)) {
            return null;
        }

        int jobId = ffsafeScantaskSummaryList.get(0).getJobId();
        SysJob sysJob = sysJobService.selectJobById(Long.valueOf(jobId));
        return sysJob;
    }

    /**
     *
     * @param taskId
     * @param webScanTaskSummaryResult
     * @return 0: 入库失败  1: 入库成功 且任务状态为FINISH或EXCPTION  2： 入库成功 且任务状态为RUNNING 3:  任务状态为 SCHEDULING
     */
    private int dealFfsafeWebscanTaskSummary(int taskId, WebScanTaskSummaryResult webScanTaskSummaryResult) throws SchedulerException, TaskException {
        // 检查参数是否为null，避免空指针异常
        if (webScanTaskSummaryResult == null) {
            log.warn("dealFfsafeWebscanTaskSummary 参数为null，taskId: " + taskId);
            return 0;
        }

        if (webScanTaskSummaryResult.getTaskSummaryList() != null && webScanTaskSummaryResult.getTaskSummaryList().size() > 0) {
            WebScanTaskSummaryResult.TaskSummary taskSummary = webScanTaskSummaryResult.getTaskSummaryList().get(0);
            if ((taskSummary.getStatus() == WEBTASK_FINISH)
                ||(taskSummary.getStatus() == WEBTASK_FAIL)
                ||(taskSummary.getStatus() == WEBTASK_STOP)) { // 任务完成
                webtaskDetailParam.setTaskId(taskId);
                webtaskDetailParam.setNeedEvidence(1);
                List<WebscanTaskDetailResult> webscanTaskDetailResultList = webScanService.getWebTaskDetail(webtaskDetailParam);
                if (webscanTaskDetailResultList == null) {
                    return 0;
                }
                SysJob sysJob = getSysJogByTaskId(taskId, HostVulnScan.WEB_SCAN);
                if (sysJob == null) {
                    return 0;
                }
                boolean bRet = taskDetailResultService.dealWebscanTaskDetailResult(taskId, webScanTaskSummaryResult, webscanTaskDetailResultList, sysJob);
                if (bRet) {
                    sysJob.setCurrentStatus(SysJob.PROCESS_FINISHED);
                    updateSysJob(sysJob);
                }
                return bRet? 1:0;
            }

            if (taskSummary.getStatus() == WEBTASK_RUNNING) {
                boolean bRet = taskDetailResultService.dealTaskSummaryResult(webScanTaskSummaryResult.toFfsafeScantaskSummary());
                return bRet? 2:0;
            }
        }

        return 3;
    }

    private void startEvent() {
        Thread event = new Thread(new Runnable() {
            @Override
            public void run() {
                while (bRun){
                    List<JSONObject> taskList = cloneTaskList();
                    ITblDeviceConfigService deviceConfigService = SpringUtil.getBean(ITblDeviceConfigService.class);
                    List<Runnable> tasks = new ArrayList<>();
                    for (JSONObject taskInfo : taskList) {
                        tasks.add(() -> {
                            try {
                                log.info("开始主机扫描摘要任务: {}",taskInfo);
                                TblDeviceConfig deviceConfig = deviceConfigService.selectDeviceConfigOrDefault(taskInfo.getLong("deviceConfigId"));
                                FfsafeClientService.deviceConfigThreadLocal.set(deviceConfig);
                                HostScanTaskSummaryResult hostScanTaskSummaryResult = getHostScanTaskSummary(taskInfo.getInteger("taskId"));
                                Integer taskId = taskInfo.getInteger("taskId");
                                if (hostScanTaskSummaryResult != null) {
                                    int nRet = dealFfsafeScanTaskSummary(taskId, hostScanTaskSummaryResult);
                                    if (nRet == 1) {
                                        // 处理成功，移除任务并重置失败计数
                                        removeHostScanTask(taskId);
                                        log.info("主机扫描任务 {} 处理完成，已移除", taskId);
                                    } else if (nRet == 0) {
                                        // 处理失败，增加失败计数
                                        int failCount = incrementHostScanTaskFailCount(taskId);
                                        log.warn("处理非凡主机扫描任务结果失败！taskId: {}, 失败次数: {}", taskId, failCount);

                                        // 检查是否达到最大失败次数
                                        if (isHostScanTaskMaxFailuresReached(taskId)) {
                                            log.warn("主机扫描任务 {} 连续失败 {} 次，已自动移除", taskId, MAX_FAILURE_COUNT);
                                            removeHostScanTask(taskId);
                                        }
                                    } else {
                                        // nRet == 2 或 3，处理成功但任务继续，重置失败计数
                                        resetHostScanTaskFailCount(taskId);
                                    }
                                } else {
                                    // hostScanTaskSummaryResult为null，增加失败计数
                                    int failCount = incrementHostScanTaskFailCount(taskId);
                                    log.warn("获取主机扫描任务 {} 摘要信息失败，失败次数: {}", taskId, failCount);

                                    // 检查是否达到最大失败次数
                                    if (isHostScanTaskMaxFailuresReached(taskId)) {
                                        log.warn("主机扫描任务 {} 连续失败 {} 次，已自动移除", taskId, MAX_FAILURE_COUNT);
                                        removeHostScanTask(taskId);
                                    }
                                }
                                log.info("结束主机扫描摘要任务: {}",taskInfo);
                                ThreadUtil.sleep(10000L);
                            }catch (Exception e){
                                e.printStackTrace();
                                log.error("HostScanResultMonitorEvent 线程出错:" + e.getMessage());
                            }
                        });
                    }

                    List<JSONObject> webtaskList = cloneWebTaskList();
                    for (JSONObject taskInfo: webtaskList) {
                        tasks.add(() -> {
                            try {
                                log.info("开始Web扫描摘要任务: {}",taskInfo);
                                TblDeviceConfig deviceConfig = deviceConfigService.selectDeviceConfigOrDefault(taskInfo.getLong("deviceConfigId"));
                                FfsafeClientService.deviceConfigThreadLocal.set(deviceConfig);
                                Integer webtaskId = taskInfo.getInteger("taskId");
                                WebScanTaskSummaryResult webScanTaskSummaryResult = getWebScanTaskSummary(webtaskId);
                                if (webScanTaskSummaryResult != null) {
                                    int nRet = dealFfsafeWebscanTaskSummary(webtaskId, webScanTaskSummaryResult);
                                    if (nRet == 1) {
                                        // 处理成功，移除任务并重置失败计数
                                        removeWebScanTask(webtaskId);
                                        log.info("Web扫描任务 {} 处理完成，已移除", webtaskId);
                                    } else if (nRet == 0) {
                                        // 处理失败，增加失败计数
                                        int failCount = incrementWebScanTaskFailCount(webtaskId);
                                        log.warn("处理非凡web扫描任务结果失败！taskId: {}, 失败次数: {}", webtaskId, failCount);

                                        // 检查是否达到最大失败次数
                                        if (isWebScanTaskMaxFailuresReached(webtaskId)) {
                                            log.warn("Web扫描任务 {} 连续失败 {} 次，已自动移除", webtaskId, MAX_FAILURE_COUNT);
                                            removeWebScanTask(webtaskId);
                                        }
                                    } else {
                                        // nRet == 2 或 3，处理成功但任务继续，重置失败计数
                                        resetWebScanTaskFailCount(webtaskId);
                                    }
                                } else {
                                    // webScanTaskSummaryResult为null，增加失败计数
                                    int failCount = incrementWebScanTaskFailCount(webtaskId);
                                    log.warn("获取Web扫描任务 {} 摘要信息失败，失败次数: {}", webtaskId, failCount);

                                    // 检查是否达到最大失败次数
                                    if (isWebScanTaskMaxFailuresReached(webtaskId)) {
                                        log.warn("Web扫描任务 {} 连续失败 {} 次，已自动移除", webtaskId, MAX_FAILURE_COUNT);
                                        removeWebScanTask(webtaskId);
                                    }
                                }
                                log.info("结束Web扫描摘要任务: {}",taskInfo);
                                ThreadUtil.sleep(10000L);
                            }catch (Exception e){
                                e.printStackTrace();
                                log.error("HostScanResultMonitorEvent 线程出错:" + e.getMessage());
                            }
                        });
                    }

                    Threads.batchAsyncExecute(tasks);
                }
            }
        });
        event.start();
    }
}
