{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\eventList.vue?vue&type=template&id=9f5cded6&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\eventList.vue", "mtime": 1755584508953}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751956544249}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}