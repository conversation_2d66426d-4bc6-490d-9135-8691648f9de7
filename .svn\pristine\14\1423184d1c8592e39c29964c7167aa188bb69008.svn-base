package com.ruoyi.ffsafe.scantaskapi.domain;

import com.ruoyi.ffsafe.api.domain.FfsafeApiConfig;
import lombok.Data;
import lombok.ToString;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpRequestBase;
import org.springframework.stereotype.Component;

@Data
@ToString
@Component
public class WebTaskParam extends ParamBase implements RequestBase{
    private int taskId;
    private String userId;
    private Long deviceConfigId;

    public void parseParam(int taskId) {
        this.taskId = taskId;
        userId = "1";
    }

    @Override
    public HttpRequestBase getRequestBase(Long deviceId) {
        /*if (ffurl == null) {
            if (!updateFfsafeApiConfig(deviceId)) {
                return null;
            }
        }*/
        FfsafeApiConfig ffsafeApiConfig = getFfsafeApiConfig();
        String ffurl = ffsafeApiConfig.getUrl();
        String fftoken = ffsafeApiConfig.getToken();


        String fullUrl = ffurl + "/v1/webscan/" + userId + "?access_token=" + fftoken + "&task_id=" + taskId;

        HttpGet httpGet = new HttpGet(fullUrl);
        return httpGet;
    }
}
