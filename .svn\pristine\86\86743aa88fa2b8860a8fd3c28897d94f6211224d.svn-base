package com.ruoyi.ffsafe.api.event;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.Threads;
import com.ruoyi.ffsafe.api.domain.*;
import com.ruoyi.ffsafe.api.service.IFfsafeInterfaceConfigService;
import com.ruoyi.ffsafe.api.service.ITblDeviceConfigService;
import com.ruoyi.monitor2.changting.client.FfsafeClientService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 流量风险资产拉取事件
 */
@Slf4j
@Component
public class PullFlowRiskAssetsEvent extends BaseEvent {
    private boolean bRun;
    private boolean bFirst = true;
    private FfsafeInterfaceConfig interfaceConfig;

    @Autowired
    private FfsafeClientService ffsafeClientService;

    @Autowired
    private IFfsafeInterfaceConfigService ffsafeInterfaceConfigService;
    @Resource
    private ITblDeviceConfigService deviceConfigService;

    /**
     * 获取流量风险资产接口配置
     */
    private FfsafeInterfaceConfig getFlowRiskAssetsInterfaceConfig() {
        FfsafeInterfaceConfig ffsafeInterfaceConfig = new FfsafeInterfaceConfig();
        ffsafeInterfaceConfig.setInterfacePath("/v2/flow-risk-assets");

        List<FfsafeInterfaceConfig> ffsafeInterfaceConfigList = ffsafeInterfaceConfigService.selectFfsafeInterfaceConfigList(ffsafeInterfaceConfig);
        if (ffsafeInterfaceConfigList != null && !ffsafeInterfaceConfigList.isEmpty()) {
            return ffsafeInterfaceConfigList.get(0);
        }

        return null;
    }

    /**
     * 获取流量风险资产参数
     */
    private FlowRiskAssetsParam getFlowRiskAssetsParam(FfsafeInterfaceConfig ffsafeInterfaceConfig) {
        FlowRiskAssetsParam param = new FlowRiskAssetsParam();
        Date lastDataTime = ffsafeInterfaceConfig.getDataLastTime();
        if (lastDataTime == null) {
            // 默认拉取7天前的数据
            lastDataTime = DateUtils.addDays(DateUtils.getNowDate(), -7);
        }

        // 设置开始时间为上次同步时间，往前回退1天防止数据遗漏
        Date startTime = DateUtils.addDays(lastDataTime, -1);
        param.setStartTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, startTime));

        // 设置结束时间为当前时间前2分钟，避免获取正在生成的数据
        Date endTime = DateUtils.addMinutes(DateUtils.getNowDate(), -2);
        param.setEndTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, endTime));

        param.setRiskType("all");

        return param;
    }

    @PostConstruct
    public void init() {
        log.info("开始获取流量风险资产数据线程...");
        bRun = true;
        startEvent();
    }

    protected void startEvent() {
        Thread event = new Thread(new Runnable() {
            @Override
            public void run() {
                while (bRun){
                    TblDeviceConfig queryDeviceConfig = new TblDeviceConfig();
                    queryDeviceConfig.setStatus(1);
                    List<TblDeviceConfig> list = deviceConfigService.selectTblDeviceConfigList(queryDeviceConfig);
                    if(CollUtil.isNotEmpty(list)){
                        List<Runnable> tasks = new ArrayList<>();
                        list.forEach(deviceConfig -> {
                            tasks.add(() -> {
                                try {
                                    log.info("开始获取流量风险资产数据: {}",deviceConfig.getDeviceName());
                                    if(deviceConfig.getRiskAssetLastTime() == null){
                                        deviceConfig.setRiskAssetLastTime(DateUtil.date());
                                    }
                                    // 获取参数并调用接口
                                    FfsafeApiConfig ffsafeApiConfig = deviceConfigService.getFfsafeApiConfig(deviceConfig);
                                    if(!ffsafeApiConfig.isEnable()){
                                        log.info("ffsafe未启用: {}",deviceConfig);
                                        throw new ServiceException("ffsafe未启用");
                                    }

                                    FfsafeInterfaceConfig ffsafeInterfaceConfig = new FfsafeInterfaceConfig();
                                    ffsafeInterfaceConfig.setDataLastTime(deviceConfig.getRiskAssetLastTime());
                                    FfsafeClientService.deviceConfigThreadLocal.set(deviceConfig);
                                    FlowRiskAssetsParam param = getFlowRiskAssetsParam(ffsafeInterfaceConfig);
                                    param.setDeviceConfigId(deviceConfig.getId());
                                    boolean bRet = ffsafeClientService.pullFlowRiskAssets(param, ffsafeApiConfig);
                                    if (bRet) {
                                        //更新最后更新日期
                                        TblDeviceConfig update = new TblDeviceConfig();
                                        update.setId(deviceConfig.getId());
                                        update.setRiskAssetLastTime(DateUtil.parse(param.getEndTime()));
                                        deviceConfigService.updateLastTime(update);
                                    }
                                } catch (Exception e) {
                                    log.error("流量风险资产数据同步失败: {},config: {}",e.getMessage(),deviceConfig);
                                } finally {
                                    FfsafeClientService.deviceConfigThreadLocal.remove();
                                }
                            });
                        });
                        Threads.batchAsyncExecute(tasks);
                    }
                    ThreadUtil.sleep(10000);
                }
            }
        });
        event.start();
    }
}
