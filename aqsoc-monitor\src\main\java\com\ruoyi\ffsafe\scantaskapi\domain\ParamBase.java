package com.ruoyi.ffsafe.scantaskapi.domain;
import cn.hutool.extra.spring.SpringUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.ffsafe.api.domain.FfsafeApiConfig;
import com.ruoyi.ffsafe.api.domain.TblDeviceConfig;
import com.ruoyi.ffsafe.api.service.ITblDeviceConfigService;
import com.ruoyi.monitor2.changting.client.FfsafeClientService;
import com.ruoyi.system.event.SysConfigMonitor;
import lombok.extern.slf4j.Slf4j;
import javax.annotation.PostConstruct;


@Slf4j
public class ParamBase {

    /*protected static String fftoken;
    protected static String ffurl;*/

    private FfsafeApiConfig getFfsafeApiConfig(Long deviceId) {
        String apiConfig = SysConfigMonitor.getConfigValue("ffsafe.api2");
        if (apiConfig == null)
            return null;

        FfsafeApiConfig ffsafeApiConfig = null;
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            ffsafeApiConfig = objectMapper.readValue(apiConfig, FfsafeApiConfig.class);
            if (!ffsafeApiConfig.isEnable())
                return null;
            return ffsafeApiConfig;
        } catch (Exception e) {
            log.error("getXrayApiConfig 错误: " + e.getMessage());
        }
        return null;
    }

    protected boolean updateFfsafeApiConfig(Long deviceId) {
        ITblDeviceConfigService deviceConfigService = SpringUtil.getBean(ITblDeviceConfigService.class);
        TblDeviceConfig deviceConfig = deviceConfigService.selectTblDeviceConfigById(deviceId);
        if (deviceConfig == null)
            return false;
        FfsafeApiConfig ffsafeApiConfig = deviceConfigService.getFfsafeApiConfig(deviceConfig);
        if (ffsafeApiConfig != null) {
            //ffurl = ffsafeApiConfig.getUrl();
            //fftoken = ffsafeApiConfig.getToken();
            return true;
        }
        return false;
    }

    protected FfsafeApiConfig getFfsafeApiConfig() {
        TblDeviceConfig deviceConfig = FfsafeClientService.deviceConfigThreadLocal.get();
        ITblDeviceConfigService deviceConfigService = SpringUtil.getBean(ITblDeviceConfigService.class);
        if(deviceConfig == null){
            //获取默认配置
            deviceConfig = deviceConfigService.selectDeviceConfigOrDefault(null);
        }
        return deviceConfigService.getFfsafeApiConfig(deviceConfig);
    }
}
