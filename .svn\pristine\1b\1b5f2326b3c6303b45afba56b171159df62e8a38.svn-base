package com.ruoyi.ffsafe.scantaskapi.domain;

import com.ruoyi.ffsafe.api.domain.FfsafeApiConfig;
import lombok.Data;
import lombok.ToString;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.entity.StringEntity;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

@Data
@ToString
@Component
public class DeleteTaskParam extends ParamBase implements RequestBase{
    private int taskId;
    private String userId;
    private Long deviceConfigId;

    @Override
    public HttpRequestBase getRequestBase(Long deviceId) {
        /*if (ffurl == null) {
            if (!updateFfsafeApiConfig(deviceId)) {
                return null;
            }
        }*/
        FfsafeApiConfig ffsafeApiConfig = this.getFfsafeApiConfig();
        String ffurl = ffsafeApiConfig.getUrl();
        String fftoken = ffsafeApiConfig.getToken();
        userId = "1";

        List<NameValuePair> params = new ArrayList<NameValuePair>();
        HttpDeleteWithBody httpDelete = new HttpDeleteWithBody(ffurl + "/v1/" + taskId);

        params.add(new BasicNameValuePair("access_token", fftoken));
        params.add(new BasicNameValuePair("user_id", userId));

        StringEntity entity = new UrlEncodedFormEntity(params, StandardCharsets.UTF_8);
        httpDelete.setEntity(entity);
        httpDelete.setHeader("Content-type", "application/x-www-form-urlencoded");


        return httpDelete;
    }
}
