-- =====================================================
-- FfsafeScanReportRecord.scanTarget字段优化数据库修改脚本
-- 方案一：完全移除scanTarget字段
-- =====================================================

USE aqsoc;

-- 1. 迁移前检查 - 记录当前状态
SELECT 
    '迁移前检查' as step,
    '当前记录数' as item,
    COUNT(*) as count
FROM ffsafe_scan_report_record;

-- 2. 检查关联关系完整性
SELECT 
    '关联关系检查' as step,
    '有关联关系的报告记录数' as item,
    COUNT(DISTINCT r.id) as count
FROM ffsafe_scan_report_record r
INNER JOIN ffsafe_scan_report_task_relation rel ON r.id = rel.scan_report_record_id;

-- 3. 检查scanTarget数据来源
SELECT 
    '数据来源检查' as step,
    '有job关联的记录数' as item,
    COUNT(DISTINCT r.id) as count
FROM ffsafe_scan_report_record r
INNER JOIN ffsafe_scan_report_task_relation rel ON r.id = rel.scan_report_record_id
INNER JOIN ffsafe_scantask_summary s ON rel.task_summary_id = s.id
INNER JOIN sys_job j ON s.job_id = j.job_id;

-- 4. 备份现有数据（可选）
-- CREATE TABLE ffsafe_scan_report_record_backup_YYYYMMDD AS
-- SELECT * FROM ffsafe_scan_report_record;

-- 5. 执行字段删除
-- 注意：请先在测试环境验证后再在生产环境执行
ALTER TABLE ffsafe_scan_report_record DROP COLUMN scan_target;

-- 6. 迁移后验证
SELECT 
    '迁移后检查' as step,
    '记录数' as item,
    COUNT(*) as count
FROM ffsafe_scan_report_record;

-- 7. 验证表结构
DESCRIBE ffsafe_scan_report_record;

-- 8. 验证查询是否正常工作
SELECT 
    r.id,
    CASE
        WHEN j.invoke_target LIKE '%|%' THEN
            SUBSTRING_INDEX(SUBSTRING_INDEX(j.invoke_target, '|', 2), '|', -1)
        ELSE
            j.invoke_target
    END as scan_target,
    r.create_time,
    r.report_type
FROM ffsafe_scan_report_record r
LEFT JOIN ffsafe_scan_report_task_relation rel ON r.id = rel.scan_report_record_id
LEFT JOIN ffsafe_scantask_summary s ON rel.task_summary_id = s.id
LEFT JOIN sys_job j ON s.job_id = j.job_id
LIMIT 5;

-- 9. 检查索引是否需要优化
-- 如果发现查询性能问题，可以考虑在以下字段上添加索引：
-- ALTER TABLE ffsafe_scan_report_task_relation ADD INDEX idx_scan_report_record_id (scan_report_record_id);
-- ALTER TABLE ffsafe_scantask_summary ADD INDEX idx_job_id (job_id);
-- ALTER TABLE sys_job ADD INDEX idx_invoke_target (invoke_target(255));

-- =====================================================
-- 迁移完成标记
-- =====================================================
SELECT 'scanTarget字段移除完成' as migration_status;