<template>
  <div class="custom-container">
    <div class="custom-content-container-right">
      <div class="div-main-top">
        <div class="div-main-top-one">
          <div class="main-top-one-left"><img class="img-style" src="../../../assets/images/fengxianzongshu.png"></div>
          <div class="main-top-one-right">
            <div class="main-top-one-right-top">{{ totalNumberOfRisks }}</div>
            <div class="main-top-one-right-bottom">风险总数</div>
          </div>
        </div>
        <div class="div-main-top-two" :style="{borderColor: borderNum === 1 ? '#637fef' : '#fff'}" tabindex="0" role="button" id="autoFocusBox" @click="toLoophole()">
          <div class="main-top-two-three-four">
            <div class="top-title-left">主机风险</div>
            <div class="top-title-right">{{hostRisk.hostRiskNum}}</div>
          </div>
          <div class="main-top-two-bottom">
            <div :class="currentIndex === 'ip'+index ? 'icons-title-count icons-title-count-active':'icons-title-count'" v-for="(item,index) in hostRiskList" @click.stop="toLoopholeByType(item.severity,index)">
              <div class="icons-title-count-img"><img class="title-count-img-style" :src="item.img"></div>
              <div class="icons-title-count-right">
                <div class="icons-title-count-top">{{ item.num }}</div>
                <div class="icons-title-count-bottom">{{ item.title }}</div>
              </div>
            </div>
            <div :class="currentIndex == 'ip'+'4' ? 'icons-title-count icons-title-count-active':'icons-title-count'" @click.stop="toWeakPassword()">
              <div class="icons-title-count-img"><img class="title-count-img-style" src="../../../assets/images/ruokoling.png"></div>
              <div class="icons-title-count-right">
                <div class="icons-title-count-top">{{ hostRisk.weakPasswordsNum }}</div>
                <div class="icons-title-count-bottom">弱口令</div>
              </div>
            </div>
          </div>
        </div>
        <div class="div-main-top-three" :style="{borderColor: borderNum === 2 ? '#637fef' : '#fff'}" tabindex="0" @click="toWebvulnPage()">
          <div class="main-top-two-three-four">
            <div class="top-title-left">Web风险</div>
            <div class="top-title-right">{{ webRisk.webRiskNum }}</div>
          </div>
          <div class="main-top-three-bottom">
            <div :class="currentIndex === 'web'+index ? 'top-three-bottom-body icons-title-count icons-title-count-active' : 'top-three-bottom-body icons-title-count'" v-for="(item,index) in webRiskList" @click.stop="toWebvulnByType(item.severity,index)">
              <div class="icons-title-count-img"><img class="title-count-img-style" :src="item.img"></div>
              <div class="icons-title-count-right">
                <div class="icons-title-count-top">{{ item.num }}</div>
                <div class="icons-title-count-bottom">{{ item.title }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="div-main-top-four" :style="{borderColor: borderNum === 3 ? '#637fef' : '#fff'}" tabindex="0" @click="toMonitorIpPage(4)">
          <div class="main-top-two-three-four">
            <div class="top-title-left">漏扫任务</div>
            <div class="top-title-right">{{ vulnerabilityScanning.vulnerabilityScanningNum }}</div>
          </div>
          <div class="main-top-four-bottom">
            <div :class="currentIndex === 'monitorIp'+4 ? 'top-four-bottom-body icons-title-count icons-title-count-active' : 'top-four-bottom-body icons-title-count'" @click.stop="toMonitorIpPage(4)">
              <div class="icons-title-count-img"><img class="title-count-img-style" src="../../../assets/images/zhuji.png"></div>
              <div class="icons-title-count-right">
                <div class="icons-title-count-top">{{ vulnerabilityScanning.hostScanningNum }}</div>
                <div class="icons-title-count-bottom">主机漏扫</div>
              </div>
            </div>
            <div :class="currentIndex === 'monitorWeb'+5 ? 'top-four-bottom-body icons-title-count icons-title-count-active' : 'top-four-bottom-body icons-title-count'" @click.stop="toMonitorWeb(5)">
              <div class="icons-title-count-img"><img class="title-count-img-style" src="../../../assets/images/weblousao.png"></div>
              <div class="icons-title-count-right">
                <div class="icons-title-count-top">{{ vulnerabilityScanning.webVulnerabilityScanningNum }}</div>
                <div class="icons-title-count-bottom">Web漏扫</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <el-tabs v-if="listType === 4 || listType === 5" v-model="activeName" class="tabs-style">
        <el-tab-pane :label="listType === 4 ? '主机漏扫任务' : 'Web漏扫任务'" name="task" />
        <el-tab-pane :label="listType === 4 ? '主机漏扫记录' : 'Web漏扫记录'" name="record" />
      </el-tabs>
      <div :class="(listType === 4 || listType === 5) ? 'div-main-container-tabs' : 'div-main-container'">
        <index v-if="listType === 1" :severity="loopholeSeverity" :toParams="toParams"/>
        <webvuln v-if="listType === 2" :severity="webvulnSeverity" :toParams="toParams"/>
        <Index2 v-if="listType  === 3" :toParams="toParams"/>
        <Job v-if="listType === 4 && activeName === 'task'" :toParams="toParams"/>
        <MonitorWeb v-if="listType === 5 && activeName === 'task'" :toParams="toParams"/>
        <LeakyRecord
          v-if="(listType === 4 || listType === 5) && activeName === 'record'"
          :list-type="listType"
          :toParams="toParams"/>
      </div>
    </div>
  </div>
</template>

<script>
import CreateWork from "../../todoItem/todo/createWork";
import newAddloophole from "@/views/frailty/loophole/newAddloophole.vue";
import FlowBox from '@/views/zeroCode/workFlow/components/FlowBox.vue'
import FlowTemplateSelect from "@/components/FlowTemplateSelect/index.vue";
import CronInput from '@/components/CronInput/index.vue'
import LeakScanDialog from "@/views/safe/server/components/LeakScanDialog.vue";
import DeptSelect from '@/views/components/select/deptSelect.vue'
import { getVulnerabilityRiskHeadCount } from "@/api/threat/threat";
import Webvuln from "@/views/frailty/webvuln/index.vue";
import Index from "@/views/frailty/loophole/index.vue";
import Index2 from "@/views/frailty/weakPassword/index.vue";
import Job from "@/views/frailty/monitor/monitorIp.vue";
import MonitorWeb from "@/views/frailty/monitor/monitorWeb.vue"
import LeakyRecord from "@/views/frailty/monitor/leakyRecord.vue"
export default {
  components: {
    Index,
    Index2,
    MonitorWeb,
    Job,
    Webvuln,
    DeptSelect,
    LeakScanDialog,
    CronInput,
    CreateWork,
    newAddloophole,
    FlowBox,
    FlowTemplateSelect,
    LeakyRecord,
    SystemList: () => import('../../../components/SystemList')
  },
  dicts: ["loophole_category", "synchronization_status"],
  data() {
    return {
      toParams: {},
      webvulnSeverity:null,
      loopholeSeverity:null,
      listType:1,
      borderNum: 1, // 保持选中固定值
      hostRisk:{}, // 主机风险对象
      webRisk:{}, // web风险对象
      vulnerabilityScanning:{}, // 漏洞扫描对象
      totalNumberOfRisks:0, // 风险总数
      hostRiskList: [
        {
          severity:4,
          title:"可入侵漏洞",
          img:require('@/assets/images/keruqin.png'),
          num:0
        },
        {
          severity:3,
          title:"高危漏洞",
          img: require('@/assets/images/gaowei.png'),
          num:0
        },
        {
          severity:2,
          title:"中危漏洞",
          img: require('@/assets/images/zhongwei.png'),
          num:0
        },
        {
          severity:1,
          title:"低危漏洞",
          img: require('@/assets/images/diwei.png'),
          num:0
        },
      ],
      webRiskList: [
        {
          severity:4,
          title:"严重漏洞",
          img:require('@/assets/images/keruqin.png'),
          num:0
        },
        {
          severity:3,
          title:"高危漏洞",
          img: require('@/assets/images/gaowei.png'),
          num:0
        },
        {
          severity:2,
          title:"中危漏洞",
          img: require('@/assets/images/zhongwei.png'),
          num:0
        },
        {
          severity:1,
          title:"低危漏洞",
          img: require('@/assets/images/diwei.png'),
          num:0
        },
      ],
      activeName: 'task',
      userList: [],
      currentIndex: ''
    }
  },
  mounted() {
    this.$nextTick(() => {
      if (!this.$route.query.type) {
        let el = document.getElementById('autoFocusBox');
        el.focus();
      }
    });
  },
  created() {
    this.initData();
  },
  watch: {
    $route: {
      handler(newVal, oldVal) {
        // 监听所有路由变化
        if (newVal.query.referenceId) {
          // 扣分详情跳转判断
          if (newVal.query.type === '1') {
            this.toLoophole();
            this.toParams = {
              referenceId: newVal.query.referenceId
            }
          }
          if (newVal.query.type === '2') {
            this.toWebvulnPage();
            this.toParams = {
              referenceId: newVal.query.referenceId
            }
          }
          if (newVal.query.type === '3') {
            this.toWeakPassword();
            this.toParams = {
              referenceId: newVal.query.referenceId
            }
          }
          this.$router.replace({});
        }
        if (newVal.query.type === '4') {
          this.toMonitorIpPage(Number(newVal.query.type),{id: newVal.query.id})
          const query = { ...this.$route.query }; // 复制当前查询对象
          delete query.type;                      // 删除目标参数
          delete query.id;                      // 删除目标参数
          this.$router.replace({ query });         // 替换当前路由（URL更新）
        } else if (newVal.query.type === '5') {
          this.toMonitorWeb(Number(newVal.query.type),{
            id: newVal.query.id,
            cronTransfer: newVal.query.cronTransfer,
            invokeTarget: newVal.query.invokeTarget,
            jobName: newVal.query.jobName
          })
          const query = { ...this.$route.query }; // 复制当前查询对象
          delete query.type;                      // 删除目标参数
          delete query.id;                      // 删除目标参数
          this.$router.replace({ query });         // 替换当前路由（URL更新）
        }

        if (newVal.query.type) {
          this.$nextTick(() => {
            // 精确选择当前组件内的目标元素
            const targetElement = this.$el.querySelector('.div-main-top-four[tabindex]')
            if (targetElement) {
              // 先移除其他元素的焦点
              document.activeElement?.blur?.()
              // 添加延迟确保渲染完成
              setTimeout(() => {
                targetElement.focus()
                // 添加自定义聚焦样式
                targetElement.classList.add('force-focus')
              }, 50)
            }
          })
        }
      },
      immediate: true
    }
  },
  methods: {
    toWeakPassword(){
      this.currentIndex = 'ip'+'4';
      this.listType = 3;
      this.borderNum = 1;
      //this.currentIndex = ''
    },
    toLoophole(){
      this.loopholeSeverity = null;
      this.listType = 1;
      this.borderNum = 1;
      this.currentIndex = '';
    },
    toLoopholeByType(type,index){
      this.currentIndex = 'ip'+index;
      this.loopholeSeverity = type;
      this.listType = 1;
      this.borderNum = 1;
    },
    toWebvulnPage(){
      this.listType = 2;
      this.borderNum = 2;
      this.currentIndex = ''
      this.webvulnSeverity = null;
    },
    toWebvulnByType(type,index){
      this.webvulnSeverity = type;
      this.currentIndex = 'web'+index;
      this.listType = 2;
      this.borderNum = 2;
    },
    toMonitorIpPage(index,params){
      this.listType = index;
      this.borderNum = 3;
      this.currentIndex = 'monitorIp'+index;
      this.toParams = params;
      this.activeName = 'task'
    },
    toMonitorWeb(index,params){
      this.listType = index;
      this.borderNum = 3;
      this.currentIndex = 'monitorWeb'+index;
      this.toParams = params;
      this.activeName = 'task'
    },
    initData() {
      getVulnerabilityRiskHeadCount().then(res => {
        if (res.data){
          this.hostRisk = res.data.hostRisk;
          this.webRisk = res.data.webRisk;
          this.vulnerabilityScanning = res.data.vulnerabilityScanning;
          this.totalNumberOfRisks = res.data.totalNumberOfRisks;
          //遍历hostRiskList
          this.hostRiskList.forEach(e => {
           let num = this.hostRisk.ipVulnerabilityLevelNum.filter(e1 => {return e1.severity == e.severity});
           if (num.length == 0){
             e.num = 0
           }else {
             e.num = num[0].num
           }
          })
          this.webRiskList.forEach(e => {
            let num = this.webRisk.webVulnerabilityLevelNum.filter(e1 => {return e1.severity == e.severity});
            if (num.length == 0){
              e.num = 0
            }else {
              e.num = num[0].num
            }
          })
        }
      })
    },
  }
}
</script>
<style lang="scss" scoped>
@import "../../../assets/styles/tabs.scss";

.div-main-container {
  height: calc(100% - 119px)
}

.div-main-container-tabs {
  height: calc(100% - 160px);
  margin-top: 3px;
}

::v-deep.el-select {
  width: 100%;
  .el-select-dropdown {
    position: absolute;
    top: 30px !important;
    left: 5px;
    .el-scrollbar {
      max-height: 300px;
      overflow-y: auto;
    }
  }
}

.loop_dialog {
  height: 90vh;
  overflow: hidden;
  ::v-deep .el-dialog {
    height: 100%;
    .el-dialog__body {
      height: calc(100% - 110px);
      padding: 10px 20px 0;
      overflow: auto;
    }
  }
}

.asset-tag {
  margin-left: 5px;
  max-width: 35%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  vertical-align: middle;
}

.overflow-tag:not(:first-child) {
  margin-top: 5px;
}
</style>
