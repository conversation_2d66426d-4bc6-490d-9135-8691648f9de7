<template>
  <div class="custom-container">
    <div class="custom-content-container-right">
      <div class="custom-content-search-box">
        <el-form
          ref="queryForm"
          :model="queryParams"
          size="small"
          label-position="right"
          label-width="70px"
          :inline="true"
        >
          <el-row :gutter="10">
            <el-col :span="6">
              <el-form-item label="告警时间" prop="beginTime">
                <el-date-picker
                  v-model="dateRange"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  @change="handleDateRangeChange"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="风险资产" prop="riskAssets">
                <el-input
                  v-model="queryParams.riskAssets"
                  placeholder="请输入风险资产"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="风险类别" prop="riskType">
                <el-select v-model="queryParams.riskType" placeholder="请选择风险类别" clearable>
                  <el-option
                    v-for="dict in dict.type.flow_risk_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item class="custom-search-btn">
                <el-button
                  class="btn1"
                  size="small"
                  @click="handleQuery"
                >查询</el-button>
                <el-button
                  class="btn2"
                  size="small"
                  @click="resetQuery"
                >重置</el-button>
                <el-button
                  v-if="!showSearch"
                  class="btn2"
                  size="small"
                  icon="el-icon-arrow-down"
                  @click="showSearch = true"
                >展开</el-button>
                <el-button
                  v-else
                  class="btn2"
                  size="small"
                  icon="el-icon-arrow-up"
                  @click="showSearch = false"
                >收起</el-button>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="showSearch" :gutter="10">
            <el-col :span="6">
              <el-form-item label="处置状态" prop="handleState">
                <el-select v-model="queryParams.handleState" placeholder="请选择处置状态" clearable>
                  <el-option
                    v-for="dict in handleStateOptions"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="所属探针">
                <el-select v-model="queryParams.deviceConfigId" filterable clearable placeholder="请选择">
                  <el-option
                    v-for="item in deviceConfigList"
                    :key="item.id"
                    :label="item.deviceName"
                    :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="custom-content-container">
        <div class="common-header">
          <div><span class="common-head-title">告警列表</span></div>
          <div class="common-head-right">
            <el-row :gutter="10">
              <el-col :span="1.5">
                <el-button
                  v-hasPermi="['ffsafe:flowRiskAssets:export']"
                  class="btn1"
                  size="small"
                  @click="handleExport"
                >导出</el-button>
              </el-col>
            </el-row>
          </div>
        </div>
        <el-table
          v-loading="loading"
          height="100%"
          :data="apiAlarmList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="告警更新时间" align="left" prop="updateTime" width="160" sortable>
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="风险资产" align="left" prop="riskAssets" />
          <el-table-column label="风险类别" align="left" prop="riskType" width="120">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.flow_risk_type" :value="scope.row.riskType" />
            </template>
          </el-table-column>
          <el-table-column label="风险信息" align="left" prop="riskInfo" />
          <el-table-column label="处置状态" align="center" prop="handleState" width="100" :formatter="handleStateFormatter" />
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
            width="200"
          >
            <template slot-scope="scope">
              <!--   <el-button
                v-hasPermi="['ffsafe:flowRiskAssets:edit']"
                size="mini"
                type="text"
                @click="handleDetail(scope.row)"
              >详情</el-button> -->
              <el-button
                v-hasPermi="['ffsafe:flowRiskAssets:remove']"
                size="mini"
                type="text"
                class="JNPF-table-delBtn"
                @click="handleDelete(scope.row)"
              >删除</el-button>
              <el-button
                v-if="scope.row.handleState !== 1"
                v-hasPermi="['ffsafe:flowRiskAssets:handle']"
                size="mini"
                type="text"
                @click="handleDispose(scope.row)"
              >处置</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>

    <!-- 处置对话框 -->
    <el-dialog title="快速处置" :visible.sync="disposeOpen" width="500px" append-to-body>
      <el-form ref="disposeForm" :model="disposeForm" :rules="disposeRules" label-width="80px">
        <el-form-item label="处置状态" prop="handleState">
          <el-select v-model="disposeForm.handleState" placeholder="请选择处置状态" clearable>
            <el-option
              v-for="dict in handleStateOption"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="处置备注" prop="handleDesc">
          <el-input v-model="disposeForm.handleDesc" type="textarea" placeholder="请输入处置备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitDispose">确 定</el-button>
        <el-button @click="disposeOpen = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 批量处置对话框 -->
    <el-dialog title="批量处置" :visible.sync="batchDisposeOpen" width="500px" append-to-body>
      <el-form ref="batchDisposeForm" :model="batchDisposeForm" label-width="80px">
        <el-form-item label="处置状态" prop="handleState">
          <el-select v-model="batchDisposeForm.handleState" placeholder="请选择处置状态">
            <el-option
              v-for="dict in handleStateOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="处置备注" prop="handleDesc">
          <el-input v-model="batchDisposeForm.handleDesc" type="textarea" placeholder="请输入处置备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitBatchDispose">确 定</el-button>
        <el-button @click="batchDisposeOpen = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog title="API告警详情" :visible.sync="detailOpen" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="告警更新时间">
          {{ parseTime(detailData.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
        </el-descriptions-item>
        <el-descriptions-item label="风险资产">
          {{ detailData.riskAssets }}
        </el-descriptions-item>
        <el-descriptions-item label="风险类别">
          {{ getRiskTypeLabel(detailData.riskType) }}
        </el-descriptions-item>
        <el-descriptions-item label="引擎名称">
          {{ detailData.engineName }}
        </el-descriptions-item>
        <el-descriptions-item label="处置状态">
          {{ getHandleStateLabel(detailData.handleState) }}
        </el-descriptions-item>
        <el-descriptions-item label="处置人">
          {{ detailData.disposerName || '' }}
        </el-descriptions-item>
        <el-descriptions-item label="风险信息" :span="2">
          {{ detailData.riskInfo }}
        </el-descriptions-item>
        <el-descriptions-item v-if="detailData.handleDesc" label="处置描述" :span="2">
          {{ detailData.handleDesc }}
        </el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailOpen = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listFlowRiskAssets, handleAlarm, batchHandleAlarms, delFlowRiskAssets } from '@/api/ffsafe/flowRiskAssets'
import {listDeviceConfig} from "@/api/ffsafe/deviceConfig";

export default {
  name: 'ApiAlarmList',
  dicts: ['flow_risk_type'],
  props: {
    propsActiveName: {
      type: String,
      default: ''
    },
    propsQueryParams: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      deviceConfigList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      // 总条数
      total: 0,
      // API告警列表
      apiAlarmList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 是否显示处置弹出层
      disposeOpen: false,
      // 是否显示批量处置弹出层
      batchDisposeOpen: false,
      // 是否显示详情弹出层
      detailOpen: false,
      // 详情数据
      detailData: {},
      // 处置表单
      disposeForm: {
        id: null,
        handleState: undefined,
        handleDesc: ''
      },
      // 处置表单验证规则
      disposeRules: {
        handleState: [
          { required: true, message: '请选择处置状态', trigger: 'change' }
        ]
      },
      // 批量处置表单
      batchDisposeForm: {
        eventIds: [],
        handleState: '',
        handleDesc: ''
      },
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        riskAssets: undefined,
        riskType: undefined,
        handleState: undefined,
        params: {
          beginTime: undefined,
          endTime: undefined
        }
      },
      // 处置状态字典
      handleStateOptions: [
        { value: 0, label: '未处置' },
        { value: 1, label: '已处置' },
        { value: 2, label: '忽略' },
        { value: 3, label: '处置中' }
      ],
      handleStateOption: [
        {
          label: '已处置',
          value: 1
        },
        {
          label: '忽略',
          value: 2
        }
      ]
    }
  },
  watch: {
    propsActiveName: {
      handler(newVal) {
        if (newVal === 'apiAlarm') {
          // 确保时间参数已设置，避免无时间参数的查询
          if (!this.queryParams.params.beginTime || !this.queryParams.params.endTime) {
            this.setDefaultDateRange()
          }
          this.getList()
        }
      },
      immediate: true
    },
    propsQueryParams: {
      handler(newVal) {
        if (newVal) {
          // 保留已设置的时间范围参数，避免被空的params覆盖
          const originalBeginTime = this.queryParams.params.beginTime
          const originalEndTime = this.queryParams.params.endTime

          this.queryParams = { ...this.queryParams, ...newVal }

          // 如果新的查询参数没有时间范围，则恢复原有的时间范围
          if (!newVal.params || (!newVal.params.beginTime && !newVal.params.endTime)) {
            if (originalBeginTime && originalEndTime) {
              this.queryParams.params.beginTime = originalBeginTime
              this.queryParams.params.endTime = originalEndTime
            }
          }

          // 只有当前标签是apiAlarm时才触发查询，避免重复查询
          if (this.propsActiveName === 'apiAlarm') {
            this.getList()
          }
        }
      },
      deep: true
    }
  },
  created() {
    // 设置默认查询日期范围为最近7天
    this.setDefaultDateRange()
    // 不在created中调用getList，由watch处理
    this.getDeviceConfigList();
  },
  methods: {
    getDeviceConfigList(){
      listDeviceConfig({queryAllData: true}).then(res => {
        this.deviceConfigList = res.rows;
      })
    },
    /** 设置默认日期范围 */
    setDefaultDateRange() {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)

      // 确保开始时间为 00:00:00，结束时间为 23:59:59
      start.setHours(0, 0, 0, 0)
      end.setHours(23, 59, 59, 999)

      this.dateRange = [
        this.parseTime(start, '{y}-{m}-{d} {h}:{i}:{s}'),
        this.parseTime(end, '{y}-{m}-{d} {h}:{i}:{s}')
      ]
      this.queryParams.params.beginTime = this.dateRange[0]
      this.queryParams.params.endTime = this.dateRange[1]
    },
    /** 查询API告警列表 */
    getList() {
      this.loading = true

      // 通知父组件同步查询条件和按钮选中状态
      this.$emit('query-change', {
        riskType: this.queryParams.riskType,
        deviceConfigId: this.queryParams.deviceConfigId
      })

      // 同步请求类型统计数据
      this.$emit('getList', { ...this.queryParams })
      listFlowRiskAssets(this.queryParams).then(response => {
        this.apiAlarmList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 日期范围发生变化
    handleDateRangeChange(val) {
      if (val && val.length === 2) {
        // 确保开始时间为 00:00:00，结束时间为 23:59:59
        const startDate = new Date(val[0])
        const endDate = new Date(val[1])

        startDate.setHours(0, 0, 0, 0)
        endDate.setHours(23, 59, 59, 999)

        this.queryParams.params.beginTime = this.parseTime(startDate, '{y}-{m}-{d} {h}:{i}:{s}')
        this.queryParams.params.endTime = this.parseTime(endDate, '{y}-{m}-{d} {h}:{i}:{s}')

        // 更新dateRange显示值
        this.dateRange = [
          this.queryParams.params.beginTime,
          this.queryParams.params.endTime
        ]
      } else {
        this.queryParams.params.beginTime = undefined
        this.queryParams.params.endTime = undefined
      }
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        handleState: '',
        handleDesc: ''
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      // 如果时间范围为空，设置默认时间范围
      if (!this.queryParams.params.beginTime || !this.queryParams.params.endTime) {
        this.setDefaultDateRange()
      }
      this.queryParams.pageNum = 1

      // 通知父组件同步查询条件和按钮选中状态
      this.$emit('query-change', {
        riskType: this.queryParams.riskType,
        deviceConfigId: this.queryParams.deviceConfigId
      })

      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      // 手动重置所有查询字段
      this.queryParams.riskAssets = undefined
      this.queryParams.riskType = undefined
      this.queryParams.handleState = undefined
      this.queryParams.deviceConfigId = undefined
      this.setDefaultDateRange()
      // 通知父组件重置按钮选中状态
      this.$emit('reset-button')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 处置按钮操作 */
    handleDispose(row) {
      this.disposeForm = {
        id: row.id,
        handleState: row.handleState === 2 ? row.handleState : undefined,
        handleDesc: row.handleState === 2 ? (row.handleDesc || '') : ''
      }
      this.disposeOpen = true
      // 清除表单验证状态
      this.$nextTick(() => {
        if (this.$refs.disposeForm) {
          this.$refs.disposeForm.clearValidate()
        }
      })
    },
    /** 提交处置 */
    submitDispose() {
      this.$refs['disposeForm'].validate(valid => {
        if (valid) {
          handleAlarm({
            id: this.disposeForm.id,
            handleState: this.disposeForm.handleState,
            handleDesc: this.disposeForm.handleDesc
          }).then(response => {
            this.$modal.msgSuccess('处置成功')
            this.disposeOpen = false
            this.getList()
          })
        }
      })
    },
    /** 批量处置按钮操作 */
    handleBatchDispose() {
      if (this.ids.length === 0) {
        this.$modal.msgError('请至少选择一条记录')
        return
      }
      this.batchDisposeForm = {
        eventIds: this.ids,
        handleState: 1,
        handleDesc: ''
      }
      this.batchDisposeOpen = true
    },
    /** 提交批量处置 */
    submitBatchDispose() {
      this.$refs['batchDisposeForm'].validate(valid => {
        if (valid) {
          const eventIds = this.batchDisposeForm.eventIds
          const handleState = this.batchDisposeForm.handleState
          const handleDesc = this.batchDisposeForm.handleDesc

          batchHandleAlarms({
            eventIds: eventIds,
            handleState: handleState,
            handleDesc: handleDesc
          }).then(response => {
            this.$modal.msgSuccess('批量处置成功')
            this.batchDisposeOpen = false
            this.getList()
          })
        }
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        'ffsafe/flowRiskAssets/export',
        {
          ...this.queryParams
        },
        'API告警数据.xlsx'
      )
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      this.detailData = { ...row }
      this.detailOpen = true
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除该API告警记录？').then(() => {
        return this.deleteFlowRiskAssets(row.id)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {})
    },
    /** 删除API告警记录 */
    deleteFlowRiskAssets(id) {
      return delFlowRiskAssets(id)
    },
    /** 获取风险类别标签 */
    getRiskTypeLabel(riskType) {
      const dict = this.dict.type.flow_risk_type.find(d => d.value === riskType)
      return dict ? dict.label : riskType
    },
    /** 获取处置状态标签 */
    getHandleStateLabel(handleState) {
      const option = this.handleStateOptions.find(item => item.value === handleState)
      return option ? option.label : '未知'
    },
    handleStateFormatter(row, column, cellValue, index) {
      let name = '未处置'
      const match = this.handleStateOptions.find(item => item.value === cellValue)
      if (match) {
        name = match.label
      }
      return name
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
