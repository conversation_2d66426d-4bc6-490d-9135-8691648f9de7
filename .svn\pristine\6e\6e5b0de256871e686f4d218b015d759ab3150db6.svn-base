package com.ruoyi.ffsafe.api.controller;

import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.collection.CollUtil;
import com.ruoyi.dict.domain.NetworkDomain;
import com.ruoyi.dict.service.INetworkDomainService;
import com.ruoyi.ffsafe.api.domain.TblDeviceConfig;
import com.ruoyi.ffsafe.api.service.ITblDeviceConfigService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 设备接入配置Controller
 *
 * <AUTHOR>
 * @date 2025-08-13
 */
@RestController
@RequestMapping("/api/deviceConfig")
public class TblDeviceConfigController extends BaseController
{
    @Autowired
    private ITblDeviceConfigService tblDeviceConfigService;
    @Resource
    private INetworkDomainService networkDomainService;

    /**
     * 查询设备接入配置列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TblDeviceConfig tblDeviceConfig)
    {
        if(!tblDeviceConfig.isQueryAllData()){
            startPage();
        }
        List<TblDeviceConfig> list = tblDeviceConfigService.selectTblDeviceConfigList(tblDeviceConfig);
        return getDataTable(list);
    }

    /**
     * 导出设备接入配置列表
     */
    @PreAuthorize("@ss.hasPermi('api:deviceConfig:export')")
    @Log(title = "设备接入配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TblDeviceConfig tblDeviceConfig)
    {
        List<TblDeviceConfig> list = tblDeviceConfigService.selectTblDeviceConfigList(tblDeviceConfig);
        ExcelUtil<TblDeviceConfig> util = new ExcelUtil<TblDeviceConfig>(TblDeviceConfig.class);
        util.exportExcel(response, list, "设备接入配置数据");
    }

    /**
     * 获取设备接入配置详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(tblDeviceConfigService.selectTblDeviceConfigById(id));
    }

    /**
     * 新增设备接入配置
     */
    @PreAuthorize("@ss.hasPermi('api:deviceConfig:add')")
    @Log(title = "设备接入配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TblDeviceConfig tblDeviceConfig)
    {
        return toAjax(tblDeviceConfigService.insertTblDeviceConfig(tblDeviceConfig));
    }

    /**
     * 修改设备接入配置
     */
    @PreAuthorize("@ss.hasPermi('api:deviceConfig:edit')")
    @Log(title = "设备接入配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TblDeviceConfig tblDeviceConfig)
    {
        return toAjax(tblDeviceConfigService.updateTblDeviceConfig(tblDeviceConfig));
    }

    /**
     * 修改状态
     * @return
     */
    @PreAuthorize("@ss.hasPermi('api:deviceConfig:edit')")
    @Log(title = "设备接入配置-状态", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody TblDeviceConfig tblDeviceConfig){
        TblDeviceConfig update = new TblDeviceConfig();
        update.setId(tblDeviceConfig.getId());
        update.setStatus(tblDeviceConfig.getStatus());
        return toAjax(tblDeviceConfigService.updateTblDeviceConfig(update));
    }

    /**
     * 删除设备接入配置
     */
    @PreAuthorize("@ss.hasPermi('api:deviceConfig:remove')")
    @Log(title = "设备接入配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        //查询是否已经绑定
        for (Long id : ids) {
            NetworkDomain networkDomain = new NetworkDomain();
            networkDomain.setDeviceConfigId(id);
            List<NetworkDomain> networkDomains = networkDomainService.selectNetworkDomainList(networkDomain);
            if(CollUtil.isNotEmpty(networkDomains)){
                return error("设备下有绑定网络区域，禁止删除");
            }
        }
        return toAjax(tblDeviceConfigService.deleteTblDeviceConfigByIds(ids));
    }
}
