package com.ruoyi.threaten.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.ffsafe.api.service.ITblDeviceConfigService;
import com.ruoyi.monitor2.domain.MonitorBssVulnDeal;
import com.ruoyi.monitor2.service.IMonitorBssVulnDealService;
import com.ruoyi.monitor2.service.IMonitorBssWebvulnDealService;
import com.ruoyi.monitor2.service.IMonitorBssWpDealService;
import com.ruoyi.quartz.domain.SysJob;
import com.ruoyi.quartz.service.ISysJobService;
import com.ruoyi.safe.domain.FfsafeIpfilterBlocking;
import com.ruoyi.safe.domain.NetworkIpMacInfo;
import com.ruoyi.safe.domain.TblBusinessApplication;
import com.ruoyi.safe.domain.TblDeductionDetail;
import com.ruoyi.safe.domain.dto.OverviewParams;
import com.ruoyi.safe.service.IFfsafeIpfilterBlockingService;
import com.ruoyi.safe.service.ITblBusinessApplicationService;
import com.ruoyi.safe.service.ITblDeductionDetailService;
import com.ruoyi.safe.service.ITblNetworkIpMacService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.threaten.domain.*;
import com.ruoyi.threaten.mapper.TblThreatenAlarmMapper;
import com.ruoyi.threaten.service.ITblHoneypotAlarmService;
import com.ruoyi.threaten.service.ITblThreatenAlarmService;
import com.ruoyi.work.domain.TblWorkOrder;
import com.ruoyi.work.service.ITblWorkOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/system/threadten")
public class TblThreatenAlarmController extends BaseController {
    @Autowired
    private ITblThreatenAlarmService tblThreatenAlarmService;
    @Resource
    private ITblNetworkIpMacService netIpMacService;
    @Resource
    private ITblBusinessApplicationService businessApplicationService;
    @Resource
    private ITblWorkOrderService workOrderService;
    @Resource
    private IFfsafeIpfilterBlockingService ffsafeIpfilterBlockingService;
    @Autowired
    private ITblDeductionDetailService tblDeductionDetailService;
    @Autowired
    private ISysUserService userService;
    @Resource
    private TblThreatenAlarmMapper tblThreatenAlarmMapper;
    @Resource
    private ITblHoneypotAlarmService tblHoneypotAlarmService;

    /**
     * 查询威胁情报列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TblThreatenAlarm tblThreatenAlarm)
    {
        //查询netIpMac
        List<NetworkIpMacInfo> networkIpMacInfos = null;
        if(!tblThreatenAlarm.isQueryAllData()){
            networkIpMacInfos = handleAssetInfo(tblThreatenAlarm);
        }
        //查工单
        List<TblWorkOrder> workOrders = new ArrayList<>();
        TblWorkOrder workOrderQuery = new TblWorkOrder();
        if(StrUtil.isNotBlank(tblThreatenAlarm.getFlowState()) && !"99".equals(tblThreatenAlarm.getFlowState())){
            workOrderQuery.setFlowState(tblThreatenAlarm.getFlowState());
        }
        workOrderQuery.setWorkType("3");
        workOrders = workOrderService.selectWaitList(workOrderQuery);
        if(StrUtil.isNotBlank(tblThreatenAlarm.getFlowState()) && !"99".equals(tblThreatenAlarm.getFlowState())){
            tblThreatenAlarm.setWorkOrderIdList(workOrders.stream().map(TblWorkOrder::getId).collect(Collectors.toList()));
            if(CollUtil.isEmpty(tblThreatenAlarm.getWorkOrderIdList())){
                tblThreatenAlarm.setWorkOrderIdList(Collections.singletonList(-1L));
            }
        }
        if (StrUtil.isNotBlank(tblThreatenAlarm.getDisposer())){
            SysUser user = new SysUser();
            user.setNickName(tblThreatenAlarm.getDisposer());
            List<SysUser> list = userService.selectUserList(user);
            if (CollUtil.isNotEmpty(list)){
                tblThreatenAlarm.setDisposers(list.stream().map(SysUser::getUserId).collect(Collectors.toList()));
            }else {
                return getDataTable(new ArrayList<>());
            }
        }
        startPage();
        List<TblThreatenAlarm> list = tblThreatenAlarmService.selectTblThreatenAlarmList(tblThreatenAlarm);
//        list.forEach(item->{
//            if (item.getHandleState()==null){
//                item.setHandleState(TblThreatenAlarm.NO_WORK);
//            }
//            if (item.getFlowState()==null){
//                item.setFlowState(TblThreatenAlarm.NO_WORK);
//            }
//        });
        if(CollUtil.isNotEmpty(list)){
            list.forEach(this::removePrefix);
        }
        handleResultAssetInfo(list,networkIpMacInfos);
        handleResultWorkOrderInfo(list,workOrders);
        handleResultBlocking(list);
        return getDataTable(list);
    }

    /**
     * 导出威胁情报列表
     */
    @Log(title = "威胁情报", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TblThreatenAlarm tblThreatenAlarm)
    {
        List<TblThreatenAlarm> list = tblThreatenAlarmService.selectTblThreatenAlarmList(tblThreatenAlarm);
        list.forEach(item->{
            if (item.getHandleState()==null){
                item.setHandleState(TblThreatenAlarm.NO_WORK);
            }
            if (item.getFlowState()==null){
                item.setFlowState(TblThreatenAlarm.NO_WORK);
            }
        });
        ExcelUtil<TblThreatenAlarm> util = new ExcelUtil<TblThreatenAlarm>(TblThreatenAlarm.class);
        util.hideColumn("createTime","reason","label","mateRule","handSuggest","attackNum","srcPort","destPort","attackType","attackStage","attackResult","associaDevice","logTime");
        util.exportExcel(response, list, "威胁情报数据");
    }

    @Log(title = "导入威胁告警", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult importServer(MultipartFile file) throws Exception {
        ExcelUtil<TblThreatenAlarm> util = new ExcelUtil<TblThreatenAlarm>(TblThreatenAlarm.class);
        List<TblThreatenAlarm> tblThreatenAlarmList = util.importExcel(file.getInputStream(), 1);

        for (int i=0;i<tblThreatenAlarmList.size();i++) {
            tblThreatenAlarmList.get(i).checkImportData(i);
        }

        try {
            tblThreatenAlarmService.insertTblThreatenAlarmList(tblThreatenAlarmList);
        } catch(Exception e) {
            logger.error("导入ThreantenAlarm数据出错： " + e.getMessage());
            throw new ServiceException("数据导入错误：" + e.getMessage());
        }
        String message = "威胁告警导入成功";
        return AjaxResult.success(message);
    }

    /**
     * 获取威胁情报详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        TblThreatenAlarm alarm = tblThreatenAlarmService.selectTblThreatenAlarmById(id);
        removePrefix(alarm);
        return AjaxResult.success(alarm);
    }

    /**
     * 新增威胁情报
     */
    @Log(title = "威胁情报", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TblThreatenAlarm tblThreatenAlarm)
    {
        return toAjax(tblThreatenAlarmService.insertTblThreatenAlarm(tblThreatenAlarm));
    }

    /**
     * 修改威胁情报
     */
    @Log(title = "威胁情报", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TblThreatenAlarm tblThreatenAlarm)
    {
        if (StrUtil.isBlank(tblThreatenAlarm.getDisposer()) && StrUtil.isNotBlank(tblThreatenAlarm.getHandleState())){
            Long userId = getUserId();
            tblThreatenAlarm.setDisposer(userId.toString());
        }
        if (StrUtil.isNotBlank(tblThreatenAlarm.getHandleState()) && "1".equals(tblThreatenAlarm.getHandleState())){
            TblDeductionDetail tblDeductionDetail = new TblDeductionDetail();
            tblDeductionDetail.setRiskType("外部威胁");
            tblDeductionDetail.setReferenceId(tblThreatenAlarm.getId().toString());
            List<TblDeductionDetail> tblDeductionDetails = tblDeductionDetailService.selectTblDeductionDetailList(tblDeductionDetail);
            if (CollUtil.isNotEmpty(tblDeductionDetails)){
                TblDeductionDetail deductionDetail = tblDeductionDetails.get(0);
                deductionDetail.setIsDel("0");
                deductionDetail.setDeleteTime(DateUtil.date());
                tblDeductionDetailService.updateTblDeductionDetail(deductionDetail);
            }
        }
        return toAjax(tblThreatenAlarmService.updateTblThreatenAlarm(tblThreatenAlarm));
    }

    /**
     * 删除威胁情报
     */
    @Log(title = "威胁情报", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tblThreatenAlarmService.deleteTblThreatenAlarmByIds(ids));
    }

    @GetMapping("/getAttackNum")
    public AjaxResult getAttackNum(TblThreatenAlarm tblThreatenAlarm) {
        int attackNum = tblThreatenAlarmService.getAttackNum(tblThreatenAlarm);
        return AjaxResult.success(attackNum);
    }

    @GetMapping("/getHighRiskHostNum")
    public AjaxResult getHighRiskHostNum(TblThreatenAlarm tblThreatenAlarm) {
        int highRiskHostNum = tblThreatenAlarmService.getHighRiskHostNum(tblThreatenAlarm);
        return AjaxResult.success(highRiskHostNum);
    }

    @GetMapping("/getRiskHostNum")
    public AjaxResult getRiskHostNum(TblThreatenAlarm tblThreatenAlarm) {
        int riskHostNum = tblThreatenAlarmService.getRiskHostNum(tblThreatenAlarm);
        return AjaxResult.success(riskHostNum);
    }

    @GetMapping("/statInfo")
    public TableDataInfo statInfo(TblThreatenAlarm tblThreatenAlarm) {
        List<Map<String, Object>> statMapList = new ArrayList<>();
        Map<String, Object> statMap = new HashMap<>();
        tblThreatenAlarm.setHandleState("0");
        CompletableFuture<Integer> attackNumFuture = CompletableFuture.supplyAsync(() -> tblThreatenAlarmService.getAttackNum(tblThreatenAlarm));
        CompletableFuture<Integer> highRiskHostNumFuture = CompletableFuture.supplyAsync(() -> tblThreatenAlarmService.getHighRiskHostNum(tblThreatenAlarm));
        CompletableFuture<Integer> riskHostNumFuture = CompletableFuture.supplyAsync(() -> tblThreatenAlarmService.getRiskHostNum(tblThreatenAlarm));
        CompletableFuture<Integer> alarmNumFuture = CompletableFuture.supplyAsync(() -> tblThreatenAlarmService.getThreatenAlarmNum(tblThreatenAlarm));
        CompletableFuture<Map<Integer, Object>> threatenLevelNumMapFuture = CompletableFuture.supplyAsync(() -> tblThreatenAlarmService.getThreatenLevelNum(tblThreatenAlarm));
        CompletableFuture.allOf(
                attackNumFuture,
                highRiskHostNumFuture,
                riskHostNumFuture,
                alarmNumFuture,
                threatenLevelNumMapFuture
        ).join();
        try {
            statMap.put("attackNum", attackNumFuture.get());
            statMap.put("highRishHostNum", highRiskHostNumFuture.get());
            statMap.put("rishHostNum", riskHostNumFuture.get());
            statMap.put("alarmNum", alarmNumFuture.get());

            Map<Integer, Object> threatenLevelNumMap = threatenLevelNumMapFuture.get();
            for (Map.Entry<Integer, Object> entry : threatenLevelNumMap.entrySet()) {
                int key = entry.getKey();
                if (key == 0) {
                    statMap.put("unknown", entry.getValue());
                } else if (key == 1) {
                    statMap.put("noThreat", entry.getValue());
                } else if (key == 2) {
                    statMap.put("low", entry.getValue());
                } else if (key == 3) {
                    statMap.put("middle", entry.getValue());
                } else if (key == 4) {
                    statMap.put("high", entry.getValue());
                } else if (key == 5) {
                    statMap.put("critical", entry.getValue());
                }
            }
        } catch (InterruptedException | ExecutionException e) {
            e.printStackTrace();
        }
        statMapList.add(statMap);
        return getDataTable(statMapList);
    }

    @GetMapping("/selectAttackIpList")
    public TableDataInfo selectAttackIpList(TblThreatenAlarm tblThreatenAlarm) {
        startPage();
        List<TblThreatenAlarm> tblThreatenAlarmList = tblThreatenAlarmService.selectAttackIpList(tblThreatenAlarm);
        return getDataTable(tblThreatenAlarmList);
    }

    @GetMapping("/selectVictimIpList")
    public TableDataInfo selectVictimIpList(TblThreatenAlarm tblThreatenAlarm) {
        startPage();
        List<TblThreatenAlarm> tblThreatenAlarmList = tblThreatenAlarmService.selectVictimIpList(tblThreatenAlarm);
        return getDataTable(tblThreatenAlarmList);
    }

    @GetMapping("/getAttackSegSummary")
    public AjaxResult getAttackSegSummary(TblThreatenAlarm tblThreatenAlarm) {
        /*if(tblThreatenAlarm.getDeptId() == null){
            tblThreatenAlarm.setDeptId(getDeptId());
        }*/
        //查询netIpMac
        List<NetworkIpMacInfo> networkIpMacInfos = handleAssetInfo(tblThreatenAlarm);
        //查工单
        List<TblWorkOrder> workOrders = new ArrayList<>();
        TblWorkOrder workOrderQuery = new TblWorkOrder();
        if(StrUtil.isNotBlank(tblThreatenAlarm.getFlowState()) && !"99".equals(tblThreatenAlarm.getFlowState())){
            workOrderQuery.setFlowState(tblThreatenAlarm.getFlowState());
        }
        workOrderQuery.setWorkType("3");
        workOrders = workOrderService.selectWaitList(workOrderQuery);
        if(StrUtil.isNotBlank(tblThreatenAlarm.getFlowState()) && !"99".equals(tblThreatenAlarm.getFlowState())){
            tblThreatenAlarm.setWorkOrderIdList(workOrders.stream().map(TblWorkOrder::getId).collect(Collectors.toList()));
            if(CollUtil.isEmpty(tblThreatenAlarm.getWorkOrderIdList())){
                tblThreatenAlarm.setWorkOrderIdList(Collections.singletonList(-1L));
            }
        }
        if (StrUtil.isNotBlank(tblThreatenAlarm.getDisposer())){
            SysUser user = new SysUser();
            user.setNickName(tblThreatenAlarm.getDisposer());
            List<SysUser> list = userService.selectUserList(user);
            if (CollUtil.isNotEmpty(list)){
                tblThreatenAlarm.setDisposers(list.stream().map(SysUser::getUserId).collect(Collectors.toList()));
            }else {
                tblThreatenAlarm.setDisposers(Collections.singletonList(-123L));
            }
        }
        TblThreatenTypeSeg tblThreatenTypeSeg = tblThreatenAlarmService.getAttackSegSummary(tblThreatenAlarm);
        return AjaxResult.success(tblThreatenTypeSeg);
    }

    @GetMapping("/getAssetAttackSegSummary")
    public AjaxResult getAssetAttackSegSummary(TblThreatenAlarm tblThreatenAlarm) {
        //查询netIpMac
        List<NetworkIpMacInfo> networkIpMacInfos = handleAssetInfo(tblThreatenAlarm);
        TblThreatenTypeSeg tblThreatenTypeSeg = tblThreatenAlarmService.getAssetAttackSegSummary(tblThreatenAlarm);
        return AjaxResult.success(tblThreatenTypeSeg);
    }

    @GetMapping("/selectIpAttackSegNum")
    public AjaxResult selectIpAttackSegNum(TblThreatenAlarm tblThreatenAlarm) {
        TblThreatenTypeSeg tblThreatenTypeSeg = tblThreatenAlarmService.selectIpAttackSegNum(tblThreatenAlarm);
        return AjaxResult.success(tblThreatenTypeSeg);
    }

    @GetMapping("/selectAttackIpOverviewList")
    public TableDataInfo selectAttackIpOverviewList(TblThreatenAlarm tblThreatenAlarm) {
        //查询netIpMac
        List<NetworkIpMacInfo> networkIpMacInfos = handleAssetInfo(tblThreatenAlarm);
        startPage();
        List<TblThreatenAlarm> attackIpOverviewList = tblThreatenAlarmService.selectAttackIpOverviewList(tblThreatenAlarm);
        handleResultAssetInfo(attackIpOverviewList,networkIpMacInfos);
        return getDataTable(attackIpOverviewList);
    }

    @GetMapping("/selectVictimIpOverviewList")
    public TableDataInfo selectVictimIpOverviewList(TblThreatenAlarm tblThreatenAlarm) {
        startPage();
        //查询netIpMac
        List<NetworkIpMacInfo> networkIpMacInfos = handleAssetInfo(tblThreatenAlarm);
        List<TblThreatenAlarm>  victimIpOverviewList = tblThreatenAlarmService.selectVictimIpOverviewList(tblThreatenAlarm);
        handleResultAssetInfo(victimIpOverviewList,networkIpMacInfos);
        return getDataTable(victimIpOverviewList);
    }

    @GetMapping("/selectAssetIpOverviewList")
    public TableDataInfo selectAssetIpOverviewList(TblThreatenAlarm tblThreatenAlarm) {
        if(StrUtil.isNotBlank(tblThreatenAlarm.getAttackSeg())){
            List<String> threatenTypeList = tblThreatenAlarmMapper.selectThreatenTypeByAttackSeg(tblThreatenAlarm.getAttackSeg());
            tblThreatenAlarm.setThreatenTypeList(threatenTypeList);
        }
        //查询netIpMac
        List<NetworkIpMacInfo> networkIpMacInfos = handleAssetInfo(tblThreatenAlarm);
        startPage();
        List<TblThreatenAlarm>  assetIpOverviewList = tblThreatenAlarmService.selectAssetIpOverviewList(tblThreatenAlarm);
        handleResultAssetInfo(assetIpOverviewList,networkIpMacInfos);
        return getDataTable(assetIpOverviewList);
    }

    @GetMapping("/selectAttackIpReportList")
    public TableDataInfo selectAttackIpReportList(TblThreatenAlarm tblThreatenAlarm) {
        startPage();
        List<TblThreatenAlarm> attackIpReportList = tblThreatenAlarmService.selectAttackIpReportList(tblThreatenAlarm);
        return getDataTable(attackIpReportList);
    }

    @GetMapping("/selectVictimIpReportList")
    public TableDataInfo selectVictimIpReportList(TblThreatenAlarm tblThreatenAlarm) {
        startPage();
        List<TblThreatenAlarm> victimIpReportList = tblThreatenAlarmService.selectVictimIpReportList(tblThreatenAlarm);
        return getDataTable(victimIpReportList);
    }

    @GetMapping("/selectOutConnectReportList")
    public TableDataInfo selectOutConnectReportList(TblThreatenAlarm tblThreatenAlarm) {
        startPage();
        List<TblThreatenAlarm> outConnectionReportList = tblThreatenAlarmService.selectOutConnectReportList(tblThreatenAlarm);
        return getDataTable(outConnectionReportList);
    }

    @GetMapping("/selectAttackIpVictimStatReport")
    public TableDataInfo selectAttackIpVictimStatReport(TblThreatenAlarm tblThreatenAlarm) {
        List<TblThreatenAlarm> attackIpVictimStatList = tblThreatenAlarmService.selectAttackIpVictimStatReport(tblThreatenAlarm);
        return getDataTable(attackIpVictimStatList);
    }

    @GetMapping("/selectVictimIpAttackStatReport")
    public TableDataInfo selectVictimIpAttackStatReport(TblThreatenAlarm tblThreatenAlarm) {
        List<TblThreatenAlarm> victimIpAttackStatList = tblThreatenAlarmService.selectVictimIpAttackStatReport(tblThreatenAlarm);
        return getDataTable(victimIpAttackStatList);
    }

    @GetMapping("/selectTimeAxisList")
    public TableDataInfo selectTimeAxisList(TblThreatenAlarm tblThreatenAlarm) {
        startPage();
        List<TblThreatenAlarm> timeAxisList = tblThreatenAlarmService.selectTimeAxisList(tblThreatenAlarm);
        return getDataTable(timeAxisList);
    }

    /**
     * * 下载威胁情报
     * @param response
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<TblThreatenAlarmTemplate> util = new ExcelUtil<TblThreatenAlarmTemplate>(TblThreatenAlarmTemplate.class);
        List<TblThreatenAlarmTemplate> list=new ArrayList<>();
        TblThreatenAlarmTemplate template=new TblThreatenAlarmTemplate();
        template.setThreatenName("例如：ssti告警");
        template.setAlarmLevel(2L);
        template.setThreatenType("网络攻击/漏洞利用告警/模板注入");
        template.setReason("1、攻击者首*************先请求1.1.2./setup.cgi，尝试获取setup.cig权限到/tmp文件中，使用rm -rf/* 指令删除该文件(该命令可以删除 linux系统中所有文件且无需确认)");
        template.setHandSuggest("1、首选确认目标资产是否存在可能被攻击的路径，*******/setup.cgiuri路径，确实是否存在攻击路径");
        template.setLabel("ssti");
        /*template.setAttackNum(1L);*/
        template.setAssociaDevice("东区雷池日志(**************)");
        template.setLogTime(new Date());
        template.setCreateTime(new Date());
        template.setUpdateTime(new Date());
        template.setSrcIp("************* 内部信息服务设备");
        template.setDestIp("***********");
        template.setSrcPort(8090L);
        template.setDestPort(8080L);
        list.add(template);
        util.exportExcel(response, list,"威胁告警模板",  "注意事项：\n" +
                "1. 字段前有*则属于必填项，如未填写则无法导入，其余为非必填选项。（如必填项不知道信息，请填写“空”字表示）\n" +
                "2. 日志时间、告警时间、告警更新时间，导入数据时可以填写“同一个时间”。（如果第三方数据提供，也可填写不同时间）\n"+
                "3. 当导入数据中告警名称、告警类型、告警等级、攻击IP、攻击端口，5个字段与平台数据库中的完全相同时，则需进行合并处理。（需判断该威胁告警事件的工单状态，当工单状态为：未分配、待处理、待验证状态时，符合重复漏洞与告警信息则数量加1，当工单状态为：已完成时，则符合重复漏洞与告警信息重新生成一条记录进行计数。）");
    }

    /**
     * 攻击阶段 查询
     * <AUTHOR>
     * @Description //
     * @Date 2024/9/12 14:27
     * @param tblThreatenAlarm
     * @return java.util.Map<String, Integer>
     **/
    @GetMapping("/getEventSegTypeList")
    public AjaxResult getEventSegTypeList(TblThreatenAlarm tblThreatenAlarm) {
        TblThreatenTypeSeg seg = tblThreatenAlarmService.getEventSegTypeList(tblThreatenAlarm);
        return AjaxResult.success(seg);
    }

    /** 攻击者/受害者视角 告警名称分组查询 **/
    @GetMapping("/getGroupByName")
    public TableDataInfo getGroupByName(TblThreatenAlarm tblThreatenAlarm) {
        startPage();
        List<TblThreatenAlarm> list = tblThreatenAlarmService.getGroupByName(tblThreatenAlarm);
        return getDataTable(list);
    }

    /**  攻击者/受害者视角 告警等级分组查询 **/
    @GetMapping("/getGroupByLevel")
    public TableDataInfo getGroupByLevel(TblThreatenAlarm tblThreatenAlarm) {
        startPage();
        List<TblThreatenAlarm> list = tblThreatenAlarmService.getGroupByLevel(tblThreatenAlarm);
        return getDataTable(list);
    }

    /** 攻击者 攻击者分组查询 **/
    @GetMapping("/getGroupByAttack")
    public TableDataInfo getGroupByAttack(TblThreatenAlarm tblThreatenAlarm) {
        startPage();
        List<TblThreatenAlarm> list = tblThreatenAlarmService.getGroupByAttack(tblThreatenAlarm);
        return getDataTable(list);
    }

    /** 受害者 分组查询**/
    @GetMapping("/getGroupByVictim")
    public TableDataInfo getGroupByVictim(TblThreatenAlarm tblThreatenAlarm) {
        startPage();
        List<TblThreatenAlarm> list = tblThreatenAlarmService.getGroupByVictim(tblThreatenAlarm);
        return getDataTable(list);
    }


    @Autowired
    private IMonitorBssVulnDealService monitorBssVulnDealService;
    @Autowired
    private IMonitorBssWebvulnDealService monitorBssWebvulnDealService;
    @Autowired
    private IMonitorBssWpDealService monitorBssWpDealService;
    @Autowired
    private ISysJobService jobService;
    /**
     * 漏洞风险头部统计
     * */
    @GetMapping("/getVulnerabilityRiskHeadCount")
    public AjaxResult getVulnerabilityRiskHeadCount() {
        JSONObject count = new JSONObject();

        // 主机风险
        JSONObject hostRisk = calculateHostRisk(new TblThreatenAlarm());
        count.put("hostRisk", hostRisk);

        // Web 风险
        JSONObject webRisk = calculateWebRisk(new TblThreatenAlarm());
        count.put("webRisk", webRisk);

        count.put("totalNumberOfRisks", hostRisk.getIntValue("hostRiskNum") + webRisk.getIntValue("webRiskNum"));

        // 漏扫任务
        JSONObject vulnerabilityScanning = calculateVulnerabilityScanning();
        count.put("vulnerabilityScanning", vulnerabilityScanning);

        return AjaxResult.success(count);
    }

    private JSONObject calculateHostRisk(TblThreatenAlarm tblThreatenAlarm) {
        JSONObject hostRisk = new JSONObject();
        hostRisk.put("hostRiskNum", 0); // 初始化主机风险数量
        MonitorBssVulnDeal monitorBssVulnDeal = new MonitorBssVulnDeal();
        handleAssetInfo(monitorBssVulnDeal);
        tblThreatenAlarm.setIpv4List(monitorBssVulnDeal.getIpv4List());
        List<HashMap> rickLevelStat = monitorBssVulnDealService.getRickLevelStat(tblThreatenAlarm);
        if (isValidList(rickLevelStat)) {
            rickLevelStat.removeIf(item -> isValidSeverity(item, 0L));
            int num = calculateTotalNum(rickLevelStat, "num");
            hostRisk.put("hostRiskNum", num); // 更新主机风险数量
        }

        hostRisk.put("ipVulnerabilityLevelNum", rickLevelStat); // 风险等级统计

        List<HashMap> handleStateStat = monitorBssWpDealService.getHandleStateStat();
        if (isValidList(handleStateStat)) {
            int weakPasswordsNum = calculateTotalNum(handleStateStat, "num");
            hostRisk.put("weakPasswordsNum", weakPasswordsNum); // 弱口令数量
            hostRisk.put("hostRiskNum", hostRisk.getIntValue("hostRiskNum") + weakPasswordsNum); // 更新主机风险数量
        } else {
            hostRisk.put("weakPasswordsNum", 0); // 默认弱口令数量为 0
        }

        return hostRisk;
    }

    private JSONObject calculateWebRisk(TblThreatenAlarm tblThreatenAlarm) {
        JSONObject webRisk = new JSONObject();
        webRisk.put("webRiskNum", 0); // 初始化 Web 风险数量

        List<HashMap> levelStat = monitorBssWebvulnDealService.getRickLevelStat(tblThreatenAlarm);
        if (isValidList(levelStat)) {
            levelStat.removeIf(item -> isValidSeverity(item, 0L));
            int num = calculateTotalNum(levelStat, "num");
            webRisk.put("webRiskNum", num); // 更新 Web 风险数量
        }

        webRisk.put("webVulnerabilityLevelNum", levelStat); // Web 风险等级统计

        return webRisk;
    }

    private JSONObject calculateVulnerabilityScanning() {
        JSONObject vulnerabilityScanning = new JSONObject();
        vulnerabilityScanning.put("vulnerabilityScanningNum", 0); // 初始化漏扫任务数量

        SysJob sysJob = new SysJob();
        sysJob.setJobType(1);
        sysJob.setJobGroup("ASSET_SCAN");
        List<SysJob> ipSysJobs = jobService.selectJobList(sysJob);
        vulnerabilityScanning.put("hostScanningNum", ipSysJobs.size()); // 主机扫描任务数量

        sysJob.setJobType(2);
        List<SysJob> webSysJobs = jobService.selectJobList(sysJob);
        vulnerabilityScanning.put("webVulnerabilityScanningNum", webSysJobs.size()); // Web 扫描任务数量

        vulnerabilityScanning.put("vulnerabilityScanningNum",
                vulnerabilityScanning.getIntValue("hostScanningNum") +
                        vulnerabilityScanning.getIntValue("webVulnerabilityScanningNum")); // 总漏扫任务数量

        return vulnerabilityScanning;
    }

    private boolean isValidList(List<?> list) {
        return list != null && !list.isEmpty();
    }

    private boolean isValidSeverity(HashMap item, long severity) {
        Object value = item.get("severity");
        return value != null && value.equals(severity);
    }

    private int calculateTotalNum(List<HashMap> list, String key) {
        return list.stream()
                .filter(item -> item.containsKey(key) && item.get(key) != null)
                .mapToInt(item -> Integer.parseInt(item.get(key).toString()))
                .sum();
    }


    /** 导出 攻击者视角数据 **/
    @PostMapping("/exportAttack")
    public void exportAttack(HttpServletResponse response, TblThreatenAlarm tblThreatenAlarm) {
        List<TblThreatenAlarm> attackList = tblThreatenAlarmService.selectAttackIpOverviewList(tblThreatenAlarm);
        List<TblThreatenAlarm> stage = tblThreatenAlarmService.exportAttackStage(tblThreatenAlarm);
        List<ExportThreaten> list = attackList.stream().map(e -> {
            ExportThreaten export = new ExportThreaten();
            export.setAttackIp(e.getAttackIp());
            export.setAlarmNum(e.getAlarmNum());
            export.setUpdateTime(e.getUpdateTime());
            //
            TblThreatenTypeSeg seg = new TblThreatenTypeSeg();
            stage.forEach(s -> {
                if (StringUtils.isNotBlank(s.getAttackIp()) && StringUtils.isNotBlank(e.getAttackIp()) && e.getAttackIp().equals(s.getAttackIp())) {
                    seg.dataLineRoll(s.getAttackSeg(), s.getAlarmNum());
                }
            });
            export.setThreatenTypeSeg(seg);
            return export;
        }).collect(Collectors.toList());
        ExcelUtil<ExportThreaten> util = new ExcelUtil<>(ExportThreaten.class);
        util.hideColumn("victimIp","assetIp");
        util.exportExcel(response, list, "攻击者视角");
    }

    /** 导出 受害者视角数据 **/
    @PostMapping("/exportSuffer")
    public void exportSuffer(HttpServletResponse response, TblThreatenAlarm tblThreatenAlarm) {
        List<TblThreatenAlarm> sufferList = tblThreatenAlarmService.selectVictimIpOverviewList(tblThreatenAlarm);
        List<TblThreatenAlarm> stage = tblThreatenAlarmService.exportSufferStage(tblThreatenAlarm);
        List<ExportThreaten> list = sufferList.stream().map(e -> {
            ExportThreaten export = new ExportThreaten();
            export.setVictimIp(e.getVictimIp());
            export.setAlarmNum(e.getAlarmNum());
            export.setUpdateTime(e.getUpdateTime());
            //
            TblThreatenTypeSeg seg = new TblThreatenTypeSeg();
            stage.forEach(s -> {
                if (StringUtils.isNotBlank(s.getVictimIp()) && StringUtils.isNotBlank(e.getVictimIp()) && e.getVictimIp().equals(s.getVictimIp())) {
                    seg.dataLineRoll(s.getAttackSeg(), s.getAlarmNum());
                }
            });
            export.setThreatenTypeSeg(seg);
            return export;
        }).collect(Collectors.toList());
        ExcelUtil<ExportThreaten> util = new ExcelUtil<>(ExportThreaten.class);
        util.hideColumn("attackIp","assetIp");
        util.exportExcel(response, list, "受害者视角");
    }

    /** 导出 资产威胁  资产 数据 **/
    @PostMapping("/exportAsset")
    public void exportAsset(HttpServletResponse response, TblThreatenAlarm tblThreatenAlarm) {
        List<TblThreatenAlarm> assetIpList = tblThreatenAlarmService.selectAssetIpOverviewList(tblThreatenAlarm);
        List<TblThreatenAlarm> stage = tblThreatenAlarmService.exportSufferStage(tblThreatenAlarm);
        List<ExportThreaten> list = assetIpList.stream().map(e -> {
            ExportThreaten export = new ExportThreaten();
            export.setAssetIp(e.getVictimIp());
            export.setAlarmNum(e.getAlarmNum());
            export.setUpdateTime(e.getUpdateTime());
            //
            TblThreatenTypeSeg seg = new TblThreatenTypeSeg();
            stage.forEach(s -> {
                if (StringUtils.isNotBlank(s.getVictimIp()) && StringUtils.isNotBlank(e.getVictimIp()) && e.getVictimIp().equals(s.getVictimIp())) {
                    seg.dataLineRoll(s.getAttackSeg(), s.getAlarmNum());
                }
            });
            export.setThreatenTypeSeg(seg);
            return export;
        }).collect(Collectors.toList());
        ExcelUtil<ExportThreaten> util = new ExcelUtil<>(ExportThreaten.class);
        util.hideColumn("attackIp","victimIp","alarmNum");
        util.exportExcel(response, list, "资产IP");
    }

    @GetMapping("/ffSafeHoneypotAttackdetailList")
    public TableDataInfo ffsafeHoneypotAttackdetailList(TblHoneypotAlarm tblThreatenAlarm) {
        if(tblThreatenAlarm.getId() == null){
            return getDataTable(new ArrayList<>(1));
        }
        tblThreatenAlarm = tblHoneypotAlarmService.selectTblThreatenAlarmById(tblThreatenAlarm.getId());
        if(tblThreatenAlarm == null){
            return getDataTable(new ArrayList<>(1));
        }
        startPage();
        List<FfsafeHoneypotAttackdetail> list = tblHoneypotAlarmService.getFfsafeHoneypotAttackdetailList(tblThreatenAlarm);

        return getDataTable(list);
    }

    private void removePrefix(TblThreatenAlarm threatenAlarm){
        if(threatenAlarm == null || StrUtil.isBlank(threatenAlarm.getThreatenName())){
            return;
        }
        threatenAlarm.setSrcThreatenName(threatenAlarm.getThreatenName());
        String src = threatenAlarm.getThreatenName();
        src = src.replaceAll("^\\[.*?\\]", "");
        if(src.startsWith(" ")){
            src = src.substring(1);
        }
        threatenAlarm.setThreatenName(src);
    }

    /**
     * 添加阻断IP
     * @return
     */
    @PostMapping("/addBlockIp")
    public AjaxResult addBlockIp(@RequestBody JSONObject params){
        return tblThreatenAlarmService.addBlockIp(params);
    }

    @GetMapping("/groupAlarmLevelStatistics")
    public AjaxResult groupAlarmLevelStatistics(TblThreatenAlarm tblThreatenAlarm){
        List<NetworkIpMacInfo> networkIpMacInfos = handleAssetInfo(tblThreatenAlarm);

        //查工单
        List<TblWorkOrder> workOrders = new ArrayList<>();
        TblWorkOrder workOrderQuery = new TblWorkOrder();
        if(StrUtil.isNotBlank(tblThreatenAlarm.getFlowState()) && !"99".equals(tblThreatenAlarm.getFlowState())){
            workOrderQuery.setFlowState(tblThreatenAlarm.getFlowState());
        }
        workOrderQuery.setWorkType("3");
        workOrders = workOrderService.selectWaitList(workOrderQuery);
        if(StrUtil.isNotBlank(tblThreatenAlarm.getFlowState()) && !"99".equals(tblThreatenAlarm.getFlowState())){
            tblThreatenAlarm.setWorkOrderIdList(workOrders.stream().map(TblWorkOrder::getId).collect(Collectors.toList()));
            if(CollUtil.isEmpty(tblThreatenAlarm.getWorkOrderIdList())){
                tblThreatenAlarm.setWorkOrderIdList(Collections.singletonList(-1L));
            }
        }
        if (StrUtil.isNotBlank(tblThreatenAlarm.getDisposer())){
            SysUser user = new SysUser();
            user.setNickName(tblThreatenAlarm.getDisposer());
            List<SysUser> list = userService.selectUserList(user);
            if (CollUtil.isNotEmpty(list)){
                tblThreatenAlarm.setDisposers(list.stream().map(SysUser::getUserId).collect(Collectors.toList()));
            }else {
                tblThreatenAlarm.setDisposers(Collections.singletonList(-123L));
            }
        }

        List<JSONObject> dataList = tblThreatenAlarmService.groupAlarmLevelStatistics(tblThreatenAlarm);
        JSONObject result = new JSONObject();
        dataList.forEach(data -> {
            result.put("alarmLevel" + data.getString("alarm_level"),data.getLongValue("alarmNum",0));
        });
        Set<String> keySet = result.keySet();
        Long total = 0L;
        for (String key : keySet) {
            total += result.getLong(key);
        }
        result.put("total",total);
        return AjaxResult.success(result);
    }

    private List<NetworkIpMacInfo> handleAssetInfo(TblThreatenAlarm threatenAlarm){
        //查询netIpMac
        JSONObject queryAssetInfoParams = new JSONObject();
        queryAssetInfoParams.put("domainId",threatenAlarm.getDomainId());
        queryAssetInfoParams.put("deptId",threatenAlarm.getDeptId());
        queryAssetInfoParams.put("applicationId",threatenAlarm.getApplicationId());
        List<NetworkIpMacInfo> assetInfoList = netIpMacService.selectAssetInfoList(queryAssetInfoParams);
        if(CollUtil.isNotEmpty(assetInfoList)){
            threatenAlarm.setIpv4List(assetInfoList.stream().map(NetworkIpMacInfo::getIpv4).collect(Collectors.toList()));
            //查询业务系统
            List<Long> businessAssetIds = new ArrayList<>();
            assetInfoList.forEach(assetInfo -> {
                String assetIdList = assetInfo.getAssetIdList();
                if(StrUtil.isNotBlank(assetIdList)){
                    businessAssetIds.addAll(StrUtil.split(assetIdList, ",").stream().map(Long::valueOf).collect(Collectors.toList()));
                }
            });
            if(CollUtil.isNotEmpty(businessAssetIds)){
                List<TblBusinessApplication> applicationList = businessApplicationService.selectByAssetIds(CollUtil.distinct(businessAssetIds));
                if(CollUtil.isNotEmpty(applicationList)){
                    assetInfoList.forEach(assetInfo -> {
                        String assetIdList = assetInfo.getAssetIdList();
                        if(StrUtil.isNotBlank(assetIdList)){
                            List<TblBusinessApplication> matchAppList = applicationList.stream().filter(application -> assetIdList.contains(application.getAssetId().toString())).collect(Collectors.toList());
                            assetInfo.setBusinessApplications(matchAppList);
                        }
                    });
                }
            }
        }
        /*if((threatenAlarm.getApplicationId() != null || threatenAlarm.getDeptId() != null || StrUtil.isNotBlank(threatenAlarm.getDomainId())) && CollUtil.isEmpty(threatenAlarm.getIpv4List())){
            threatenAlarm.setIpv4List(CollUtil.toList("noIP"));
        }*/
        boolean isAll = false;
        LoginUser loginUser = getLoginUser();
        if(loginUser != null){
            SysUser user = loginUser.getUser();
            isAll = user.haveAllData();
        }
        if(CollUtil.isEmpty(threatenAlarm.getIpv4List()) && !isAll){
            threatenAlarm.setIpv4List(CollUtil.toList("noIP"));
        }
        if((threatenAlarm.getApplicationId() == null && threatenAlarm.getDeptId() == null && StrUtil.isBlank(threatenAlarm.getDomainId())) && isAll){
            threatenAlarm.setIpv4List(null);
        }
        return assetInfoList;
    }

    private void handleResultAssetInfo(List<TblThreatenAlarm> threatenAlarmList, List<NetworkIpMacInfo> assetInfoList){
        if(CollUtil.isNotEmpty(threatenAlarmList) && CollUtil.isNotEmpty(assetInfoList)){
            threatenAlarmList.forEach(threatenAlarm -> {
                assetInfoList.stream().filter(assetInfo -> StrUtil.isNotBlank(assetInfo.getIpv4()) && (assetInfo.getIpv4().equals(threatenAlarm.getSrcIp()) || assetInfo.getIpv4().equals(threatenAlarm.getDestIp())))
                        .findFirst().ifPresent(assetInfo -> {
                            threatenAlarm.setDeptName(assetInfo.getDeptName());
                            threatenAlarm.setDeptIdStr(assetInfo.getDeptId());
                            threatenAlarm.setBusinessApplications(assetInfo.getBusinessApplications());
                            threatenAlarm.setAssetName(assetInfo.getAssetName());
                            threatenAlarm.setAssetTypeDesc(assetInfo.getAssetTypeDesc());
                            threatenAlarm.setAssetClassDesc(assetInfo.getAssetClassDesc());
                        });
            });
        }
    }

    private void handleResultWorkOrderInfo(List<TblThreatenAlarm> threatenAlarmList, List<TblWorkOrder> workOrderList){
        if(CollUtil.isNotEmpty(threatenAlarmList) && CollUtil.isNotEmpty(workOrderList)){
            threatenAlarmList.forEach(threatenAlarm -> {
                workOrderList.stream().filter(workOrder -> workOrder.getId().equals(threatenAlarm.getWorkOrderId()))
                        .findFirst().ifPresent(match -> {
                            threatenAlarm.setFlowState(match.getFlowState());
                            threatenAlarm.setWorkId(match.getId());
                            threatenAlarm.setProdId(match.getProdId());
                        });
            });
        }
    }

    private void handleResultBlocking(List<TblThreatenAlarm> threatenAlarmList){
        if(CollUtil.isNotEmpty(threatenAlarmList)){
            List<String> ipList = threatenAlarmList.stream().map(TblThreatenAlarm::getSrcIp).collect(Collectors.toList());
            if(CollUtil.isNotEmpty(ipList)){
                List<FfsafeIpfilterBlocking> list = ffsafeIpfilterBlockingService.list(new LambdaQueryWrapper<FfsafeIpfilterBlocking>()
                        .in(FfsafeIpfilterBlocking::getIp, ipList));
                DateTime nowDate = DateUtil.date();
                threatenAlarmList.forEach(threatenAlarm -> {
                    list.stream().filter(item -> item.getIp().equals(threatenAlarm.getSrcIp())).findFirst().ifPresent(item -> {
                        if(item.getReleaseTime().after(nowDate)){
                            threatenAlarm.setIsBlocking(true);
                        }
                    });
                });
            }
        }
    }

    /**
     * 获取外部威胁头部信息
     * */
    @GetMapping("/getExternalThreatHeadData")
    public AjaxResult getExternalThreatHeadData(TblThreatenAlarm tblThreatenAlarm) throws ExecutionException, InterruptedException {
        if (tblThreatenAlarm.getDeptId() == null){
            tblThreatenAlarm.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
        }
        if (tblThreatenAlarm.getDeptId() == 100){
            tblThreatenAlarm.setDeptId(null);
        }
        JSONObject result = new JSONObject();
        //查询netIpMac
        handleAssetInfo(tblThreatenAlarm);
        Date beginTime = new Date();
        Date comeToAnEndTime = new Date();
        Map<String,Integer> cycleAttackers = new LinkedHashMap<>();
        if ("1".equals(tblThreatenAlarm.getCycleType())){
            List<Date> dateList = getWeekDates(new Date());
            beginTime = DateUtil.beginOfWeek(new Date());
            comeToAnEndTime = DateUtil.endOfWeek(new Date());
            handlePeriodicAttackerData(tblThreatenAlarm,dateList,result,cycleAttackers);
        }else if ("2".equals(tblThreatenAlarm.getCycleType())) {
            //表示为本月
            List<Date> currentMonthDates = getCurrentMonthDates();
            //获取本月的开始时间和结束时间
            beginTime = DateUtil.beginOfMonth(currentMonthDates.get(0));
            comeToAnEndTime = DateUtil.endOfMonth(currentMonthDates.get(currentMonthDates.size() - 1));
            handlePeriodicAttackerData(tblThreatenAlarm,currentMonthDates,result,cycleAttackers);
        } else if ("3".equals(tblThreatenAlarm.getCycleType())) {
            //表示为本季度
            List<Date> startDates = getCurrentQuarterMonthStartDates();
            //获取当前季度的开始时间和结束时间
            beginTime = DateUtil.beginOfMonth(startDates.get(0));
            comeToAnEndTime = DateUtil.endOfMonth(startDates.get(startDates.size() - 1));
            handlePeriodicAttackerData(tblThreatenAlarm,startDates,result,cycleAttackers);
        } else if ("4".equals(tblThreatenAlarm.getCycleType())) {
            //表示为当年
            List<Date> startDates = getYearlyMonthStartDates();
            //获取当年开始时间和结束时间
            beginTime = DateUtil.beginOfYear(startDates.get(0));
            comeToAnEndTime = DateUtil.endOfYear(startDates.get(startDates.size() - 1));
            handlePeriodicAttackerData(tblThreatenAlarm,startDates,result,cycleAttackers);
        }
        Integer currentYear = DateUtil.year(new Date());
        tblThreatenAlarm.setStartTime(beginTime);
        tblThreatenAlarm.setEndTime(comeToAnEndTime);
        //获取年度威胁告警
        int threatenAlarmNum = tblThreatenAlarmService.selectTblThreatenAlarmListDeputy(tblThreatenAlarm);
        TblThreatenAlarm finalTblThreatenAlarm1 = tblThreatenAlarm;
        //获取年度阻断数
        FfsafeIpfilterBlocking ffsafeIpfilterBlocking = new FfsafeIpfilterBlocking();
        ffsafeIpfilterBlocking.setStartTime(beginTime);
        ffsafeIpfilterBlocking.setEndTime(comeToAnEndTime);
        CompletableFuture<List<FfsafeIpfilterBlocking>> safeIpFilterBlocKings = CompletableFuture.supplyAsync(() -> ffsafeIpfilterBlockingService.selectFfsafeIpfilterBlockingList(ffsafeIpfilterBlocking));
        //获取年度攻击者
        int attackersNum = 0;
        if (CollUtil.isNotEmpty(cycleAttackers)){
            //获取value进行求和
            attackersNum = cycleAttackers.values().stream().mapToInt(Integer::intValue).sum();
        }
        //CompletableFuture<Integer> attackIpOverviewNum = CompletableFuture.supplyAsync(() -> tblThreatenAlarmService.countAttackIpOverview(finalTblThreatenAlarm1));
        finalTblThreatenAlarm1.setHandleState("1");
        CompletableFuture<Integer> disposedOfNum = CompletableFuture.supplyAsync(() -> tblThreatenAlarmService.selectTblThreatenAlarmListDeputy(finalTblThreatenAlarm1));
        CompletableFuture.allOf(
                safeIpFilterBlocKings,
                disposedOfNum
        ).join();
        //外部威胁
        JSONObject externalThreat = new JSONObject();
        externalThreat.put("year", currentYear);//年度
        if (attackersNum > 0){ //攻击者
            externalThreat.put("assailantNum", attackersNum);
        }else {
            externalThreat.put("assailantNum", 0);
        }
        if (CollUtil.isNotEmpty(safeIpFilterBlocKings.get())){ //阻断
            externalThreat.put("interdictionNum", safeIpFilterBlocKings.get().size());
        }else {
            externalThreat.put("interdictionNum", 0);
        }
        if (threatenAlarmNum > 0 ){//威胁告警-年度
            externalThreat.put("yearThreatAlertsNum", threatenAlarmNum);
        }else {
            externalThreat.put("yearThreatAlertsNum", 0);
        }
        //List<TblThreatenAlarm> disposedOfList = threatenAlarmList.stream().filter(item -> "1".equals(item.getHandleState())).collect(Collectors.toList());
        if (disposedOfNum.get() > 0){ //处置数
            externalThreat.put("disposedOfNum", disposedOfNum.get());
        }else {
            externalThreat.put("disposedOfNum", 0);
        }
        result.put("externalThreatHead", externalThreat);
        return AjaxResult.success(result);
    }

    /**
     * 获取威胁告警周期数据
     * */
    @GetMapping("/getThreatAlertPeriodData")
    public AjaxResult getThreatAlertPeriodData(TblThreatenAlarm tblThreatenAlarm) {
        if (tblThreatenAlarm.getDeptId() == null){
            tblThreatenAlarm.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
        }
        if (tblThreatenAlarm.getDeptId() == 100){
            tblThreatenAlarm.setDeptId(null);
        }
        JSONObject jsonObject = new JSONObject();
        handleAssetInfo(tblThreatenAlarm);
        Map<String,Integer> cycleAttackers = new LinkedHashMap<>();
        if ("1".equals(tblThreatenAlarm.getCycleType())){
            //拿到本周的周一至周末
            List<Date> weekDates = getWeekDates(new Date());
            // 表示为本周，拿到当前周的开始时间和结束时间
            Date beginTime = DateUtil.beginOfWeek(new Date());
            Date comeToAnEndTime = DateUtil.endOfWeek(new Date());
            disposeThreatAlertPeriodData(tblThreatenAlarm, beginTime, comeToAnEndTime,weekDates,cycleAttackers,jsonObject);
        } else if ("2".equals(tblThreatenAlarm.getCycleType())) {
            //表示为本月
            List<Date> currentMonthDates = getCurrentMonthDates();
            //获取本月的开始时间和结束时间
            Date beginTime = DateUtil.beginOfMonth(currentMonthDates.get(0));
            Date comeToAnEndTime = DateUtil.endOfMonth(currentMonthDates.get(currentMonthDates.size() - 1));
            disposeThreatAlertPeriodData(tblThreatenAlarm, beginTime, comeToAnEndTime,currentMonthDates,cycleAttackers,jsonObject);
        } else if ("3".equals(tblThreatenAlarm.getCycleType())) {
            //表示为本季度
            List<Date> startDates = getCurrentQuarterMonthStartDates();
            //获取当前季度的开始时间和结束时间
            Date beginTime = DateUtil.beginOfMonth(startDates.get(0));
            Date comeToAnEndTime = DateUtil.endOfMonth(startDates.get(startDates.size() - 1));
            disposeThreatAlertPeriodData(tblThreatenAlarm, beginTime, comeToAnEndTime,startDates,cycleAttackers,jsonObject);
        } else if ("4".equals(tblThreatenAlarm.getCycleType())) {
            //表示为当年
            List<Date> startDates = getYearlyMonthStartDates();
            //获取当年开始时间和结束时间
            Date beginTime = DateUtil.beginOfYear(startDates.get(0));
            Date comeToAnEndTime = DateUtil.endOfYear(startDates.get(startDates.size() - 1));
            disposeThreatAlertPeriodData(tblThreatenAlarm, beginTime, comeToAnEndTime,startDates,cycleAttackers,jsonObject);
        }
        return AjaxResult.success(jsonObject);
    }

    public void disposeThreatAlertPeriodData(TblThreatenAlarm tblThreatenAlarm,Date beginTime, Date comeToAnEndTime,
                                             List<Date> weekDates,Map<String,Integer> cycleAttackers,JSONObject jsonObject){
        tblThreatenAlarm.setStartTime(beginTime);
        tblThreatenAlarm.setEndTime(comeToAnEndTime);
        final int[] threatAlertsNum = {0};
        weekDates.forEach(monthDate -> {
            Date startOfDay = DateUtil.beginOfDay(monthDate);
            Date endOfDay = DateUtil.endOfDay(monthDate);
            String format = "";
            if ("1".equals(tblThreatenAlarm.getCycleType())){
                format = DateUtil.format(monthDate, "MM-dd");
            }else if ("2".equals(tblThreatenAlarm.getCycleType())){
                format = DateUtil.format(monthDate, "MM-dd");
            }else if ("3".equals(tblThreatenAlarm.getCycleType())){
                Date monthStart = DateUtil.beginOfMonth(monthDate);
                Date monthEnd = DateUtil.endOfMonth(monthDate);
                startOfDay = monthStart;
                endOfDay = monthEnd;
                format = DateUtil.format(monthDate, "yyyy-MM");
            }else if ("4".equals(tblThreatenAlarm.getCycleType())){
                //根据monthDate获取这个月的末头和末尾
                Date monthStart = DateUtil.beginOfMonth(monthDate);
                Date monthEnd = DateUtil.endOfMonth(monthDate);
                startOfDay = monthStart;
                endOfDay = monthEnd;
                format = DateUtil.format(monthDate, "yyyy-MM");
            }
            tblThreatenAlarm.setStartTime(startOfDay);
            tblThreatenAlarm.setEndTime(endOfDay);
            int dayThreatenAlarmNum = tblThreatenAlarmService.selectTblThreatenAlarmListDeputy(tblThreatenAlarm);
            if (dayThreatenAlarmNum > 0){
                threatAlertsNum[0] += dayThreatenAlarmNum;
                cycleAttackers.put(format,dayThreatenAlarmNum);
            }else {
                cycleAttackers.put(format,0);
            }
        });
        jsonObject.put("threatAlertsNum", threatAlertsNum[0]); //周期告警总数
        jsonObject.put("numberOfPeriodicAlarms",cycleAttackers); //周期告警数据
    }

    /**
     * 获取告警分段以及告警动态数据
     * */
    @GetMapping("/getThreatAlertSegmentAndDynamicsData")
    public AjaxResult getThreatAlertSegmentAndDynamicsData(TblThreatenAlarm tblThreatenAlarm){
        if (tblThreatenAlarm == null || tblThreatenAlarm.getCycleType() == null) {
            throw new IllegalArgumentException("参数不能为空");
        }
        if (tblThreatenAlarm.getDeptId() == null){
            tblThreatenAlarm.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
        }
        if (tblThreatenAlarm.getDeptId() == 100){
            tblThreatenAlarm.setDeptId(null);
        }
        JSONObject result = new JSONObject();
        handleAssetInfo(tblThreatenAlarm);

        // 初始化威胁告警动态数据
        JSONObject threatAlertDynamics = new JSONObject();
        threatAlertDynamics.put("currentThreatAlertsNum", 0);
        threatAlertDynamics.put("successfullyBlockedNum", 0);
        threatAlertDynamics.put("theThreatHasBeenDisposedOfNum", 0);
        threatAlertDynamics.put("honeypotAlarmsNum", 0);

        String cycleType = tblThreatenAlarm.getCycleType();
        Date beginTime, endTime;

        switch (cycleType) {
            case "1": // 本周
                beginTime = DateUtil.beginOfWeek(new Date());
                endTime = DateUtil.endOfWeek(new Date());
                break;
            case "2": // 本月
                beginTime = DateUtil.beginOfMonth(new Date());
                endTime = DateUtil.endOfMonth(new Date());
                break;
            case "3": // 本季度
                List<Date> quarterMonths = getCurrentQuarterMonthStartDates();
                beginTime = DateUtil.beginOfMonth(quarterMonths.get(0));
                endTime = DateUtil.endOfMonth(quarterMonths.get(quarterMonths.size() - 1));
                break;
            case "4": // 本年
                List<Date> yearlyMonths = getYearlyMonthStartDates();
                beginTime = DateUtil.beginOfYear(yearlyMonths.get(0));
                endTime = DateUtil.endOfYear(yearlyMonths.get(yearlyMonths.size() - 1));
                break;
            default:
                throw new IllegalArgumentException("未知的周期类型: " + cycleType);
        }

        handleThreatAlertSegmentAndDynamicsData(tblThreatenAlarm, beginTime, endTime, threatAlertDynamics, result);

        return AjaxResult.success(result);
    }

    /**
     * 获取告警类型，告警top10，告警等级统计
     * */
    @GetMapping("/getAlarmTypeTop10AlarmsAlarmLevel")
    public AjaxResult getAlarmTypeTop10AlarmsAlarmLevel(TblThreatenAlarm tblThreatenAlarm) throws ExecutionException, InterruptedException {
        if (tblThreatenAlarm == null) {
            return AjaxResult.error("请求参数不能为空");
        }
        if (tblThreatenAlarm.getDeptId() == null){
            tblThreatenAlarm.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
        }
        if (tblThreatenAlarm.getDeptId() == 100){
            tblThreatenAlarm.setDeptId(null);
        }
        JSONObject result = new JSONObject();
        handleAssetInfo(tblThreatenAlarm);

        String cycleType = tblThreatenAlarm.getCycleType();
        Date beginTime = null;
        Date endTime = null;
        if ("1".equals(cycleType)) {
            // 本周
            beginTime = DateUtil.beginOfWeek(new Date());
            endTime = DateUtil.endOfWeek(new Date());
        } else if ("2".equals(cycleType)) {
            // 本月
            beginTime = DateUtil.beginOfMonth(new Date());
            endTime = DateUtil.endOfMonth(new Date());
        } else if ("3".equals(cycleType)) {
            // 本季度
            List<Date> startDates = getCurrentQuarterMonthStartDates();
            if (startDates == null || startDates.isEmpty()) {
                return AjaxResult.error("无法获取季度起始时间");
            }
            beginTime = DateUtil.beginOfMonth(startDates.get(0));
            endTime = DateUtil.endOfMonth(startDates.get(startDates.size() - 1));
        } else if ("4".equals(cycleType)) {
            // 当年
            List<Date> startDates = getYearlyMonthStartDates();
            if (startDates == null || startDates.isEmpty()) {
                return AjaxResult.error("无法获取年度起始时间");
            }
            beginTime = DateUtil.beginOfYear(startDates.get(0));
            endTime = DateUtil.endOfYear(startDates.get(startDates.size() - 1));
        } else {
            return AjaxResult.error("无效的周期类型");
        }
        handleAlarmTypeTop10AlarmsAlarmLevel(tblThreatenAlarm, beginTime, endTime, result);
        return AjaxResult.success(result);
    }

    /**
     * 获取告警系统排名
     * */
    @GetMapping("/getAlarmSystemRanking")
    public AjaxResult getAlarmSystemRankingAndHostOverview(TblThreatenAlarm tblThreatenAlarm){
        if (tblThreatenAlarm.getDeptId() == null){
            tblThreatenAlarm.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
        }
        if (tblThreatenAlarm.getDeptId() == 100){
            tblThreatenAlarm.setDeptId(null);
        }
        JSONObject result = new JSONObject();
        String cycleType = tblThreatenAlarm.getCycleType();
        // 定义常量类或直接使用 map 映射处理函数
        switch (cycleType) {
            case "1": // 本周
                setDateRange(tblThreatenAlarm, DateUtil.beginOfWeek(new Date()), DateUtil.endOfWeek(new Date()));
                break;
            case "2": // 本月
                setDateRange(tblThreatenAlarm, DateUtil.beginOfMonth(new Date()), DateUtil.endOfMonth(new Date()));
                break;
            case "3": { // 本季度
                List<Date> startDates = getCurrentQuarterMonthStartDates();
                if (startDates == null || startDates.isEmpty()) {
                    result.put("rankingOfAlarmSystems", Collections.emptyList());
                    return AjaxResult.success(result);
                }
                Date beginTime = DateUtil.beginOfMonth(startDates.get(0));
                Date endTime = DateUtil.endOfMonth(startDates.get(startDates.size() - 1));
                setDateRange(tblThreatenAlarm, beginTime, endTime);
                break;
            }
            case "4": { // 本年度
                List<Date> startDates = getYearlyMonthStartDates();
                if (startDates == null || startDates.isEmpty()) {
                    result.put("rankingOfAlarmSystems", Collections.emptyList());
                    return AjaxResult.success(result);
                }
                Date beginTime = DateUtil.beginOfYear(startDates.get(0));
                Date endTime = DateUtil.endOfYear(startDates.get(startDates.size() - 1));
                setDateRange(tblThreatenAlarm, beginTime, endTime);
                break;
            }
            default:
                // 处理未知周期类型
                result.put("rankingOfAlarmSystems", Collections.emptyList());
                return AjaxResult.success(result);
        }

        List<JSONObject> rankingOfAlarmSystems = tblThreatenAlarmService.getRankingOfAlarmSystems(tblThreatenAlarm);
        //删除rankingOfAlarmSystems中ipv4Num为0并且Num为0的数据
        if(CollUtil.isNotEmpty(rankingOfAlarmSystems)){
            rankingOfAlarmSystems.removeIf(item -> item.getInteger("ipv4Num") == 0 && item.getInteger("num") == 0);
        }
        result.put("rankingOfAlarmSystems", rankingOfAlarmSystems);
        return AjaxResult.success(result);
    }

    private void setDateRange(TblThreatenAlarm alarm, Date startTime, Date endTime) {
        alarm.setStartTime(startTime);
        alarm.setEndTime(endTime);
    }

    /**
     * 告警主机总览
     * */
    @GetMapping("/getHostOverview")
    public AjaxResult getHostOverview(TblThreatenAlarm tblThreatenAlarm){
        if (tblThreatenAlarm.getDeptId() == null){
            tblThreatenAlarm.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
        }
        if (tblThreatenAlarm.getDeptId() == 100){
            tblThreatenAlarm.setDeptId(null);
        }
        JSONObject result = new JSONObject();
        handleAssetInfo(tblThreatenAlarm);

        String cycleType = tblThreatenAlarm.getCycleType();

        Date now = new Date();
        Date beginTime = null;
        Date endTime = null;

        if (CycleType.WEEKLY.equals(cycleType)) {
            beginTime = DateUtil.beginOfWeek(now);
            endTime = DateUtil.endOfWeek(now);
        } else if (CycleType.MONTHLY.equals(cycleType)) {
            beginTime = DateUtil.beginOfMonth(now);
            endTime = DateUtil.endOfMonth(now);
        } else if (CycleType.QUARTERLY.equals(cycleType)) {
            List<Date> startDates = getCurrentQuarterMonthStartDates();
            if (!startDates.isEmpty()) {
                beginTime = DateUtil.beginOfMonth(startDates.get(0));
                endTime = DateUtil.endOfMonth(startDates.get(startDates.size() - 1));
            }
        } else if (CycleType.YEARLY.equals(cycleType)) {
            List<Date> startDates = getYearlyMonthStartDates();
            if (!startDates.isEmpty()) {
                beginTime = DateUtil.beginOfYear(startDates.get(0));
                endTime = DateUtil.endOfYear(startDates.get(startDates.size() - 1));
            }
        } else {
            // 可选：记录未知类型日志
            // log.warn("Unknown cycle type: {}", cycleType);
            return AjaxResult.error("无效的周期类型");
        }

        if (beginTime != null && endTime != null) {
            tblThreatenAlarm.setStartTime(beginTime);
            tblThreatenAlarm.setEndTime(endTime);
            JSONObject hostOverview = tblThreatenAlarmService.getHostOverview(tblThreatenAlarm);
            result.put("hostOverview", hostOverview);
        } else {
            // 可选：处理时间设置失败的情况
            return AjaxResult.error("无法确定时间范围");
        }

        return AjaxResult.success(result);
    }

    class CycleType {
        public static final String WEEKLY = "1";
        public static final String MONTHLY = "2";
        public static final String QUARTERLY = "3";
        public static final String YEARLY = "4";
    }

    /**
     * 获取周期攻击者数据
     * */
    @GetMapping("/getPeriodicAttackerData")
    public AjaxResult getPeriodicAttackerData(TblThreatenAlarm tblThreatenAlarm){
        if (tblThreatenAlarm.getDeptId() == null){
            tblThreatenAlarm.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
        }
        if (tblThreatenAlarm.getDeptId() == 100){
            tblThreatenAlarm.setDeptId(null);
        }
        JSONObject result = new JSONObject();
        handleAssetInfo(tblThreatenAlarm);
        Map<String,Integer> cycleAttackers = new LinkedHashMap<>();
        if ("1".equals(tblThreatenAlarm.getCycleType())) {
            //拿到本周的周一至周末
            List<Date> dateList = getWeekDates(new Date());
            handlePeriodicAttackerData(tblThreatenAlarm,dateList,result,cycleAttackers);
        }else if ("2".equals(tblThreatenAlarm.getCycleType())){
            //表示为本月
            List<Date> currentMonthDates = getCurrentMonthDates();
            handlePeriodicAttackerData(tblThreatenAlarm,currentMonthDates,result,cycleAttackers);
        }else if ("3".equals(tblThreatenAlarm.getCycleType())){
            //表示为本季度
            List<Date> startDates = getCurrentQuarterMonthStartDates();
            handlePeriodicAttackerData(tblThreatenAlarm,startDates,result,cycleAttackers);
        }else if ("4".equals(tblThreatenAlarm.getCycleType())){
            //表示为当年
            List<Date> startDates = getYearlyMonthStartDates();
            handlePeriodicAttackerData(tblThreatenAlarm,startDates,result,cycleAttackers);
        }
        return AjaxResult.success(result);
    }

    public void handlePeriodicAttackerData(TblThreatenAlarm tblThreatenAlarm,List<Date> dateList,JSONObject result,Map<String,Integer> cycleAttackers){
        AtomicInteger count  = new AtomicInteger();
        dateList.forEach(monthDate -> {
            Date startOfDay = DateUtil.beginOfDay(monthDate);
            Date endOfDay = DateUtil.endOfDay(monthDate);
            String format = "";
            if ("1".equals(tblThreatenAlarm.getCycleType())){
                format = DateUtil.format(monthDate, "MM-dd");
            }else if ("2".equals(tblThreatenAlarm.getCycleType())){
                format = DateUtil.format(monthDate, "MM-dd");
            }else if ("3".equals(tblThreatenAlarm.getCycleType())){
                Date monthStart = DateUtil.beginOfMonth(monthDate);
                Date monthEnd = DateUtil.endOfMonth(monthDate);
                startOfDay = monthStart;
                endOfDay = monthEnd;
                format = DateUtil.format(monthDate, "yyyy-MM");
            }else if ("4".equals(tblThreatenAlarm.getCycleType())){
                //根据monthDate获取这个月的末头和末尾
                Date monthStart = DateUtil.beginOfMonth(monthDate);
                Date monthEnd = DateUtil.endOfMonth(monthDate);
                startOfDay = monthStart;
                endOfDay = monthEnd;
                format = DateUtil.format(monthDate, "yyyy-MM");
            }
            tblThreatenAlarm.setStartTime(startOfDay);
            tblThreatenAlarm.setEndTime(endOfDay);
            int dayAttackIpOverviewNum = tblThreatenAlarmService.countAttackIpOverview(tblThreatenAlarm);
            if (dayAttackIpOverviewNum > 0){ //周期攻击者
                cycleAttackers.put(format,dayAttackIpOverviewNum);
                count.addAndGet(dayAttackIpOverviewNum);
            }else {
                cycleAttackers.put(format,0);
            }
        });
        result.put("count",count.get()); //周期攻击者总数
        result.put("cycleAttackers",cycleAttackers); //周期攻击者
    }


    public void handleAlarmTypeTop10AlarmsAlarmLevel(TblThreatenAlarm tblThreatenAlarm,Date beginTime, Date comeToAnEndTime,JSONObject result) throws ExecutionException, InterruptedException {
        tblThreatenAlarm.setStartTime(beginTime);
        tblThreatenAlarm.setEndTime(comeToAnEndTime);
        // 告警类型统计
        //List<JSONObject> alarmTypeNum = tblThreatenAlarmService.selectAlarmTypeNum(tblThreatenAlarm);
        CompletableFuture<List<JSONObject>> alarmTypeNum = CompletableFuture.supplyAsync(() -> tblThreatenAlarmService.selectAlarmTypeNum(tblThreatenAlarm));
        // 告警top10
        //List<JSONObject> top10AlarmsNum = tblThreatenAlarmService.selectTop10Alarm(tblThreatenAlarm);
        CompletableFuture<List<JSONObject>> top10AlarmsNum = CompletableFuture.supplyAsync(() -> tblThreatenAlarmService.selectTop10Alarm(tblThreatenAlarm));
        // 告警等级统计
        //List<JSONObject> alarmLevelStatisticsNum = tblThreatenAlarmService.selectAlarmLevelStatistics(tblThreatenAlarm);
        CompletableFuture<List<JSONObject>> alarmLevelStatisticsNum = CompletableFuture.supplyAsync(() -> tblThreatenAlarmService.selectAlarmLevelStatistics(tblThreatenAlarm));
        CompletableFuture.allOf(
                alarmTypeNum,
                top10AlarmsNum,
                alarmLevelStatisticsNum
        ).join();
        result.put("alarmTypeNum",alarmTypeNum.get());
        result.put("top10AlarmsNum",top10AlarmsNum.get());
        result.put("alarmLevelStatisticsNum",alarmLevelStatisticsNum.get());
    }

    public void handleThreatAlertSegmentAndDynamicsData(TblThreatenAlarm tblThreatenAlarm,Date beginTime,
                                                        Date comeToAnEndTime,JSONObject threatAlertDynamics,JSONObject result){
        tblThreatenAlarm.setStartTime(beginTime);
        tblThreatenAlarm.setEndTime(comeToAnEndTime);
        //获取告警分段
        TblThreatenTypeSeg tblThreatenTypeSeg = tblThreatenAlarmService.getAttackSegSummary(tblThreatenAlarm);
        //当前威胁告警
        int currentThreatAlerts = tblThreatenAlarmService.selectTblThreatenAlarmListDeputy(tblThreatenAlarm);
        //已成功阻断数
        FfsafeIpfilterBlocking weekSafeIpFilterBlocKing = new FfsafeIpfilterBlocking();
        weekSafeIpFilterBlocKing.setStartTime(beginTime);
        weekSafeIpFilterBlocKing.setEndTime(comeToAnEndTime);
        List<FfsafeIpfilterBlocking> weekSafeIpFilterBlocKings = ffsafeIpfilterBlockingService.selectFfsafeIpfilterBlockingList(weekSafeIpFilterBlocKing);
        //已处置威胁
        tblThreatenAlarm.setHandleState("1");
        int weekDisposedOfNum = tblThreatenAlarmService.selectTblThreatenAlarmListDeputy(tblThreatenAlarm);
        //蜜罐告警
        //tblThreatenAlarm.setHandleState(null);
        //tblThreatenAlarm.setThreatenType("其他/蜜罐诱捕");
        OverviewParams params = new OverviewParams();
        if (tblThreatenAlarm.getDeptId() == null){
            params.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
        }
        params.setStartTime(beginTime);
        params.setEndTime(comeToAnEndTime);
        int weekHoneypotAlarmNum = tblHoneypotAlarmService.countThreatenAlarmNum(params);
        //int weekHoneypotAlarmNum = tblThreatenAlarmService.selectTblThreatenAlarmListDeputy(tblThreatenAlarm);
        if (currentThreatAlerts >  0){
            threatAlertDynamics.put("currentThreatAlertsNum",currentThreatAlerts);
        }
        if (weekSafeIpFilterBlocKings.size() > 0){
            threatAlertDynamics.put("successfullyBlockedNum",weekSafeIpFilterBlocKings.size());
        }
        if (weekDisposedOfNum > 0){
            threatAlertDynamics.put("theThreatHasBeenDisposedOfNum",weekDisposedOfNum);
        }
        if (weekHoneypotAlarmNum > 0){
            threatAlertDynamics.put("honeypotAlarmsNum",weekHoneypotAlarmNum);
        }
        result.put("tblThreatenTypeSeg",tblThreatenTypeSeg);
        result.put("threatAlertDynamics",threatAlertDynamics);
    }


    /**
     * 最近阻断，攻击者top10
     * */
    @GetMapping("/getSafeDataByDeptIdAndDate")
    public AjaxResult getSafeDataByDeptIdAndDate(TblThreatenAlarm tblThreatenAlarm) throws ExecutionException, InterruptedException {
        // 参数校验
        if (tblThreatenAlarm == null) {
            return AjaxResult.error("请求参数不能为空");
        }

        // 查询资产信息
        handleAssetInfo(tblThreatenAlarm);

        // 设置默认部门ID
        if (tblThreatenAlarm.getDeptId() == null) {
            tblThreatenAlarm.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
        }
        if (tblThreatenAlarm.getDeptId() == 100) {
            tblThreatenAlarm.setDeptId(null);
        }

        Long deptId = tblThreatenAlarm.getDeptId();


        JSONObject result = new JSONObject();

        String cycleType = tblThreatenAlarm.getCycleType();
        if (cycleType == null) {
            return AjaxResult.error("周期类型不能为空");
        }

        Date now = new Date();

        switch (cycleType) {
            case "1": { // 本周
                List<Date> weekDates = getWeekDates(now);
                Date beginTime = DateUtil.beginOfWeek(now);
                Date endTime = DateUtil.endOfWeek(now);
                handleDataRequests(tblThreatenAlarm, deptId, beginTime, endTime, result);
                break;
            }
            case "2": { // 本月
                List<Date> currentMonthDates = getCurrentMonthDates();
                Date beginTime = DateUtil.beginOfMonth(currentMonthDates.get(0));
                Date endTime = DateUtil.endOfMonth(currentMonthDates.get(currentMonthDates.size() - 1));
                handleDataRequests(tblThreatenAlarm, deptId, beginTime, endTime, result);
                break;
            }
            case "3": { // 本季度
                List<Date> startDates = getCurrentQuarterMonthStartDates();
                Date beginTime = DateUtil.beginOfMonth(startDates.get(0));
                Date endTime = DateUtil.endOfMonth(startDates.get(startDates.size() - 1));
                handleDataRequests(tblThreatenAlarm, deptId, beginTime, endTime, result);
                break;
            }
            case "4": { // 当年
                List<Date> startDates = getYearlyMonthStartDates();
                Date beginTime = DateUtil.beginOfYear(startDates.get(0));
                Date endTime = DateUtil.endOfYear(startDates.get(startDates.size() - 1));
                handleDataRequests(tblThreatenAlarm, deptId, beginTime, endTime, result);
                break;
            }
            default:
                return AjaxResult.error("不支持的周期类型");
        }

        return AjaxResult.success(result);
    }

    public void handleDataRequests(TblThreatenAlarm tblThreatenAlarm,Long deptId,Date beginTime,Date comeToAnEndTime,JSONObject result) throws ExecutionException, InterruptedException {
        //查询netIpMac
        //handleAssetInfo(tblThreatenAlarm);
        tblThreatenAlarm = new TblThreatenAlarm();
        tblThreatenAlarm.setDeptId(deptId);
        tblThreatenAlarm.setStartTime(beginTime);
        tblThreatenAlarm.setEndTime(comeToAnEndTime);
        TblThreatenAlarm finalTblThreatenAlarm = tblThreatenAlarm;
        //CompletableFuture<Integer> assailantNum = CompletableFuture.supplyAsync(() -> tblThreatenAlarmService.countAttackIpOverview(finalTblThreatenAlarm));
        //List<JSONObject> recentlyBlockedList = tblThreatenAlarmService.selectRecentlyBlockedList(tblThreatenAlarm); //最近阻断
        CompletableFuture<List<JSONObject>> recentlyBlockedList = CompletableFuture.supplyAsync(() -> tblThreatenAlarmService.selectRecentlyBlockedList(finalTblThreatenAlarm));
        //List<JSONObject> assailantTop10List = tblThreatenAlarmService.selectAttackIpOverviewTop10(tblThreatenAlarm); //攻击者top10
        CompletableFuture<List<JSONObject>> assailantTop10List = CompletableFuture.supplyAsync(() -> tblThreatenAlarmService.selectAttackIpOverviewTop10(finalTblThreatenAlarm));
        CompletableFuture.allOf(
                recentlyBlockedList,
                assailantTop10List
        ).join();
        if(CollUtil.isNotEmpty(recentlyBlockedList.get())){ //最近已阻断
            result.put("recentlyBlocked",recentlyBlockedList.get());
        }else {
            result.put("recentlyBlocked",null);
        }
        if (CollUtil.isNotEmpty(assailantTop10List.get())){
            result.put("assailantTop10",assailantTop10List.get());
        }else {
            result.put("assailantTop10",null);
        }
    }


    /**
     * 内部风险头部数据
     * */
    @GetMapping("/getInnerRiskHeaderData")
    public AjaxResult getInnerRiskHeaderData(TblThreatenAlarm tblThreatenAlarm) {
        if (tblThreatenAlarm.getDeptId() == null){
            tblThreatenAlarm.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
        }
        if (tblThreatenAlarm.getDeptId() == 100){
            tblThreatenAlarm.setDeptId(null);
        }
        Long deptId = tblThreatenAlarm.getDeptId();
        JSONObject result = new JSONObject();
        //周期风险总览
        Map<String,Integer> cycleRiskOverview = new LinkedHashMap<>();
        tblThreatenAlarm.setDeptId(deptId);
        Date startTime = new Date();
        Date endTime = new Date();
        if ("1".equals(tblThreatenAlarm.getCycleType())){
            //拿到本周的周一至周末
            List<Date> weekDates = getWeekDates(new Date());
            Date beginTime = DateUtil.beginOfWeek(new Date());
            Date comeToAnEndTime = DateUtil.endOfWeek(new Date());
            startTime = beginTime;
            endTime = comeToAnEndTime;
            dealingWithInternalRiskHeads(tblThreatenAlarm,weekDates,beginTime,comeToAnEndTime,cycleRiskOverview,"1");
        }else if ("2".equals(tblThreatenAlarm.getCycleType())){
            //表示为本月
            List<Date> currentMonthDates = getCurrentMonthDates();
            //获取本月的开始时间和结束时间
            Date beginTime = DateUtil.beginOfMonth(currentMonthDates.get(0));
            Date comeToAnEndTime = DateUtil.endOfMonth(currentMonthDates.get(currentMonthDates.size() - 1));
            startTime = beginTime;
            endTime = comeToAnEndTime;
            dealingWithInternalRiskHeads(tblThreatenAlarm,currentMonthDates,beginTime,comeToAnEndTime,cycleRiskOverview,"2");
        }else if ("3".equals(tblThreatenAlarm.getCycleType())){
            //表示为本季度
            List<Date> startDates = getCurrentQuarterMonthStartDates();
            //获取当前季度的开始时间和结束时间
            Date beginTime = DateUtil.beginOfMonth(startDates.get(0));
            Date comeToAnEndTime = DateUtil.endOfMonth(startDates.get(startDates.size() - 1));
            startTime = beginTime;
            endTime = comeToAnEndTime;
            dealingWithInternalRiskHeads(tblThreatenAlarm,startDates,beginTime,comeToAnEndTime,cycleRiskOverview,"3");
        } else if ("4".equals(tblThreatenAlarm.getCycleType())) {
            //表示为当年
            List<Date> startDates = getYearlyMonthStartDates();
            //获取当年开始时间和结束时间
            Date beginTime = DateUtil.beginOfYear(startDates.get(0));
            Date comeToAnEndTime = DateUtil.endOfYear(startDates.get(startDates.size() - 1));
            startTime = beginTime;
            endTime = comeToAnEndTime;
            dealingWithInternalRiskHeads(tblThreatenAlarm,startDates,beginTime,comeToAnEndTime,cycleRiskOverview,"4");
        }
        AtomicInteger count = new AtomicInteger();
        //遍历cycleRiskOverview，统计value
        cycleRiskOverview.forEach((k,v) -> {
            count.addAndGet(v);
        });
        result.put("riskCount",count.get()); //风险总数
        result.put("cycleRiskOverview",cycleRiskOverview); //周期风险总览
        tblThreatenAlarm.setStartTime(startTime);
        tblThreatenAlarm.setEndTime(endTime);
        //获取hostRiskNum和disposedOfNum ip漏洞
        MonitorBssVulnDeal monitorBssVulnDeal = new MonitorBssVulnDeal();
        monitorBssVulnDeal.setDeptId(deptId);
        monitorBssVulnDeal.setStartTime(tblThreatenAlarm.getStartTime());
        monitorBssVulnDeal.setEndTime(tblThreatenAlarm.getEndTime());
        handleAssetInfo(monitorBssVulnDeal);
        JSONObject ipVulnerabilityDataAggregation = monitorBssVulnDealService.getInnerRiskHeaderData(monitorBssVulnDeal);
        //获取webVulnerabilitiesNum和webDisposalNum //web漏洞
        JSONObject webVulnerabilityDataAggregation = monitorBssVulnDealService.webVulnerabilityDataAggregation(tblThreatenAlarm);
        //获取weakIPPasswordsNum和weakIPDisposalNum //弱口令
        JSONObject weakIPPasswordsDataAggregation = monitorBssVulnDealService.getweakIPPasswordsDataAggregation(tblThreatenAlarm);
        result.put("ipVulnerabilityDataAggregation",ipVulnerabilityDataAggregation); //ip漏洞
        result.put("webVulnerabilityDataAggregation",webVulnerabilityDataAggregation); //web漏洞
        result.put("weakIPPasswordsDataAggregation",weakIPPasswordsDataAggregation); //弱口令
        return AjaxResult.success(result);
    }

    private void dealingWithInternalRiskHeads(TblThreatenAlarm tblThreatenAlarm,  List<Date> weekDates, Date beginTime,
                                              Date comeToAnEndTime,Map<String,Integer> cycleRiskOverview,String type){
        tblThreatenAlarm.setStartTime(beginTime);
        tblThreatenAlarm.setEndTime(comeToAnEndTime);
        MonitorBssVulnDeal monitorBssVulnDeal = new MonitorBssVulnDeal();
        monitorBssVulnDeal.setDeptId(tblThreatenAlarm.getDeptId());
        handleAssetInfo(monitorBssVulnDeal);
        weekDates.forEach(date -> {
            Date startOfDay = DateUtil.beginOfDay(date);
            Date endOfDay = DateUtil.endOfDay(date);
            String format;
            if ("1".equals(type)){
                format = DateUtil.format(date, "MM-dd");
            }else if ("2".equals(type)){
                format = DateUtil.format(date, "MM-dd");
            } else if ("3".equals(type) || "4".equals(type)) {
                Date monthStart = DateUtil.beginOfMonth(date);
                Date monthEnd = DateUtil.endOfMonth(date);
                startOfDay = monthStart;
                endOfDay = monthEnd;
                format = DateUtil.format(date, "yyyy-MM");
            } else {
                format = "";
            }
            tblThreatenAlarm.setStartTime(startOfDay);
            tblThreatenAlarm.setEndTime(endOfDay);
            monitorBssVulnDeal.setStartTime(tblThreatenAlarm.getStartTime());
            monitorBssVulnDeal.setEndTime(tblThreatenAlarm.getEndTime());
            JSONObject cycleIpVulnerabilityDataAggregation = monitorBssVulnDealService.getInnerRiskHeaderData(monitorBssVulnDeal);
            JSONObject cycleWebVulnerabilityDataAggregation = monitorBssVulnDealService.webVulnerabilityDataAggregation(tblThreatenAlarm);
            JSONObject cycleWeakIPPasswordsDataAggregation = monitorBssVulnDealService.getweakIPPasswordsDataAggregation(tblThreatenAlarm);
            int count = 0;
            if (cycleIpVulnerabilityDataAggregation != null){
                count += cycleIpVulnerabilityDataAggregation.getIntValue("hostRiskNum");
            }
            if (cycleWebVulnerabilityDataAggregation != null){
                count += cycleWebVulnerabilityDataAggregation.getIntValue("webVulnerabilitiesNum");
            }
            if (cycleWeakIPPasswordsDataAggregation != null){
                count += cycleWeakIPPasswordsDataAggregation.getIntValue("weakIPPasswordsNum");
            }
            cycleRiskOverview.put(format,count);
        });
    }

    /**
     * 主机漏洞、web漏洞、弱口令总数以及处置时长
     * */
    @GetMapping("/countTotalNumberOfVulnerabilitiesTimes")
    public AjaxResult countTotalNumberOfVulnerabilitiesTimes(MonitorBssVulnDeal monitorBssVulnDeal) {
        // 参数校验
        if (monitorBssVulnDeal == null || monitorBssVulnDeal.getCycleType() == null) {
            return AjaxResult.error("参数不能为空");
        }
        if (monitorBssVulnDeal.getDeptId() == null){
            monitorBssVulnDeal.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
        }
        if (monitorBssVulnDeal.getDeptId() == 100){
            monitorBssVulnDeal.setDeptId(null);
        }

        JSONObject result = new JSONObject();
        handleAssetInfo(monitorBssVulnDeal);

        String cycleType = monitorBssVulnDeal.getCycleType();
        Date beginTime = null;
        Date endTime = null;

        if ("1".equals(cycleType)) {
            beginTime = DateUtil.beginOfWeek(new Date());
            endTime = DateUtil.endOfWeek(new Date());
        } else if ("2".equals(cycleType)) {
            List<Date> currentMonthDates = getCurrentMonthDates();
            beginTime = DateUtil.beginOfMonth(currentMonthDates.get(0));
            endTime = DateUtil.endOfMonth(currentMonthDates.get(currentMonthDates.size() - 1));
        } else if ("3".equals(cycleType)) {
            List<Date> startDates = getCurrentQuarterMonthStartDates();
            beginTime = DateUtil.beginOfMonth(startDates.get(0));
            endTime = DateUtil.endOfMonth(startDates.get(startDates.size() - 1));
        } else if ("4".equals(cycleType)) {
            List<Date> startDates = getYearlyMonthStartDates();
            beginTime = DateUtil.beginOfYear(startDates.get(0));
            endTime = DateUtil.endOfYear(startDates.get(startDates.size() - 1));
        } else {
            return AjaxResult.error("无效的周期类型");
        }

        disposeWebAndIpAndWeakIP(monitorBssVulnDeal, result, beginTime, endTime);
        return AjaxResult.success(result);
    }

    private void disposeWebAndIpAndWeakIP(MonitorBssVulnDeal monitorBssVulnDeal, JSONObject result, Date beginTime, Date comeToAnEndTime){
        monitorBssVulnDeal.setStartTime(beginTime);
        monitorBssVulnDeal.setEndTime(comeToAnEndTime);

        // 抽象处理逻辑
        processVulnerabilityData(monitorBssVulnDeal, result, "hostVulnerabilityDataAggregation", monitorBssVulnDealService::getHostVulnerabilityDataAggregation);
        processVulnerabilityData(monitorBssVulnDeal, result, "webVulnerabilityDataAggregation", monitorBssVulnDealService::getWebVulnerabilityDataAggregation);
        processVulnerabilityData(monitorBssVulnDeal, result, "weakIPPasswordsDataAggregation", monitorBssVulnDealService::getWeakIpPasswordsDataAggregation);
    }

    private void processVulnerabilityData(MonitorBssVulnDeal deal, JSONObject result, String key, Function<MonitorBssVulnDeal, JSONObject> dataFetcher) {
        JSONObject data = dataFetcher.apply(deal);
        Integer total = data.getInteger("total");
        Integer disposalNum = data.getInteger("disposalNum");

        if (total != null && disposalNum != null && total > 0) {
            double percentage = ((double) disposalNum / total) * 100;
            String formattedPercentage = String.format("%.1f", percentage);
            data.put("percentage", formattedPercentage + "%");
        } else {
            data.put("percentage", "0%");
        }

        result.put(key, data);
    }


    /**
     * 漏洞等级统计
     * */
    @GetMapping("/getInnerRiskLevelStatistics")
    public AjaxResult getInnerRiskLevelStatistics(MonitorBssVulnDeal monitorBssVulnDeal) {
        // 设置默认部门ID
        if (monitorBssVulnDeal.getDeptId() == null) {
            monitorBssVulnDeal.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
        }
        // 特殊处理：部门ID为100时设为null
        if (monitorBssVulnDeal.getDeptId() == 100) {
            monitorBssVulnDeal.setDeptId(null);
        }

        JSONObject result = new JSONObject();
        handleAssetInfo(monitorBssVulnDeal);

        String cycleType = monitorBssVulnDeal.getCycleType();
        if (cycleType == null) {
            return AjaxResult.success(result); // CycleType为空时不处理
        }

        Date beginTime = null;
        Date endTime = null;

        Date now = new Date();

        switch (cycleType) {
            case "1": // 本周
                beginTime = DateUtil.beginOfWeek(now);
                endTime = DateUtil.endOfWeek(now);
                break;
            case "2": // 本月
                List<Date> currentMonthDates = getCurrentMonthDates();
                if (!currentMonthDates.isEmpty()) {
                    beginTime = DateUtil.beginOfMonth(currentMonthDates.get(0));
                    endTime = DateUtil.endOfMonth(currentMonthDates.get(currentMonthDates.size() - 1));
                }
                break;
            case "3": // 本季度
                List<Date> quarterStartDates = getCurrentQuarterMonthStartDates();
                if (!quarterStartDates.isEmpty()) {
                    beginTime = DateUtil.beginOfMonth(quarterStartDates.get(0));
                    endTime = DateUtil.endOfMonth(quarterStartDates.get(quarterStartDates.size() - 1));
                }
                break;
            case "4": // 本年
                List<Date> yearlyStartDates = getYearlyMonthStartDates();
                if (!yearlyStartDates.isEmpty()) {
                    beginTime = DateUtil.beginOfYear(yearlyStartDates.get(0));
                    endTime = DateUtil.endOfYear(yearlyStartDates.get(yearlyStartDates.size() - 1));
                }
                break;
            default:
                // 不支持的周期类型，直接返回空结果
                return AjaxResult.success(result);
        }

        if (beginTime != null && endTime != null) {
            handleVulnerabilityLevelStatistics(monitorBssVulnDeal, result, beginTime, endTime);
        }

        return AjaxResult.success(result);
    }

    /**
     * 风险系统排名
     * */
    @GetMapping("/getRiskSystemRanking")
    public AjaxResult getRiskSystemRanking(MonitorBssVulnDeal monitorBssVulnDeal) {
        if (monitorBssVulnDeal.getDeptId() == null){
            monitorBssVulnDeal.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
        }
        if (monitorBssVulnDeal.getDeptId() == 100){
            monitorBssVulnDeal.setDeptId(null);
        }
        String cycleType = monitorBssVulnDeal.getCycleType();
        Date beginTime;
        Date endTime;

        if ("1".equals(cycleType)) {
            // 本周
            beginTime = DateUtil.beginOfWeek(new Date());
            endTime = DateUtil.endOfWeek(new Date());
        } else if ("2".equals(cycleType)) {
            // 本月
            List<Date> currentMonthDates = getCurrentMonthDates();
            beginTime = DateUtil.beginOfMonth(currentMonthDates.get(0));
            endTime = DateUtil.endOfMonth(currentMonthDates.get(currentMonthDates.size() - 1));
        } else if ("3".equals(cycleType)) {
            // 本季度
            List<Date> startDates = getCurrentQuarterMonthStartDates();
            beginTime = DateUtil.beginOfMonth(startDates.get(0));
            endTime = DateUtil.endOfMonth(startDates.get(startDates.size() - 1));
        } else if ("4".equals(cycleType)) {
            // 本年
            List<Date> startDates = getYearlyMonthStartDates();
            beginTime = DateUtil.beginOfYear(startDates.get(0));
            endTime = DateUtil.endOfYear(startDates.get(startDates.size() - 1));
        } else {
            // 非法cycleType处理
            return AjaxResult.error("无效的周期类型");
        }

        monitorBssVulnDeal.setStartTime(beginTime);
        monitorBssVulnDeal.setEndTime(endTime);

        List<JSONObject> riskSystemRanking = monitorBssVulnDealService.getRiskSystemRanking(monitorBssVulnDeal);
        if (CollUtil.isNotEmpty(riskSystemRanking)){
            //删除riskSystemRanking中total_risk等于0的数据
            riskSystemRanking.removeIf(item -> item.getInteger("total_risk") == 0);
        }
        JSONObject result = new JSONObject();
        result.put("riskSystemRanking", riskSystemRanking == null ? Collections.EMPTY_LIST : riskSystemRanking);

        return AjaxResult.success(result);
    }


    /**
     * 主机漏洞周期数据汇总
     * */
    @GetMapping("/riskStatisticsRanking")
    public AjaxResult riskStatisticsRanking(MonitorBssVulnDeal monitorBssVulnDeal) {
        // 参数校验
        if (monitorBssVulnDeal == null) {
            return AjaxResult.error("参数不能为空");
        }

        // 设置部门ID逻辑
        if (monitorBssVulnDeal.getDeptId() == null) {
            monitorBssVulnDeal.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
        }
        if (monitorBssVulnDeal.getDeptId() == 100) {
            monitorBssVulnDeal.setDeptId(null);
        }

        JSONObject result = new JSONObject();
        handleAssetInfo(monitorBssVulnDeal);

        String cycleType = monitorBssVulnDeal.getCycleType();
        if (cycleType == null) {
            return AjaxResult.error("周期类型不能为空");
        }

        List<Date> dates = null;
        Date beginTime = null;
        Date endTime = null;

        try {
            switch (cycleType) {
                case "1": // 本周
                    dates = getWeekDates(new Date());
                    beginTime = DateUtil.beginOfWeek(new Date());
                    endTime = DateUtil.endOfWeek(new Date());
                    break;
                case "2": // 本月
                    dates = getCurrentMonthDates();
                    beginTime = DateUtil.beginOfMonth(dates.get(0));
                    endTime = DateUtil.endOfMonth(dates.get(dates.size() - 1));
                    break;
                case "3": // 本季度
                    dates = getCurrentQuarterMonthStartDates();
                    beginTime = DateUtil.beginOfMonth(dates.get(0));
                    endTime = DateUtil.endOfMonth(dates.get(dates.size() - 1));
                    break;
                case "4": // 本年
                    dates = getYearlyMonthStartDates();
                    beginTime = DateUtil.beginOfYear(dates.get(0));
                    endTime = DateUtil.endOfYear(dates.get(dates.size() - 1));
                    break;
                default:
                    return AjaxResult.error("不支持的周期类型");
            }

            Map<String, Integer> cycleRiskOverview = new LinkedHashMap<>();
            disposeRiskStatisticsRanking(monitorBssVulnDeal, dates, cycleRiskOverview, beginTime, endTime, result, cycleType);
        } catch (Exception e) {
            // 异常捕获，防止服务崩溃
            return AjaxResult.error("数据处理异常，请稍后重试");
        }

        return AjaxResult.success(result);
    }

    /**
     * web漏洞数据汇总
     * */
    @GetMapping("/summaryOfWebVulnerabilityData")
    public AjaxResult summaryOfWebVulnerabilityData(MonitorBssVulnDeal monitorBssVulnDeal) {
        if (monitorBssVulnDeal.getDeptId() == null){
            monitorBssVulnDeal.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
        }
        if (monitorBssVulnDeal.getDeptId() == 100){
            monitorBssVulnDeal.setDeptId(null);
        }
        JSONObject result = new JSONObject();
        //handleAssetInfo(monitorBssVulnDeal);
        //周期web漏洞总览
        Map<String,Integer> cycleRiskOverview = new LinkedHashMap<>();
        TblThreatenAlarm tblThreatenAlarm = new TblThreatenAlarm();
        tblThreatenAlarm.setDeptId(monitorBssVulnDeal.getDeptId());
        if ("1".equals(monitorBssVulnDeal.getCycleType())){
            List<Date> weekDates = getWeekDates(new Date());
            // 获取本周的开始时间和结束时间
            Date beginTime = DateUtil.beginOfWeek(new Date());
            Date comeToAnEndTime = DateUtil.endOfWeek(new Date());
            handleWebSummaryOfVulnerabilityData(monitorBssVulnDeal,weekDates,beginTime,comeToAnEndTime,tblThreatenAlarm,cycleRiskOverview,result,"1");
        } else if ("2".equals(monitorBssVulnDeal.getCycleType())) {
            // 表示为本月
            List<Date> currentMonthDates = getCurrentMonthDates();
            Date beginTime = DateUtil.beginOfMonth(currentMonthDates.get(0));
            Date comeToAnEndTime = DateUtil.endOfMonth(currentMonthDates.get(currentMonthDates.size() - 1));
            handleWebSummaryOfVulnerabilityData(monitorBssVulnDeal,currentMonthDates,beginTime,comeToAnEndTime,tblThreatenAlarm,cycleRiskOverview,result,"2");
        } else if ("3".equals(monitorBssVulnDeal.getCycleType())) {
            // 表示为本季度
            List<Date> startDates = getCurrentQuarterMonthStartDates();
            Date beginTime = DateUtil.beginOfMonth(startDates.get(0));
            Date comeToAnEndTime = DateUtil.endOfMonth(startDates.get(startDates.size() - 1));
            handleWebSummaryOfVulnerabilityData(monitorBssVulnDeal,startDates,beginTime,comeToAnEndTime,tblThreatenAlarm,cycleRiskOverview,result,"3");
        } else if ("4".equals(monitorBssVulnDeal.getCycleType())) {
            // 表示为本年
            List<Date> startDates = getYearlyMonthStartDates();
            Date beginTime = DateUtil.beginOfYear(startDates.get(0));
            Date comeToAnEndTime = DateUtil.endOfYear(startDates.get(startDates.size() - 1));
            handleWebSummaryOfVulnerabilityData(monitorBssVulnDeal,startDates,beginTime,comeToAnEndTime,tblThreatenAlarm,cycleRiskOverview,result,"4");
        }
        return AjaxResult.success(result);
    }

    public void handleWebSummaryOfVulnerabilityData(MonitorBssVulnDeal monitorBssVulnDeal,List<Date> weekDates,
                                                    Date beginTime,Date comeToAnEndTime,TblThreatenAlarm tblThreatenAlarm,
                                                    Map<String,Integer> cycleRiskOverview,JSONObject result,String cycleType) {
        monitorBssVulnDeal.setStartTime(beginTime);
        monitorBssVulnDeal.setEndTime(comeToAnEndTime);
        AtomicInteger count = new AtomicInteger();
        weekDates.forEach(date -> {
            Date startOfDay = DateUtil.beginOfDay(date);
            Date endOfDay = DateUtil.endOfDay(date);
            tblThreatenAlarm.setStartTime(startOfDay);
            tblThreatenAlarm.setEndTime(endOfDay);
            String format = "";
            if ("1".equals(cycleType)){
                format = DateUtil.format(date, "MM-dd");
            }else if ("2".equals(cycleType)){
                format = DateUtil.format(date, "MM-dd");
            } else if ("3".equals(cycleType) || "4".equals(cycleType)) {
                Date monthStart = DateUtil.beginOfMonth(date);
                Date monthEnd = DateUtil.endOfMonth(date);
                startOfDay = monthStart;
                endOfDay = monthEnd;
                format = DateUtil.format(date, "yyyy-MM");
            }
            tblThreatenAlarm.setStartTime(startOfDay);
            tblThreatenAlarm.setEndTime(endOfDay);
            JSONObject cycleWebVulnerabilityDataAggregation = monitorBssVulnDealService.webVulnerabilityDataAggregation(tblThreatenAlarm);
            if (cycleWebVulnerabilityDataAggregation != null){
                Integer hostRiskNum = cycleWebVulnerabilityDataAggregation.getInteger("webVulnerabilitiesNum");
                count.addAndGet(hostRiskNum);
                cycleRiskOverview.put(DateUtil.format(date, format), hostRiskNum);
            }else {
                cycleRiskOverview.put(DateUtil.format(date, format), 0);
            }
        });
        tblThreatenAlarm.setStartTime(beginTime);
        tblThreatenAlarm.setEndTime(comeToAnEndTime);
        // web漏洞类型统计
        List<JSONObject> webVulnerabilityTypeStatistics = monitorBssVulnDealService.webVulnerabilityTypeStatistics(tblThreatenAlarm);
        // web漏洞排名
        List<JSONObject> webVulnerabilityRanking = monitorBssVulnDealService.webVulnerabilityRanking(tblThreatenAlarm);
        result.put("webLeakTotal",count.get()); // web漏洞总数
        result.put("cycleRiskOverview",cycleRiskOverview); //周期漏洞总览
        result.put("webVulnerabilityTypeStatistics",webVulnerabilityTypeStatistics); // web漏洞类型统计
        result.put("webVulnerabilityRanking",webVulnerabilityRanking); // web漏洞排名
    }

    public void disposeRiskStatisticsRanking(MonitorBssVulnDeal monitorBssVulnDeal, List<Date> weekDates,
                                             Map<String,Integer> cycleRiskOverview, Date beginTime, Date comeToAnEndTime, JSONObject result,  String type){
        monitorBssVulnDeal.setStartTime(beginTime);
        monitorBssVulnDeal.setEndTime(comeToAnEndTime);
        handleAssetInfo(monitorBssVulnDeal);
        //主机漏洞类型统计
        List<JSONObject> hostVulnerabilityTypeCount = monitorBssVulnDealService.getInnerRiskTypeData(monitorBssVulnDeal);
        //主机漏洞排名
        List<JSONObject> hostVulnerabilityRanking = monitorBssVulnDealService.getInnerRiskRankingData(monitorBssVulnDeal);
        TblThreatenAlarm tblThreatenAlarm = new TblThreatenAlarm();
        tblThreatenAlarm.setDeptId(monitorBssVulnDeal.getDeptId());
        AtomicInteger count = new AtomicInteger();
        weekDates.forEach(date -> {
            Date startOfDay = DateUtil.beginOfDay(date);
            Date endOfDay = DateUtil.endOfDay(date);
            tblThreatenAlarm.setStartTime(startOfDay);
            tblThreatenAlarm.setEndTime(endOfDay);
            String format = "";
            if ("1".equals(type)){
                format = DateUtil.format(date, "MM-dd");
            }else if ("2".equals(type)){
                format = DateUtil.format(date, "MM-dd");
            } else if ("3".equals(type) || "4".equals(type)) {
                Date monthStart = DateUtil.beginOfMonth(date);
                Date monthEnd = DateUtil.endOfMonth(date);
                startOfDay = monthStart;
                endOfDay = monthEnd;
                format = DateUtil.format(date, "yyyy-MM");
            }
            tblThreatenAlarm.setStartTime(startOfDay);
            tblThreatenAlarm.setEndTime(endOfDay);
            monitorBssVulnDeal.setStartTime(startOfDay);
            monitorBssVulnDeal.setEndTime(endOfDay);
            JSONObject cycleIpVulnerabilityDataAggregation = monitorBssVulnDealService.getInnerRiskHeaderData(monitorBssVulnDeal);
            if (cycleIpVulnerabilityDataAggregation != null){
                Integer hostRiskNum = cycleIpVulnerabilityDataAggregation.getInteger("hostRiskNum");
                count.addAndGet(hostRiskNum);
                cycleRiskOverview.put(DateUtil.format(date, format), hostRiskNum);
            }else {
                cycleRiskOverview.put(DateUtil.format(date, format), 0);
            }
        });
        result.put("leakTotal", count); // 漏洞总数
        result.put("cycleRiskOverview", cycleRiskOverview); //周期数据统计
        result.put("hostVulnerabilityTypeCount", hostVulnerabilityTypeCount); //漏洞类型统计
        result.put("hostVulnerabilityRanking", hostVulnerabilityRanking); //漏洞排名
    }


    public void handleVulnerabilityLevelStatistics(MonitorBssVulnDeal monitorBssVulnDeal, JSONObject result, Date beginTime, Date comeToAnEndTime){
        monitorBssVulnDeal.setStartTime(beginTime);
        monitorBssVulnDeal.setEndTime(comeToAnEndTime);
        // 主机风险
        List<JSONObject> hostRisk = monitorBssVulnDealService.getTheTypeOfVulnerabilityOfTheHost(monitorBssVulnDeal);
        // Web 风险
        List<JSONObject> webRisk = monitorBssWebvulnDealService.getStatisticsOnWebVulnerabilityTypes(monitorBssVulnDeal);
        result.put("hostRisk",hostRisk);
        result.put("webRisk",webRisk);
    }

    private List<NetworkIpMacInfo> handleAssetInfo(MonitorBssVulnDeal monitorBssVulnDeal){
        //查询netIpMac
        JSONObject queryAssetInfoParams = new JSONObject();
        queryAssetInfoParams.put("domainId",monitorBssVulnDeal.getDomainId());
        queryAssetInfoParams.put("deptId",monitorBssVulnDeal.getDeptId());
        queryAssetInfoParams.put("applicationId",monitorBssVulnDeal.getApplicationId());
        List<NetworkIpMacInfo> assetInfoList = netIpMacService.selectAssetInfoList(queryAssetInfoParams);
        if(CollUtil.isNotEmpty(assetInfoList)){
            monitorBssVulnDeal.setIpv4List(assetInfoList.stream().map(NetworkIpMacInfo::getIpv4).collect(Collectors.toList()));
            //查询业务系统
            List<Long> businessAssetIds = new ArrayList<>();
            assetInfoList.forEach(assetInfo -> {
                String assetIdList = assetInfo.getAssetIdList();
                if(StrUtil.isNotBlank(assetIdList)){
                    businessAssetIds.addAll(StrUtil.split(assetIdList, ",").stream().map(Long::valueOf).collect(Collectors.toList()));
                }
            });
            if(CollUtil.isNotEmpty(businessAssetIds)){
                List<TblBusinessApplication> applicationList = businessApplicationService.selectByAssetIds(CollUtil.distinct(businessAssetIds));
                if(CollUtil.isNotEmpty(applicationList)){
                    assetInfoList.forEach(assetInfo -> {
                        String assetIdList = assetInfo.getAssetIdList();
                        if(StrUtil.isNotBlank(assetIdList)){
                            List<TblBusinessApplication> matchAppList = applicationList.stream().filter(application -> assetIdList.contains(application.getAssetId().toString())).collect(Collectors.toList());
                            assetInfo.setBusinessApplications(matchAppList);
                        }
                    });
                }
            }
        }

        boolean isAll = false;
        LoginUser loginUser = getLoginUser();
        if(loginUser != null){
            SysUser user = loginUser.getUser();
            isAll = user.haveAllData();
        }
        if(CollUtil.isEmpty(monitorBssVulnDeal.getIpv4List()) && !isAll){
            monitorBssVulnDeal.setIpv4List(CollUtil.toList("noIP"));
        }
        if((monitorBssVulnDeal.getApplicationId() == null && monitorBssVulnDeal.getDeptId() == null && StrUtil.isBlank(monitorBssVulnDeal.getDomainId())) && isAll){
            monitorBssVulnDeal.setIpv4List(null);
        }
        return assetInfoList;
    }

    public List<Date> getWeekDates(Date date) {
        // 获取本周周一至周日的日期列表
        List<DateTime> dateTimes = DateUtil.rangeToList(
                DateUtil.beginOfWeek(date),
                DateUtil.endOfWeek(date),
                DateField.DAY_OF_WEEK
        );
        // 将日期列表转换为Date类型并返回
        return dateTimes.stream()
                .map(DateTime::toJdkDate)
                .collect(Collectors.toList());
    }

    public List<Date> getCurrentMonthDates() {
        YearMonth currentYearMonth = YearMonth.now();
        int daysInMonth = currentYearMonth.lengthOfMonth();

        List<Date> dateList = new ArrayList<>();
        for (int day = 1; day <= daysInMonth; day++) {
            LocalDate localDate = currentYearMonth.atDay(day);
            Date date = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            dateList.add(date);
        }
        return dateList;
    }

    public static List<Date> getYearlyMonthStartDates() {
        int year = LocalDate.now().getYear();
        List<Date> startDates = new ArrayList<>();

        for (int month = 1; month <= 12; month++) {
            LocalDate firstDayOfMonth = LocalDate.of(year, month, 1);
            Date date = Date.from(firstDayOfMonth.atStartOfDay(ZoneId.systemDefault()).toInstant());
            startDates.add(date);
        }

        return startDates;
    }

    public static List<Date> getCurrentQuarterMonthStartDates() {
        LocalDate today = LocalDate.now();
        int year = today.getYear();
        int month = today.getMonthValue();
        // 计算当前季度（1~4）
        int quarter = (month - 1) / 3 + 1;
        // 当前季度的第一个月
        int startMonth = (quarter - 1) * 3 + 1;
        List<LocalDate> startDates = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            LocalDate firstDayOfMonth = LocalDate.of(year, startMonth + i, 1);
            startDates.add(firstDayOfMonth);
        }
        //将startDates转换为Data类型
        List<Date> dateList = startDates.stream()
                .map(localDate -> Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant()))
                .collect(Collectors.toList());
        return dateList;
    }
}
