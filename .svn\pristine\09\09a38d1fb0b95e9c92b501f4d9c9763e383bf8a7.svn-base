package com.ruoyi.threaten.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.safe.domain.TblBusinessApplication;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 蜜罐告警对象 tbl_honeypot_alarm
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TblHoneypotAlarm extends TblThreatenAlarm {
    private String playload;

    private Long deviceConfigId;
}

