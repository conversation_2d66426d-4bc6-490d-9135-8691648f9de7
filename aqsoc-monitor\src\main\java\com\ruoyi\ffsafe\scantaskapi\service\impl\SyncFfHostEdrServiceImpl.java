package com.ruoyi.ffsafe.scantaskapi.service.impl;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.extra.spring.SpringUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.ffsafe.api.domain.FfsafeApiConfig;
import com.ruoyi.ffsafe.api.domain.TblDeviceConfig;
import com.ruoyi.ffsafe.api.service.ITblDeviceConfigService;
import com.ruoyi.ffsafe.scantaskapi.domain.*;
import com.ruoyi.ffsafe.scantaskapi.service.ISyncFfHostEdrService;
import com.ruoyi.ffsafe.scantaskapi.service.mapper.FfsafeSoftwareDetailsResultMapper;
import com.ruoyi.monitor2.changting.client.FfsafeClientService;
import com.ruoyi.monitor2.changting.client.SkipHttpsUtil;
import com.ruoyi.monitor2.domain.TblProduct;
import com.ruoyi.monitor2.mapper.TblProductMapper;
import com.ruoyi.safe.domain.TblDeploy;
import com.ruoyi.safe.domain.TblNetworkIpMac;
import com.ruoyi.safe.domain.TblServer;
import com.ruoyi.safe.mapper.TblDeployMapper;
import com.ruoyi.safe.mapper.TblNetworkIpMacMapper;
import com.ruoyi.safe.mapper.TblServerMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Date;
import java.util.List;

/**
 * @projectName: aqsoc
 * @author: taoyc
 * @date: 2025年01月23日 下午 3:05
 * @version: 1.0
 */
@Slf4j
@Service
public class SyncFfHostEdrServiceImpl implements ISyncFfHostEdrService {

    @Autowired
    private EdrAllTaskParam edrAllTaskParam;
    @Autowired
    private EdrDetailParam edrDetailParam;
    @Resource
    private TblNetworkIpMacMapper tblNetworkIpMacMapper;

    @Resource
    private TblServerMapper tblServerMapper;

    @Resource
    private FfsafeSoftwareDetailsResultMapper ffsafeSoftwareDetailsResultMapper;

    @Resource
    private TblProductMapper tblProductMapper;
    @Resource
    private TblDeployMapper tblDeployMapper;
    @Autowired
    private Snowflake snowflake;
    @Override
    public void syncFfHostEdrData()  throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        CloseableHttpClient httpClient = null;
        HttpResponse response = null;
        try {
            httpClient = (CloseableHttpClient) SkipHttpsUtil.wrapClient();
            ITblDeviceConfigService deviceConfigService = SpringUtil.getBean(ITblDeviceConfigService.class);
            TblDeviceConfig deviceConfig = deviceConfigService.selectDeviceConfigOrDefault(edrAllTaskParam.getDeviceConfigId());
            if(deviceConfig == null){
                log.error("同步EDR找不到对应的设备配置");
                return;
            }
            FfsafeClientService.deviceConfigThreadLocal.set(deviceConfig);
            response = httpClient.execute(edrAllTaskParam.getRequestBase(edrAllTaskParam.getDeviceConfigId()));
            if (response != null) {
                HttpEntity resEntity = response.getEntity();
                if (resEntity != null) {
                    String result = EntityUtils.toString(resEntity, "utf-8");
                    log.info("ffHostEdrSyncTask param: " + edrAllTaskParam.toString() + " result: " + result);
                    List<EdrAllTaskResult> taskResult = objectMapper.readValue(result, new TypeReference<List<EdrAllTaskResult>>() {});
                    // 先解除非凡绑定再进行匹配绑定，解决EDR撤销问题
                    tblServerMapper.updateTblServerFeifan();
                    // 根据IP查询 tbl_network_ip_mac 查询主ip为1 ip和非凡匹配的assetId 在tbl_server表绑定serial字段
                    taskResult.forEach(e -> {
                        TblNetworkIpMac tblNetworkIpMac = new TblNetworkIpMac();
                        tblNetworkIpMac.setIpv4(e.getClientIp());
                        tblNetworkIpMac.setMainIp("1");  // 表示主ip
                        List<TblNetworkIpMac> tblNetworkIpMacList = tblNetworkIpMacMapper.selectTblNetworkIpMacList(tblNetworkIpMac);
                        if ((tblNetworkIpMacList != null) && (tblNetworkIpMacList.size() == 1)) {
                            // 只有一个资产主机匹配时进行在tbl_server表绑定serial字段
                            TblServer tblServer = tblServerMapper.selectTblServerByAssetId(tblNetworkIpMacList.get(0).getAssetId());
                            tblServer.setSerial(e.getSerial());
                            tblServer.setSystemName(e.getOsPlatform());
                            tblServerMapper.updateTblServer(tblServer);

                            // 如果操作系统不存在则更新标准程序软件库（tbl_product），存在则获取标准程序软件库ID 并与部署表（tbl_deploy）对比没有则在部署表中新增，不同则更新部署表 一致则不做操作
                            if (!StringUtils.isEmpty(e.getOsPlatform())) {
                                TblProduct tblProduct = tblProductMapper.selectTblProductByProcName(e.getOsPlatform());
                                if (StringUtils.isNotNull(tblProduct)) {
                                    // 查询tbl_deploy
                                    TblDeploy tblDeploy = tblDeployMapper.selectTblDeployByAssetId(tblNetworkIpMacList.get(0).getAssetId());
                                    if (StringUtils.isNotNull(tblDeploy)) {
                                        if (!tblDeploy.getPrdid().equals(tblProduct.getPrdid())) {
                                            tblDeploy.setPrdid(tblProduct.getPrdid());
                                            tblDeploy.setProcName(tblProduct.getProcName());
                                            tblDeployMapper.updateTblDeploy(tblDeploy);
                                        }
                                    } else {
                                        TblDeploy tblDeploy1 = new TblDeploy();
                                        tblDeploy1.setDeployId(snowflake.nextId());
                                        tblDeploy1.setPrdid(tblProduct.getPrdid());
                                        tblDeploy1.setProcName(tblProduct.getProcName());
                                        tblDeploy1.setSoftlx("1");
                                        tblDeploy1.setDeployType("system");
                                        tblDeploy1.setAssetId(tblNetworkIpMacList.get(0).getAssetId());
                                        tblDeploy1.setAssetName(tblNetworkIpMacList.get(0).getAssetName());
                                        tblDeploy1.setCreateTime(DateUtils.getNowDate());
                                        tblDeployMapper.insertTblDeploy(tblDeploy1);
                                    }
                                } else {
                                    tblProduct = new TblProduct();
                                    tblProduct.setPrdid(String.valueOf(snowflake.nextId()));
                                    tblProduct.setPubTime(new Date());
                                    tblProduct.setProcName(e.getOsPlatform());
                                    tblProduct.setProcType("system");
                                    tblProductMapper.insertTblProduct(tblProduct);
                                }
                            }

                        }
                    });
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("ffHostEdrSyncTask 接口调用错误: " + e.getMessage());
            throw e;
        } finally {
            try {
                if (httpClient != null) {
                    httpClient.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
                log.error("ffHostEdrSyncTask 接口调用错误: " + e.getMessage());
                throw e;
            }
        }
    }

    @Override
    public void syncFfHostEdrDetails(TblServer tblServer) throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        CloseableHttpClient httpClient = null;
        HttpResponse response = null;
        edrDetailParam.parseParam(tblServer.getSerial());
        try {
            httpClient = (CloseableHttpClient) SkipHttpsUtil.wrapClient();
            response = httpClient.execute(edrDetailParam.getRequestBase(edrDetailParam.getDeviceConfigId()));
            if (response != null) {
                HttpEntity resEntity = response.getEntity();
                if (resEntity != null) {
                    String result = EntityUtils.toString(resEntity, "utf-8");
                    log.info("host_detail_by_serial param: " + edrDetailParam.toString() + " result: " + result);
                    EdrDetailsResult taskResult = objectMapper.readValue(result, EdrDetailsResult.class);
                    // 更新tbl_server表CPU架构（cpu_frame）
                    if (StringUtils.isEmpty(tblServer.getCpuFrame())) {
                        tblServer.setCpuFrame(taskResult.getHardwareDetails().getBasicDetails().getCpuModel());
                        tblServerMapper.updateTblServer(tblServer);
                    }
                    // 更新tbl_network_ip_mac表mac（mac） 网关 网卡名 ipv6
                    TblNetworkIpMac tblNetworkIpMac = new TblNetworkIpMac();
                    tblNetworkIpMac.setAssetId(tblServer.getAssetId());
                    List<TblNetworkIpMac> ipMacList = tblNetworkIpMacMapper.selectTblNetworkIpMacList(tblNetworkIpMac);
                    if ((ipMacList != null) && (ipMacList.size() > 0)) {
                        ipMacList.forEach(e -> {
                            if (StringUtils.isEmpty(e.getMac())) {
                                e.setMac(taskResult.getHardwareDetails().getNetcardDetails().getNetcardMac());
                            }
//                            if (StringUtils.isEmpty(e.getIpv6())) {
//                                e.setIpv6(taskResult.getHardwareDetails().getNetcardDetails().getNetcardIpv6());
//                            }
                            if (StringUtils.isEmpty(e.getDefaultGateway())) {
                                e.setDefaultGateway(taskResult.getHardwareDetails().getNetcardDetails().getNetcardGateway());
                            }
                            if (StringUtils.isEmpty(e.getNetworkCardName())) {
                                e.setNetworkCardName(taskResult.getHardwareDetails().getNetcardDetails().getNetcardName());
                            }
                            tblNetworkIpMacMapper.updateScheduleInfo(e);
                        });
                    }
                    // 更新ffsafe_software_details表先删除再新增
                    // 先查询是否有数据没有则新增 有则先删除在新增
                    FfsafeSoftwareDetails ffsafeSoftwareDetails = new FfsafeSoftwareDetails();
                    ffsafeSoftwareDetails.setAssetId(tblServer.getAssetId());
                    ffsafeSoftwareDetails.setSerial(tblServer.getSerial());
                    List<FfsafeSoftwareDetails> ffsafeSoftwareDetailsList = ffsafeSoftwareDetailsResultMapper.selectFfsafeSoftwareDetailsList(ffsafeSoftwareDetails);
                    if ((ffsafeSoftwareDetailsList != null) && (ffsafeSoftwareDetailsList.size() > 0)) {
                        // 删除
                        ffsafeSoftwareDetailsResultMapper.deleteFfsafeSoftwareDetailsBySerial(tblServer.getSerial());
                    }
                    // 新增
                    taskResult.getSoftwareDetailsList().forEach(s -> {
                        FfsafeSoftwareDetails ffsafeSoftware = new FfsafeSoftwareDetails();
                        ffsafeSoftware.setSerial(tblServer.getSerial());
                        ffsafeSoftware.setAssetId(tblServer.getAssetId());
                        ffsafeSoftware.setSoftwareName(s.getN());
                        ffsafeSoftware.setSoftwareVersion(s.getN());
                        ffsafeSoftware.setSoftwarePublisher(s.getP());
                        ffsafeSoftwareDetailsResultMapper.insertFfsafeSoftwareDetails(ffsafeSoftware);
                    });
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("host_detail_by_serial 接口调用错误: " + e.getMessage());
            throw e;
        } finally {
            try {
                if (httpClient != null) {
                    httpClient.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
                log.error("host_detail_by_serial 接口调用错误: " + e.getMessage());
                throw e;
            }
        }
    }
}
