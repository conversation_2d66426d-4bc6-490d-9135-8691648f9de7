{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\monitor\\leakyRecord.vue?vue&type=style&index=0&id=f8fdff14&scoped=true&lang=scss", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\monitor\\leakyRecord.vue", "mtime": 1755591292557}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751956516551}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751956543892}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751956526179}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751956513748}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCkBpbXBvcnQgIkAvYXNzZXRzL3N0eWxlcy9hc3NldEluZGV4LnNjc3MiOwoucG9saWN5Q29sIHsKICBtaW4td2lkdGg6IDMzMHB4OwogIG1hcmdpbi10b3A6IDEwcHg7Cn0KCi5wb2xpY3lEZXNjIHsKICBkaXNwbGF5OiBmbGV4OwogIGhlaWdodDogODBweDsKfQoKLnBvbGljeVR4dCB7CiAgbWFyZ2luLWxlZnQ6IDEwcHg7CiAgbGluZS1oZWlnaHQ6IDIwcHg7Cn0KCi5wb2xpY3lUaXRsZSB7CiAgaGVpZ2h0OiA0MHB4OwogIGxpbmUtaGVpZ2h0OiA0MHB4Owp9Cgoub25lTGluZSB7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKICB3aGl0ZS1zcGFjZTogbm93cmFwOwogIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOwp9Cgo6OnYtZGVlcCAuZWwtdGFibGUgewogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKfQoKOjp2LWRlZXAgLmVsLXRhYmxlX19ib2R5LXdyYXBwZXIgewogIG92ZXJmbG93LXk6IGF1dG87CiAgZmxleDogMTsKfQo="}, {"version": 3, "sources": ["leakyRecord.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwXA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "leakyRecord.vue", "sourceRoot": "src/views/frailty/monitor", "sourcesContent": ["<template>\n  <div class=\"custom-container\">\n    <div class=\"custom-content-container-right\">\n      <div class=\"custom-content-search-box\">\n        <el-form\n          :model=\"queryParams\"\n          ref=\"queryForm\"\n          size=\"small\"\n          :inline=\"true\"\n          label-position=\"right\"\n          label-width=\"70px\"\n        >\n          <el-row :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"任务名称\" prop=\"jobName\">\n                <el-input\n                  v-model=\"queryParams.jobName\"\n                  placeholder=\"请输入任务名称\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"扫描目标\" prop=\"invokeTarget\">\n                <el-input\n                  v-model=\"queryParams.scanTagert\"\n                  placeholder=\"扫描目标\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n<!--            <el-col :span=\"6\">\n              <el-form-item label=\"任务状态\" prop=\"status\">\n                <el-select v-model=\"queryParams.status\" placeholder=\"请选择任务状态\" clearable>\n                  <el-option\n                    v-for=\"dict in dict.type.sys_job_status\"\n                    :key=\"dict.value\"\n                    :label=\"dict.label\"\n                    :value=\"dict.value\"\n                  />\n                </el-select>\n              </el-form-item>\n            </el-col>-->\n            <el-col :span=\"12\">\n              <el-form-item class=\"custom-search-btn\">\n                <el-button class=\"btn1\" size=\"small\" @click=\"handleQuery\">查询</el-button>\n                <el-button class=\"btn2\" size=\"small\" @click=\"resetQuery\">重置</el-button>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n      </div>\n\n      <div class=\"custom-content-container\">\n        <div class=\"common-header\">\n          <div><span class=\"common-head-title\">{{ listType === 4 ? '主机' : 'Web' }}漏扫记录列表</span></div>\n          <div class=\"common-head-right\">\n            <el-row :gutter=\"10\">\n              <el-col :span=\"1.5\">\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  @click=\"handleReportRecord\"\n                >报告生成记录\n                </el-button>\n              </el-col>\n              <el-col :span=\"1.5\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  :disabled=\"multiple\"\n                  @click=\"handleCreateReport\"\n                >批量生成报告\n                </el-button>\n              </el-col>\n            </el-row>\n          </div>\n        </div>\n        <el-table height=\"100%\" v-loading=\"loading\" :data=\"jobList\" ref=\"multipleTable\" @selection-change=\"handleSelectionChange\">\n          <el-table-column type=\"selection\" width=\"55\" />\n          <el-table-column label=\"任务名称\" align=\"left\" prop=\"jobName\"/>\n          <el-table-column label=\"扫描目标\" align=\"left\" prop=\"scanTarget\" width=\"150px\" :show-overflow-tooltip=\"false\"/>\n          <el-table-column label=\"扫描状态\" align=\"left\" prop=\"taskStatus\">\n            <template slot-scope=\"scope\">\n              <el-tag v-if=\"scope.row.taskStatus === 1\">正在调度</el-tag>\n              <el-tag type=\"primary\" v-else-if=\"scope.row.taskStatus === 2\">运行中</el-tag>\n              <el-tag type=\"danger\" v-else-if=\"scope.row.taskStatus === 3\">任务异常</el-tag>\n              <el-tag type=\"success\" v-else-if=\"scope.row.taskStatus === 4\">扫描完成</el-tag>\n            </template>\n          </el-table-column>\n          <el-table-column\n            label=\"扫描进度\"\n            prop=\"finishRate\"\n            width=\"120\"\n            align=\"left\"\n          >\n            <template slot-scope=\"scope\">\n              <el-progress :text-inside=\"true\" :stroke-width=\"18\" :percentage=\"scope.row.finishRate\"></el-progress>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"存活主机\" align=\"left\" prop=\"hostNum\"/>\n          <el-table-column label=\"弱口令\" align=\"left\" prop=\"pwNum\"/>\n          <el-table-column label=\"可入侵漏洞\" align=\"left\" prop=\"pocRiskNum\"/>\n          <el-table-column label=\"高危漏洞\" align=\"left\" prop=\"highRiskNum\"/>\n          <el-table-column label=\"中危漏洞\" align=\"left\" prop=\"lowRiskNum\"/>\n          <el-table-column label=\"低危漏洞\" align=\"left\" prop=\"lowRiskNum\"/>\n          <el-table-column label=\"开始时间\" align=\"left\" prop=\"startTime\"/>\n          <el-table-column label=\"结束时间\" align=\"left\" prop=\"endTime\"/>\n          <el-table-column label=\"操作\" width=\"150\" fixed=\"right\" :show-overflow-tooltip=\"false\">\n            <template slot-scope=\"scope\">\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleView(scope.row)\"\n                v-hasPermi=\"['monitor:ipschedule:query']\"\n              >详情\n              </el-button>\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                :disabled=\"scope.row.currentStatus === 1\"\n                @click=\"handleCreateReport(scope.row)\"\n              >生成报告\n              </el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n        <pagination\n          v-show=\"total>0\"\n          :total=\"total\"\n          :page.sync=\"queryParams.pageNum\"\n          :limit.sync=\"queryParams.pageSize\"\n          @pagination=\"getList\"\n        />\n      </div>\n    </div>\n\n    <el-dialog title=\"报告生成记录\" :visible.sync=\"reportRecordDialogVisible\" width=\"80%\" append-to-body>\n      <div class=\"custom-content-container\">\n        <el-table height=\"100%\" v-loading=\"reportLoading\" :data=\"reportList\" ref=\"reportTable\">\n          <el-table-column label=\"扫描目标\" align=\"left\" prop=\"scanTarget\" />\n          <el-table-column label=\"创建时间\" align=\"left\" prop=\"createTime\" />\n          <el-table-column label=\"生成时间\" align=\"left\" prop=\"generateTime\" />\n          <el-table-column label=\"生成状态\" align=\"left\" prop=\"reportStatus\">\n            <template slot-scope=\"scope\">\n              <el-tag v-if=\"scope.row.reportStatus === 0\">生成中</el-tag>\n              <el-tag type=\"success\" v-else-if=\"scope.row.reportStatus === 1\">已完成</el-tag>\n              <el-tag type=\"danger\" v-else-if=\"scope.row.reportStatus === 2\">失败</el-tag>\n            </template>\n          </el-table-column>\n<!--          <el-table-column label=\"操作\" width=\"150\" fixed=\"right\">\n            <template slot-scope=\"scope\">\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                :disabled=\"scope.row.reportStatus !== 1\"\n                @click=\"downloadReport(scope.row)\"\n              >下载\n              </el-button>\n            </template>\n          </el-table-column>-->\n        </el-table>\n        <pagination\n          v-show=\"reportTotal>0\"\n          :total=\"reportTotal\"\n          :page.sync=\"reportQueryParams.pageNum\"\n          :limit.sync=\"reportQueryParams.pageSize\"\n          @pagination=\"getReportList\"\n        />\n      </div>\n    </el-dialog>\n\n    <!-- 任务日志详细 -->\n    <el-dialog title=\"任务详细\" v-if=\"openView\" :visible.sync=\"openView\" v-dialog-drag width=\"1200px\" append-to-body>\n      <ff-job-tasks  v-if=\"openView\" :jobId=\"jobId\" :job-type=\"jobType\" :job-row=\"editForm\" />\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {batchGenerateReport, getListWithDetails, getReportList} from \"@/api/safe/monitor\";\nimport QuestResultDetails from '../../safe/server/questResultDetails'\nimport LeakScanDialog from '../../safe/server/components/LeakScanDialog'\nimport FfJobTasks from './ffJobTasks'\nimport JobLog from '../../monitor/job/log'\n\nexport default {\n  name: \"hostLeakyRecord\",\n  components: { JobLog, FfJobTasks, LeakScanDialog, QuestResultDetails },\n  dicts: ['sys_job_group', 'sys_job_status'],\n  props: {\n    toParams: {\n      type: Object,\n      default: () => {}\n    },\n    listType: {\n      type: Number,\n      default: 4\n    }\n  },\n  data() {\n    return {\n      jobType: undefined,\n      // 是否显示Cron表达式弹出层\n      openCron: false,\n      // 展示最近一次运行结果\n      // 遮罩层\n      loading: true,\n      // 任务ID\n      jobId: '',\n      totalScan: 0,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 定时任务表格数据\n      jobList: [],\n\n      // 是否显示弹出层\n      open: false,\n      // 是否显示详细弹出层\n      openView: false,\n      editForm: {},\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n      },\n      isDisabled: false,\n      // 周期转换文字\n      cronText: '',\n      rows: [],\n      getListInterval: null,\n      // 报告生成记录相关数据\n      reportRecordDialogVisible: false,\n      reportLoading: false,\n      reportList: [],\n      reportTotal: 0,\n      reportQueryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        taskType: undefined\n      }\n    };\n  },\n  watch: {\n    toParams: {\n      handler(newVal) {\n        if(newVal && newVal.id){\n          this.handleJobLog({\n            jobId: newVal.id\n          });\n        }\n      },\n      immediate: true\n    }\n  },\n  created() {\n    this.getList()\n    this.getListInterval = setInterval(() => {\n      this.loopGetList()\n    }, 5000)\n  },\n  destroyed() {\n    if(this.getListInterval){\n      clearInterval(this.getListInterval);\n    }\n  },\n  methods: {\n    /** 查询主机漏扫记录列表 */\n    getList() {\n      this.loading = true;\n      this.queryParams.taskType = this.listType === 4 ? 2 : 1;\n      getListWithDetails(this.queryParams).then(response => {\n        this.jobList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    loopGetList() {\n      this.queryParams.taskType = this.listType === 4 ? 2 : 1;\n      getListWithDetails(this.queryParams).then(response => {\n        this.jobList = response.rows;\n        this.total = response.total;\n      });\n    },\n\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.jobId);\n      this.single = selection.length != 1;\n      this.multiple = !selection.length;\n      this.rows = selection;\n    },\n\n    /** 批量生成报告 */\n    handleCreateReport(row) {\n      // 单个生成报告\n      if (row.id) {\n        // 单个生成报告\n        batchGenerateReport({\n          ids: [row.id],\n          taskType: this.listType === 4 ? 2 : 1\n        }).then(res => {\n          this.$modal.msgSuccess(\"报告生成任务已提交\");\n        }).catch(err => {\n          this.$modal.msgError(\"报告生成失败\");\n        });\n      } else {\n        // 批量生成报告\n        if (this.rows.length === 0) {\n          this.$modal.msgWarning(\"请先选择要生成报告的记录\");\n          return;\n        }\n        const jobIds = this.rows.map(item => item.id);\n        batchGenerateReport({\n          ids: jobIds,\n          taskType: this.listType === 4 ? 2 : 1\n        }).then(res => {\n          this.$modal.msgSuccess(\"批量报告生成任务已提交\");\n        }).catch(err => {\n          this.$modal.msgError(\"批量报告生成失败\");\n        });\n      }\n\n    },\n\n    /** 报告生成记录 */\n    handleReportRecord() {\n      this.reportRecordDialogVisible = true;\n      this.reportQueryParams.taskType = this.listType === 4 ? 2 : 1;\n      this.getReportList();\n    },\n\n    /** 获取报告生成记录列表 */\n    getReportList() {\n      this.reportLoading = true;\n      getReportList(this.reportQueryParams).then(response => {\n        this.reportList = response.rows;\n        this.reportTotal = response.total;\n        this.reportLoading = false;\n      }).catch(() => {\n        this.reportLoading = false;\n      });\n    },\n\n    /** 任务详细信息 */\n    handleView(row) {\n      this.openView = true;\n      this.jobType = 2;\n      this.jobId = row.jobId;\n      this.editForm = {...row}\n    },\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n@import \"@/assets/styles/assetIndex.scss\";\n.policyCol {\n  min-width: 330px;\n  margin-top: 10px;\n}\n\n.policyDesc {\n  display: flex;\n  height: 80px;\n}\n\n.policyTxt {\n  margin-left: 10px;\n  line-height: 20px;\n}\n\n.policyTitle {\n  height: 40px;\n  line-height: 40px;\n}\n\n.oneLine {\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n}\n\n::v-deep .el-table {\n  display: flex;\n  flex-direction: column;\n}\n\n::v-deep .el-table__body-wrapper {\n  overflow-y: auto;\n  flex: 1;\n}\n</style>\n"]}]}