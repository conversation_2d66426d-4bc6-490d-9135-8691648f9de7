package com.ruoyi.ffsafe.scantaskapi.domain;

import com.ruoyi.ffsafe.api.domain.FfsafeApiConfig;
import lombok.Data;
import lombok.ToString;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.entity.StringEntity;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

@Data
@ToString
@Component
public class RunWebMonitorTaskParam extends ParamBase implements RequestBase{

    private String userId;
    private String monitorTaskId;
    private Long deviceConfigId;

    public void parseParam(int taskId) {
        monitorTaskId = String.valueOf(taskId);
    }

    @Override
    public HttpRequestBase getRequestBase(Long deviceId) {
        /*if (ffurl == null) {
            if (!updateFfsafeApiConfig(deviceId)) {
                return null;
            }
        }*/
        FfsafeApiConfig ffsafeApiConfig = getFfsafeApiConfig();
        String ffurl = ffsafeApiConfig.getUrl();
        String fftoken = ffsafeApiConfig.getToken();

        userId = "1";
        String fullUrl = ffurl + "/v1/web-period-task/" + userId +  "/" + monitorTaskId;

        HttpPut httpPut = null;
        List<NameValuePair> params = new ArrayList<NameValuePair>();
        try {
            httpPut = new HttpPut(fullUrl);
            params.add(new BasicNameValuePair("access_token", fftoken));
            StringEntity entity = new UrlEncodedFormEntity(params, StandardCharsets.UTF_8);
            httpPut.setEntity(entity);
            httpPut.setHeader("Content-type", "application/x-www-form-urlencoded");
        } catch (Exception e) {
            e.printStackTrace();
        }

        return httpPut;
    }
}
