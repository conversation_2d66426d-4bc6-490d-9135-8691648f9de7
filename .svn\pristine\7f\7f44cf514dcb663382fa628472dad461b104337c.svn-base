package com.ruoyi.ffsafe.scantaskapi.event;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.exception.job.TaskException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.ffsafe.api.service.IApiResultSevice;
import com.ruoyi.ffsafe.scantaskapi.service.IFfsafeScantaskSummaryService;
import com.ruoyi.ffsafe.scantaskapi.service.IScanTaskService;
import com.ruoyi.ffsafe.scantaskapi.domain.CreateHostScanTaskResult;
import com.ruoyi.ffsafe.scantaskapi.domain.FfsafeScantaskSummary;
import com.ruoyi.ffsafe.scantaskapi.domain.HostScanTaskParam;
import com.ruoyi.quartz.domain.SysJob;
import com.ruoyi.quartz.mapper.SysJobMapper;
import com.ruoyi.quartz.service.ISysJobService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component("HostVulnScan")
public class HostVulnScan {
    public static final int HOST_SCAN = 2;     // 非凡host scan 类型为2
    public static final int WEB_SCAN = 1;     // 非凡host scan 类型为2
    private static final int MAX_TASK_NUM = 30;

    @Autowired
    private IApiResultSevice apiResultSevice;
    @Autowired
    private IScanTaskService scanTaskService;
    @Autowired
    private IFfsafeScantaskSummaryService scantaskSummaryService;
    @Autowired
    private HostScanTaskParam hostScanTaskParam;
    @Autowired
    private ScanResultMonitorEvent hostScanResultMonitorEvent;
    @Autowired
    private ISysJobService sysJobService;
    @Resource
    private SysJobMapper sysJobMapper;

    private boolean checkCreateTask() {
        List<JSONObject> currentTaskList = hostScanResultMonitorEvent.cloneTaskList();
        if (currentTaskList.size() >= MAX_TASK_NUM) {
            return false;
        }

        return true;
    }

    public void scan(String jobId, String scanParam) throws Exception {
        if (!checkCreateTask()) {
            Thread.sleep(2000);
            throw new Exception("系统主机扫描线程已达最大, 请稍后执行.");
        }
        log.info("开始非凡主机漏扫, jodid: " + jobId + " param: " + scanParam);
        SysJob sysJob = sysJobService.selectJobById(Long.valueOf(jobId));
        if(sysJob == null){
            throw new Exception("任务不存在");
        }
        if (sysJob.getCurrentStatus() == SysJob.PROCESS_RUNNING) {
            throw new Exception("当前任务正在扫描中, 请忽重复调度.");
        }
        HostScanTaskParam curHostScanTaskParam = new HostScanTaskParam();
        curHostScanTaskParam.parseParam(scanParam, sysJob);
        CreateHostScanTaskResult createHostScanTaskResult = null;
        try {
            createHostScanTaskResult = scanTaskService.createHostScanTask(curHostScanTaskParam);
            if (createHostScanTaskResult != null) {   // 创建任务成功
                int taskId = createHostScanTaskResult.getTaskId();
                FfsafeScantaskSummary ffsafeScantaskSummary = new FfsafeScantaskSummary();
                ffsafeScantaskSummary.setJobId(Integer.valueOf(jobId));
                ffsafeScantaskSummary.setTaskId(taskId);
                ffsafeScantaskSummary.setTaskType(HOST_SCAN);
                ffsafeScantaskSummary.setTaskStatus(ScanResultMonitorEvent.SCHEDULING);
                if (scantaskSummaryService.insertFfsafeScantaskSummary(ffsafeScantaskSummary) > 0) {
                    JSONObject summaryParams = new JSONObject();
                    summaryParams.put("taskId", taskId);
                    summaryParams.put("jobId", jobId);
                    summaryParams.put("deviceConfigId",sysJob.getDeviceConfigId());
                    hostScanResultMonitorEvent.addHostScanTask(summaryParams);
                    sysJob.setCurrentStatus(SysJob.PROCESS_RUNNING);
                }
            }
        } catch (Exception e) {
            log.warn("创建主机扫描任务失败, param:  " + scanParam + " 失败原因 :" + e.getMessage());
            throw e;
        } finally {
            try {
                hostScanResultMonitorEvent.updateSysJob(sysJob);
            } catch (Exception e) {
                e.printStackTrace();
                log.warn("创建主机扫描更新任务状态失败, param:  " + scanParam + " 失败原因 :" + e.getMessage());
            }
        }
    }

    private SysJob getSysJobByTarget(String target) throws SchedulerException, TaskException {
        if (target == null)
            return null;

        SysJob sysJob = new SysJob();
        sysJob.setJobGroup("ASSET_SCAN");
        sysJob.setJobType(SysJob.VULN_SCAN);
        sysJob.setJobName(target + "_" + DateUtils.dateTimeNow());
        String tempTarget = target + " " + target + " 1 0 1";
        String jobTarget = "HostVulnScan.scan('${jobId}'," + "'" + tempTarget + "')";
        sysJob.setInvokeTarget(jobTarget);
        sysJob.setCurrentStatus(0);
        sysJob.setConcurrent("1");
        sysJob.setStatus("0");
        return sysJob;
    }

    public boolean addHostScanJob(String target) throws SchedulerException, TaskException {
        boolean bRet = false;

        SysJob sysJob = getSysJobByTarget(target);
        if (sysJobMapper.insertJob(sysJob) > 0) {
            sysJob.setInvokeTarget(sysJob.getInvokeTarget().replace("${jobId}", String.valueOf(sysJob.getJobId())));
            int nRet = sysJobService.updateSimpleJob(sysJob);
            bRet = nRet > 0;
        }
        return bRet;
    }
}
