{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\index\\index.vue?vue&type=template&id=4f88bc19&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\index\\index.vue", "mtime": 1755591292555}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751956544249}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}