# AQSOC安全运营平台AI Agent开发规范

## 项目架构

### 技术栈
- **后端**: Spring Boot 2.5.14 + MyBatis-Plus 3.5.3.1 + MySQL + Redis + RabbitMQ
- **前端**: Vue 2.6.12 + Element UI 2.15.8 + ECharts + AntV X6
- **数据库**: MySQL 5.7+，使用Druid连接池
- **缓存**: Redis 3.2+
- **消息队列**: RabbitMQ
- **文件存储**: Minio
- **其他**: Swagger API文档、JWT认证、定时任务(Quartz)

### 项目结构
```
aqsoc-main/
├── aqsoc-admin/          # Web服务入口（主启动模块）
├── aqsoc-framework/      # 核心框架模块
├── aqsoc-system/         # 系统管理模块
├── aqsoc-common/         # 通用工具模块
├── aqsoc-quartz/         # 定时任务模块
├── aqsoc-generator/      # 代码生成模块
├── aqsoc-monitor/        # 系统监控模块
├── aqsoc-plugin/         # 插件模块
├── ruoyi-ui/            # 前端Vue项目
├── yml/                 # 配置文件目录
├── sql/all/             # 数据库版本控制脚本
└── pom.xml              # Maven父项目配置
```

## 模块依赖规则

### 核心依赖关系
- **aqsoc-admin**: 依赖所有其他模块，是主启动模块
- **aqsoc-framework**: 被所有业务模块依赖，提供核心框架功能
- **aqsoc-system**: 依赖framework和common，提供系统管理功能
- **aqsoc-common**: 被所有模块依赖，提供通用工具类
- **aqsoc-quartz**: 依赖framework和common，提供定时任务功能
- **aqsoc-generator**: 依赖framework和common，提供代码生成功能
- **aqsoc-monitor**: 依赖framework和common，提供监控功能
- **aqsoc-plugin**: 依赖framework和common，提供插件功能

### 模块修改规则
1. 修改aqsoc-framework模块时，必须测试所有依赖模块的功能
2. 修改aqsoc-common模块时，必须确保向后兼容性
3. 添加新功能时，优先选择合适的现有模块，避免创建新模块
4. 跨模块调用时，必须通过接口定义，禁止直接实现类调用

### 模块启动顺序
1. 先启动数据库和Redis服务
2. 启动aqsoc-admin主模块（自动加载所有依赖模块）
3. 启动前端ruoyi-ui服务
4. 验证所有模块正常工作

## 配置文件管理

### 配置文件结构
- **yml/application.yml**: 主配置文件，包含基础配置
- **yml/application-druid.yml**: 数据库连接池配置
- **aqsoc-admin/src/main/resources/application.yml**: 应用级配置

### 配置修改规则
1. **环境变量配置**: 使用${ENV_VAR}格式，禁止硬编码配置值
2. **数据库配置**: 修改application-druid.yml时，必须同步修改测试环境配置
3. **Redis配置**: 修改连接配置时，必须检查所有使用Redis的模块
4. **RabbitMQ配置**: 修改队列或交换机配置时，必须通知所有相关开发人员
5. **第三方服务配置**: 修改API地址或认证信息时，必须更新所有相关配置项

### 环境配置优先级
1. 命令行参数 > 环境变量 > 配置文件 > 默认值
2. 开发环境使用dev配置，生产环境使用prod配置

### 多客户部署配置
1. **fair**: 公安版本配置
2. **copper**: 同铜版本配置
3. **manage**: 理版本配置
3. 修改deployment.location配置时，必须同步修改相关功能模块

## 数据库变更流程

### 数据库版本控制
- **sql/all/0.Create.sql**: 数据库创建脚本
- **sql/all/2.X.X_*.sql**: 版本化变更脚本，按版本号排序
- **sql/upgrade/**: 升级脚本目录

### 数据库变更规则
1. **新增表**: 必须创建对应的Entity、Mapper、Service类
2. **修改表结构**: 必须创建版本化脚本，格式为`版本号_描述.sql`
3. **删除表或字段**: 必须先确认无业务依赖，创建回滚脚本
4. **索引变更**: 必须评估性能影响，创建前进行测试
5. **数据迁移**: 必须创建迁移脚本和回滚脚本

### 变更执行顺序
1. 执行版本化脚本（按版本号顺序）
2. 执行数据迁移脚本
3. 更新应用版本号
4. 部署应用并验证

### 数据库命名规范
1. **表名**: 使用小写+下划线，必须添加模块前缀
2. **字段名**: 使用小写+下划线
3. **主键**: 统一使用id字段，类型为bigint
4. **时间字段**: 统一使用create_time、update_time
5. **删除标识**: 统一使用del_flag字段

## 前后端协调开发

### API接口规范
1. **Controller层**: 使用@RestController，统一返回格式
2. **接口路径**: 使用/api/模块名/功能名格式
3. **请求方法**: GET用于查询，POST用于创建，PUT用于更新，DELETE用于删除
4. **参数验证**: 使用@Valid注解进行参数验证
5. **异常处理**: 统一使用GlobalExceptionHandler处理异常

### 前端API调用规则
1. **API文件**: 按模块分组，位于src/api/目录下
2. **请求拦截**: 统一使用request.js进行请求拦截
3. **错误处理**: 统一使用errorCode.js处理错误码
4. **权限验证**: 使用token进行身份验证

### 前后端同步要求
1. 修改后端API接口时，必须同步更新前端API调用
2. 修改数据结构时，必须同步更新前端数据模型
3. 修改权限配置时，必须同步更新前端权限验证逻辑
4. 新增功能时，必须同时提供后端API和前端页面

### 前端组件规范
1. **组件命名**: 使用大驼峰命名法
2. **文件命名**: 使用kebab-case命名法
3. **CSS类名**: 使用kebab-case命名法
4. **JavaScript变量**: 使用小驼峰命名法
5. **Vue组件**: 必须添加name属性

## 第三方服务集成

### 集成配置规则
1. **Minio文件服务**: 配置在application.yml中，使用环境变量
2. **RabbitMQ消息队列**: 配置交换机、队列、路由键，支持消息加密
3. **Redis缓存**: 配置连接池和超时时间
4. **外部API服务**: 配置URL、认证信息、超时时间

### 第三方服务配置项
```yaml
# Minio配置
minio:
  url: ${MINIO_INNER_URL}
  port: ${MINIO_PORT}
  username: aqsoc
  password: hyite@2024
  bucketName: ${BUCKET_NAME}

# RabbitMQ配置
spring:
  rabbitmq:
    addresses: ${SPRING_RABBITMQ_HOST_PORT}
    username: admin
    password: Hyite@2024
    virtual-host: ${SPRING_RABBITMQ_VIRTUAL_HOST}

# Redis配置
spring:
  redis:
    host: ${SPRING_REDIS_HOST}
    port: ${SPRING_REDIS_PORT}
    password: hyite@2024
```

### 集成开发规则
1. 新增第三方服务时，必须创建配置类
2. 必须实现异常处理和重试机制
3. 必须添加超时配置和连接池配置
4. 必须提供健康检查接口

### 消息队列配置
1. **交换机**: purple-platform.data-sync（数据同步）、service-platform.device（设备）
2. **队列**: service-platform.data-sync、service-platform.device.*
3. **路由键**: data-sync、device.*
4. **消息加密**: 启用AES加密，密钥长度16位

## 部署配置管理

### 多客户部署配置
- **deployment.location=fair**: 公安版本
- **deployment.location=copper**: 同铜版本  
- **deployment.location=manage**: 理版本

### 部署规则
1. 不同客户的配置文件必须放在单独的配置文件中
2. 环境变量必须根据部署环境进行配置
3. 数据库连接信息必须使用环境变量
4. 文件上传路径必须根据操作系统进行配置

### 部署脚本
- **ry.sh**: Linux/Mac启动脚本
- **ry.bat**: Windows启动脚本
- **Dockerfile**: Docker部署配置

### 环境变量配置
1. **SPRING_REDIS_HOST**: Redis服务器地址
2. **SPRING_REDIS_PORT**: Redis端口
3. **SPRING_RABBITMQ_HOST_PORT**: RabbitMQ地址
4. **SPRING_RABBITMQ_VIRTUAL_HOST**: RabbitMQ虚拟主机
5. **MINIO_INNER_URL**: Minio内部访问地址
6. **MINIO_PORT**: Minio端口
7. **BUCKET_NAME**: Minio桶名称

## 代码开发规范

### Java开发规范
1. **包名结构**: com.ruoyi.模块名.层级
2. **类名规范**: 使用大驼峰命名法
3. **方法名规范**: 使用小驼峰命名法
4. **常量规范**: 使用全大写+下划线
5. **注释规范**: 必须添加类和方法注释

### 前端开发规范
1. **组件命名**: 使用大驼峰命名法
2. **文件命名**: 使用kebab-case命名法
3. **CSS类名**: 使用kebab-case命名法
4. **JavaScript变量**: 使用小驼峰命名法
5. **Vue组件**: 必须添加name属性

### 数据库规范
1. **表名**: 使用小写+下划线，必须添加模块前缀
2. **字段名**: 使用小写+下划线
3. **主键**: 统一使用id字段，类型为bigint
4. **时间字段**: 统一使用create_time、update_time
5. **删除标识**: 统一使用del_flag字段

### AI Agent决策规则
1. **功能定位**: 根据功能需求选择合适的模块进行开发
2. **依赖判断**: 修改框架模块时，必须评估对所有业务模块的影响
3. **配置优先**: 所有配置项必须使用环境变量，禁止硬编码
4. **测试要求**: 修改核心模块后，必须运行完整的测试套件
5. **文档同步**: 代码变更必须同步更新相关文档

## 安全规范

### 认证授权
1. 使用JWT进行身份验证
2. 使用@PreAuthorize进行权限控制
3. 敏感接口必须添加权限验证
4. 密码必须使用BCrypt加密

### 数据安全
1. 敏感数据必须加密存储
2. 数据传输必须使用HTTPS
3. 输入数据必须进行XSS过滤
4. SQL查询必须使用参数化查询

### 系统安全
1. 必须配置CORS策略
2. 必须配置CSRF防护
3. 必须配置会话超时
4. 必须配置日志审计

### 消息安全
1. RabbitMQ消息必须启用AES加密
2. 加密密钥：JNPF2024SECUREKY（16位）
3. 初始向量：JNPFINITV2024088（16位）
4. 敏感配置信息必须使用环境变量

## 禁止操作

### 严格禁止的操作
1. **禁止**在生产环境直接修改数据库数据
2. **禁止**在代码中硬编码敏感信息
3. **禁止**使用System.out.println()进行日志输出
4. **禁止**在生产环境开启调试模式
5. **禁止**使用不安全的第三方依赖
6. **禁止**在Controller中直接操作数据库
7. **禁止**使用SELECT * 查询所有字段
8. **禁止**在前端代码中暴露敏感信息
9. **禁止**修改aqsoc-framework模块而不进行全量测试
10. **禁止**在没有创建回滚脚本的情况下执行数据库变更

### 不推荐的操作
1. **避免**在循环中进行数据库操作
2. **避免**使用过大的事务
3. **避免**在Controller中编写业务逻辑
4. **避免**使用全局变量
5. **避免**在SQL中使用子查询

### AI Agent特别注意事项
1. **禁止**创建与现有功能重复的新模块
2. **禁止**修改核心配置文件而不进行充分测试
3. **禁止**在前端API调用中绕过权限验证
4. **禁止**在数据库操作中不使用事务
5. **禁止**在消息队列处理中不进行异常处理

## 测试规范

### 单元测试
1. 必须为所有Service方法编写单元测试
2. 使用JUnit 5和Mockito进行测试
3. 测试覆盖率必须达到80%以上
4. 测试文件必须与源码文件保持同步

### 集成测试
1. 必须为所有API接口编写集成测试
2. 使用Spring Boot Test进行测试
3. 必须测试正常和异常情况
4. 必须测试权限控制

### 性能测试
1. 必须对所有关键接口进行性能测试
2. 必须测试并发访问情况
3. 必须测试大数据量情况
4. 必须监控资源使用情况

### AI Agent测试要求
1. **模块测试**: 修改aqsoc-framework模块后，必须测试所有依赖模块
2. **数据库测试**: 数据库变更后，必须执行完整的集成测试
3. **前端测试**: API接口变更后，必须测试前端调用
4. **配置测试**: 配置文件变更后，必须测试所有相关功能
5. **部署测试**: 部署前必须运行完整的测试套件

## 监控和日志

### 日志规范
1. 使用SLF4J进行日志记录
2. 日志级别：ERROR>WARN>INFO>DEBUG
3. 必须记录关键业务操作
4. 必须记录异常堆栈信息
5. 必须记录用户操作日志

### 监控规范
1. 必须监控应用性能指标
2. 必须监控数据库连接池
3. 必须监控Redis连接
4. 必须监控消息队列
5. 必须设置告警规则

### AI Agent日志要求
1. **操作日志**: 记录所有AI Agent执行的操作
2. **错误日志**: 记录所有异常和错误信息
3. **性能日志**: 记录关键操作的性能指标
4. **审计日志**: 记录所有敏感操作

## 文档要求

### 代码文档
1. 所有类必须添加JavaDoc注释
2. 所有public方法必须添加方法注释
3. 复杂逻辑必须添加行内注释
4. 配置项必须添加说明注释

### 接口文档
1. 使用Swagger生成API文档
2. 必须添加接口描述和参数说明
3. 必须添加示例数据
4. 必须保持文档与代码同步

### 更新日志
1. 必须记录所有功能变更
2. 必须记录所有bug修复
3. 必须记录所有配置变更
4. 必须记录所有依赖更新

### AI Agent文档同步要求
1. **代码变更**: 必须同步更新相关文档
2. **API变更**: 必须同步更新Swagger文档
3. **配置变更**: 必须同步更新配置说明
4. **数据库变更**: 必须同步更新数据库文档

## 项目启动流程

### 开发环境启动
1. 启动MySQL和Redis服务
2. 导入数据库脚本（sql/all/目录）
3. 配置环境变量
4. 启动后端：`mvn spring-boot:run` 或 `java -jar aqsoc-admin/target/aqsoc-admin-*.jar`
5. 启动前端：`cd ruoyi-ui && npm run dev`
6. 访问：http://localhost:8080

### 生产环境部署
1. 配置生产环境变量
2. 构建后端：`mvn clean package`
3. 构建前端：`cd ruoyi-ui && npm run build:prod`
4. 部署JAR包和前端文件
5. 启动服务：`java -jar aqsoc-admin/target/aqsoc-admin-*.jar --spring.profiles.active=prod`