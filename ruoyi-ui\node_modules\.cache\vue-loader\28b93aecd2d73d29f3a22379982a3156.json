{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\index\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\index\\index.vue", "mtime": 1755591292555}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2FA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/frailty/index", "sourcesContent": ["<template>\n  <div class=\"custom-container\">\n    <div class=\"custom-content-container-right\">\n      <div class=\"div-main-top\">\n        <div class=\"div-main-top-one\">\n          <div class=\"main-top-one-left\"><img class=\"img-style\" src=\"../../../assets/images/fengxianzongshu.png\"></div>\n          <div class=\"main-top-one-right\">\n            <div class=\"main-top-one-right-top\">{{ totalNumberOfRisks }}</div>\n            <div class=\"main-top-one-right-bottom\">风险总数</div>\n          </div>\n        </div>\n        <div class=\"div-main-top-two\" :style=\"{borderColor: borderNum === 1 ? '#637fef' : '#fff'}\" tabindex=\"0\" role=\"button\" id=\"autoFocusBox\" @click=\"toLoophole()\">\n          <div class=\"main-top-two-three-four\">\n            <div class=\"top-title-left\">主机风险</div>\n            <div class=\"top-title-right\">{{hostRisk.hostRiskNum}}</div>\n          </div>\n          <div class=\"main-top-two-bottom\">\n            <div :class=\"currentIndex === 'ip'+index ? 'icons-title-count icons-title-count-active':'icons-title-count'\" v-for=\"(item,index) in hostRiskList\" @click.stop=\"toLoopholeByType(item.severity,index)\">\n              <div class=\"icons-title-count-img\"><img class=\"title-count-img-style\" :src=\"item.img\"></div>\n              <div class=\"icons-title-count-right\">\n                <div class=\"icons-title-count-top\">{{ item.num }}</div>\n                <div class=\"icons-title-count-bottom\">{{ item.title }}</div>\n              </div>\n            </div>\n            <div :class=\"currentIndex == 'ip'+'4' ? 'icons-title-count icons-title-count-active':'icons-title-count'\" @click.stop=\"toWeakPassword()\">\n              <div class=\"icons-title-count-img\"><img class=\"title-count-img-style\" src=\"../../../assets/images/ruokoling.png\"></div>\n              <div class=\"icons-title-count-right\">\n                <div class=\"icons-title-count-top\">{{ hostRisk.weakPasswordsNum }}</div>\n                <div class=\"icons-title-count-bottom\">弱口令</div>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div class=\"div-main-top-three\" :style=\"{borderColor: borderNum === 2 ? '#637fef' : '#fff'}\" tabindex=\"0\" @click=\"toWebvulnPage()\">\n          <div class=\"main-top-two-three-four\">\n            <div class=\"top-title-left\">Web风险</div>\n            <div class=\"top-title-right\">{{ webRisk.webRiskNum }}</div>\n          </div>\n          <div class=\"main-top-three-bottom\">\n            <div :class=\"currentIndex === 'web'+index ? 'top-three-bottom-body icons-title-count icons-title-count-active' : 'top-three-bottom-body icons-title-count'\" v-for=\"(item,index) in webRiskList\" @click.stop=\"toWebvulnByType(item.severity,index)\">\n              <div class=\"icons-title-count-img\"><img class=\"title-count-img-style\" :src=\"item.img\"></div>\n              <div class=\"icons-title-count-right\">\n                <div class=\"icons-title-count-top\">{{ item.num }}</div>\n                <div class=\"icons-title-count-bottom\">{{ item.title }}</div>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div class=\"div-main-top-four\" :style=\"{borderColor: borderNum === 3 ? '#637fef' : '#fff'}\" tabindex=\"0\" @click=\"toMonitorIpPage(4)\">\n          <div class=\"main-top-two-three-four\">\n            <div class=\"top-title-left\">漏扫任务</div>\n            <div class=\"top-title-right\">{{ vulnerabilityScanning.vulnerabilityScanningNum }}</div>\n          </div>\n          <div class=\"main-top-four-bottom\">\n            <div :class=\"currentIndex === 'monitorIp'+4 ? 'top-four-bottom-body icons-title-count icons-title-count-active' : 'top-four-bottom-body icons-title-count'\" @click.stop=\"toMonitorIpPage(4)\">\n              <div class=\"icons-title-count-img\"><img class=\"title-count-img-style\" src=\"../../../assets/images/zhuji.png\"></div>\n              <div class=\"icons-title-count-right\">\n                <div class=\"icons-title-count-top\">{{ vulnerabilityScanning.hostScanningNum }}</div>\n                <div class=\"icons-title-count-bottom\">主机漏扫</div>\n              </div>\n            </div>\n            <div :class=\"currentIndex === 'monitorWeb'+5 ? 'top-four-bottom-body icons-title-count icons-title-count-active' : 'top-four-bottom-body icons-title-count'\" @click.stop=\"toMonitorWeb(5)\">\n              <div class=\"icons-title-count-img\"><img class=\"title-count-img-style\" src=\"../../../assets/images/weblousao.png\"></div>\n              <div class=\"icons-title-count-right\">\n                <div class=\"icons-title-count-top\">{{ vulnerabilityScanning.webVulnerabilityScanningNum }}</div>\n                <div class=\"icons-title-count-bottom\">Web漏扫</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      <el-tabs v-if=\"listType === 4 || listType === 5\" v-model=\"activeName\" class=\"tabs-style\">\n        <el-tab-pane :label=\"listType === 4 ? '主机漏扫任务' : 'Web漏扫任务'\" name=\"task\" />\n        <el-tab-pane :label=\"listType === 4 ? '主机漏扫记录' : 'Web漏扫记录'\" name=\"record\" />\n      </el-tabs>\n      <div :class=\"(listType === 4 || listType === 5) ? 'div-main-container-tabs' : 'div-main-container'\">\n        <index v-if=\"listType === 1\" :severity=\"loopholeSeverity\" :toParams=\"toParams\"/>\n        <webvuln v-if=\"listType === 2\" :severity=\"webvulnSeverity\" :toParams=\"toParams\"/>\n        <Index2 v-if=\"listType  === 3\" :toParams=\"toParams\"/>\n        <Job v-if=\"listType === 4 && activeName === 'task'\" :toParams=\"toParams\"/>\n        <MonitorWeb v-if=\"listType === 5 && activeName === 'task'\" :toParams=\"toParams\"/>\n        <LeakyRecord\n          v-if=\"(listType === 4 || listType === 5) && activeName === 'record'\"\n          :list-type=\"listType\"\n          :toParams=\"toParams\"/>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport CreateWork from \"../../todoItem/todo/createWork\";\nimport newAddloophole from \"@/views/frailty/loophole/newAddloophole.vue\";\nimport FlowBox from '@/views/zeroCode/workFlow/components/FlowBox.vue'\nimport FlowTemplateSelect from \"@/components/FlowTemplateSelect/index.vue\";\nimport CronInput from '@/components/CronInput/index.vue'\nimport LeakScanDialog from \"@/views/safe/server/components/LeakScanDialog.vue\";\nimport DeptSelect from '@/views/components/select/deptSelect.vue'\nimport { getVulnerabilityRiskHeadCount } from \"@/api/threat/threat\";\nimport Webvuln from \"@/views/frailty/webvuln/index.vue\";\nimport Index from \"@/views/frailty/loophole/index.vue\";\nimport Index2 from \"@/views/frailty/weakPassword/index.vue\";\nimport Job from \"@/views/frailty/monitor/monitorIp.vue\";\nimport MonitorWeb from \"@/views/frailty/monitor/monitorWeb.vue\"\nimport LeakyRecord from \"@/views/frailty/monitor/leakyRecord.vue\"\nexport default {\n  components: {\n    Index,\n    Index2,\n    MonitorWeb,\n    Job,\n    Webvuln,\n    DeptSelect,\n    LeakScanDialog,\n    CronInput,\n    CreateWork,\n    newAddloophole,\n    FlowBox,\n    FlowTemplateSelect,\n    LeakyRecord,\n    SystemList: () => import('../../../components/SystemList')\n  },\n  dicts: [\"loophole_category\", \"synchronization_status\"],\n  data() {\n    return {\n      toParams: {},\n      webvulnSeverity:null,\n      loopholeSeverity:null,\n      listType:1,\n      borderNum: 1, // 保持选中固定值\n      hostRisk:{}, // 主机风险对象\n      webRisk:{}, // web风险对象\n      vulnerabilityScanning:{}, // 漏洞扫描对象\n      totalNumberOfRisks:0, // 风险总数\n      hostRiskList: [\n        {\n          severity:4,\n          title:\"可入侵漏洞\",\n          img:require('@/assets/images/keruqin.png'),\n          num:0\n        },\n        {\n          severity:3,\n          title:\"高危漏洞\",\n          img: require('@/assets/images/gaowei.png'),\n          num:0\n        },\n        {\n          severity:2,\n          title:\"中危漏洞\",\n          img: require('@/assets/images/zhongwei.png'),\n          num:0\n        },\n        {\n          severity:1,\n          title:\"低危漏洞\",\n          img: require('@/assets/images/diwei.png'),\n          num:0\n        },\n      ],\n      webRiskList: [\n        {\n          severity:4,\n          title:\"严重漏洞\",\n          img:require('@/assets/images/keruqin.png'),\n          num:0\n        },\n        {\n          severity:3,\n          title:\"高危漏洞\",\n          img: require('@/assets/images/gaowei.png'),\n          num:0\n        },\n        {\n          severity:2,\n          title:\"中危漏洞\",\n          img: require('@/assets/images/zhongwei.png'),\n          num:0\n        },\n        {\n          severity:1,\n          title:\"低危漏洞\",\n          img: require('@/assets/images/diwei.png'),\n          num:0\n        },\n      ],\n      activeName: 'task',\n      userList: [],\n      currentIndex: ''\n    }\n  },\n  mounted() {\n    this.$nextTick(() => {\n      if (!this.$route.query.type) {\n        let el = document.getElementById('autoFocusBox');\n        el.focus();\n      }\n    });\n  },\n  created() {\n    this.initData();\n  },\n  watch: {\n    $route: {\n      handler(newVal, oldVal) {\n        // 监听所有路由变化\n        if (newVal.query.referenceId) {\n          // 扣分详情跳转判断\n          if (newVal.query.type === '1') {\n            this.toLoophole();\n            this.toParams = {\n              referenceId: newVal.query.referenceId\n            }\n          }\n          if (newVal.query.type === '2') {\n            this.toWebvulnPage();\n            this.toParams = {\n              referenceId: newVal.query.referenceId\n            }\n          }\n          if (newVal.query.type === '3') {\n            this.toWeakPassword();\n            this.toParams = {\n              referenceId: newVal.query.referenceId\n            }\n          }\n          this.$router.replace({});\n        }\n        if (newVal.query.type === '4') {\n          this.toMonitorIpPage(Number(newVal.query.type),{id: newVal.query.id})\n          const query = { ...this.$route.query }; // 复制当前查询对象\n          delete query.type;                      // 删除目标参数\n          delete query.id;                      // 删除目标参数\n          this.$router.replace({ query });         // 替换当前路由（URL更新）\n        } else if (newVal.query.type === '5') {\n          this.toMonitorWeb(Number(newVal.query.type),{\n            id: newVal.query.id,\n            cronTransfer: newVal.query.cronTransfer,\n            invokeTarget: newVal.query.invokeTarget,\n            jobName: newVal.query.jobName\n          })\n          const query = { ...this.$route.query }; // 复制当前查询对象\n          delete query.type;                      // 删除目标参数\n          delete query.id;                      // 删除目标参数\n          this.$router.replace({ query });         // 替换当前路由（URL更新）\n        }\n\n        if (newVal.query.type) {\n          this.$nextTick(() => {\n            // 精确选择当前组件内的目标元素\n            const targetElement = this.$el.querySelector('.div-main-top-four[tabindex]')\n            if (targetElement) {\n              // 先移除其他元素的焦点\n              document.activeElement?.blur?.()\n              // 添加延迟确保渲染完成\n              setTimeout(() => {\n                targetElement.focus()\n                // 添加自定义聚焦样式\n                targetElement.classList.add('force-focus')\n              }, 50)\n            }\n          })\n        }\n      },\n      immediate: true\n    }\n  },\n  methods: {\n    toWeakPassword(){\n      this.currentIndex = 'ip'+'4';\n      this.listType = 3;\n      this.borderNum = 1;\n      //this.currentIndex = ''\n    },\n    toLoophole(){\n      this.loopholeSeverity = null;\n      this.listType = 1;\n      this.borderNum = 1;\n      this.currentIndex = '';\n    },\n    toLoopholeByType(type,index){\n      this.currentIndex = 'ip'+index;\n      this.loopholeSeverity = type;\n      this.listType = 1;\n      this.borderNum = 1;\n    },\n    toWebvulnPage(){\n      this.listType = 2;\n      this.borderNum = 2;\n      this.currentIndex = ''\n      this.webvulnSeverity = null;\n    },\n    toWebvulnByType(type,index){\n      this.webvulnSeverity = type;\n      this.currentIndex = 'web'+index;\n      this.listType = 2;\n      this.borderNum = 2;\n    },\n    toMonitorIpPage(index,params){\n      this.listType = index;\n      this.borderNum = 3;\n      this.currentIndex = 'monitorIp'+index;\n      this.toParams = params;\n      this.activeName = 'task'\n    },\n    toMonitorWeb(index,params){\n      this.listType = index;\n      this.borderNum = 3;\n      this.currentIndex = 'monitorWeb'+index;\n      this.toParams = params;\n      this.activeName = 'task'\n    },\n    initData() {\n      getVulnerabilityRiskHeadCount().then(res => {\n        if (res.data){\n          this.hostRisk = res.data.hostRisk;\n          this.webRisk = res.data.webRisk;\n          this.vulnerabilityScanning = res.data.vulnerabilityScanning;\n          this.totalNumberOfRisks = res.data.totalNumberOfRisks;\n          //遍历hostRiskList\n          this.hostRiskList.forEach(e => {\n           let num = this.hostRisk.ipVulnerabilityLevelNum.filter(e1 => {return e1.severity == e.severity});\n           if (num.length == 0){\n             e.num = 0\n           }else {\n             e.num = num[0].num\n           }\n          })\n          this.webRiskList.forEach(e => {\n            let num = this.webRisk.webVulnerabilityLevelNum.filter(e1 => {return e1.severity == e.severity});\n            if (num.length == 0){\n              e.num = 0\n            }else {\n              e.num = num[0].num\n            }\n          })\n        }\n      })\n    },\n  }\n}\n</script>\n<style lang=\"scss\" scoped>\n@import \"../../../assets/styles/tabs.scss\";\n\n.div-main-container {\n  height: calc(100% - 119px)\n}\n\n.div-main-container-tabs {\n  height: calc(100% - 160px);\n  margin-top: 3px;\n}\n\n::v-deep.el-select {\n  width: 100%;\n  .el-select-dropdown {\n    position: absolute;\n    top: 30px !important;\n    left: 5px;\n    .el-scrollbar {\n      max-height: 300px;\n      overflow-y: auto;\n    }\n  }\n}\n\n.loop_dialog {\n  height: 90vh;\n  overflow: hidden;\n  ::v-deep .el-dialog {\n    height: 100%;\n    .el-dialog__body {\n      height: calc(100% - 110px);\n      padding: 10px 20px 0;\n      overflow: auto;\n    }\n  }\n}\n\n.asset-tag {\n  margin-left: 5px;\n  max-width: 35%;\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  vertical-align: middle;\n}\n\n.overflow-tag:not(:first-child) {\n  margin-top: 5px;\n}\n</style>\n"]}]}