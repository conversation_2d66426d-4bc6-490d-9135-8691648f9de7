{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\eventList.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\eventList.vue", "mtime": 1755584508953}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\babel.config.js", "mtime": 1751890080904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_ruoyi", "require", "_data", "_applicationAssets", "_<PERSON><PERSON><PERSON>n", "_overview", "_DynamicTag", "_interopRequireDefault", "_alarmDetail", "_importThreaten", "_threatenConfigList", "_serverAdd", "_safeAdd", "_viewStrategy", "_publishClickDialog", "_FlowBox", "_FlowTemplateSelect", "_attackStage", "_attackViewList", "_sufferViewList", "_index", "_deptSelect", "_utils", "_FlowEngine", "_user", "_attackStageText", "_deviceConfig", "name", "components", "AttackStageText", "DeptSelect", "SufferViewList", "AttackViewList", "AttackStage", "FlowTemplateSelect", "FlowBox", "PublishClickDialog", "attackDetail", "sufferDetail", "ThreatenConfigList", "ViewStrategy", "SafeAdd", "ServerAdd", "importThreaten", "AlarmDetail", "DynamicTag", "dicts", "props", "propsActiveName", "type", "String", "props<PERSON>ueryP<PERSON><PERSON>", "Object", "default", "currentBtn", "Number", "data", "validateBlockIp", "rule", "value", "callback", "Error", "pattern", "test", "userList", "showHandleDialog", "handleForm", "id", "handleDesc", "handleState", "handleRules", "required", "message", "trigger", "showAll", "threatenDict", "queryParams", "pageNum", "pageSize", "deptOptions", "rangeTime", "loading", "threatenWarnList", "total", "title", "open<PERSON>hren<PERSON>", "form", "rules", "<PERSON><PERSON><PERSON>", "min", "max", "alarmLevel", "threatenType", "reason", "handSuggest", "logTime", "createTime", "srcIp", "srcPort", "destIp", "destPort", "mateRule", "associaDevice", "attackType", "attackStage", "attackResult", "blockingForm", "blockingRules", "block_ip", "validator", "duration_time", "remarks", "blockingIpList", "blockingDialogVisible", "editable", "assetInfoList", "openDialog", "assetData", "importDialog", "serverOpen", "assetId", "safeOpen", "threatenConfigFlag", "viewStrategy", "publishDialogVisible", "flowVisible", "flowTemplateSelectVisible", "flowStateOptions", "label", "handleStateOptions", "activeName", "syncStateOptions", "blockingDuration", "multipleSelection", "deviceConfigList", "watch", "formDestIp", "oldValue", "_this", "rg", "reg", "getAssetInfoByIp", "then", "response", "length", "for<PERSON>ach", "item", "assetName", "assetTypeDesc", "deptId", "$message", "warning", "init", "handler", "val", "handlePropsQuery", "split", "map", "ip", "trim", "filter", "immediate", "created", "getDeviceConfigList", "mounted", "$route", "query", "keys", "methods", "getThreatenDict", "handleQuery", "getDeptsData", "getUserList", "_this2", "listUser", "res", "rows", "_this3", "console", "log", "referenceId", "_objectSpread2", "startTime", "parseTime", "endTime", "Date", "setHours", "getList", "$nextTick", "JSON", "parse", "stringify", "join", "$refs", "atcAge", "initAttackStage", "_this4", "getMulTypeDict", "dictType", "_this5", "getDeptSystem", "handleChange", "reset<PERSON><PERSON>y", "flowState", "updateTime", "currentSelectedCard", "handleAdd", "$set", "handleImport", "handleExport", "download", "concat", "getTime", "handleRowClick", "row", "column", "event", "_this6", "property", "listAlarm", "_defineProperty2", "assetType", "assetClassDesc", "toString", "handleSelectionChange", "flowStateFormatter", "cellValue", "index", "match", "find", "disposer<PERSON><PERSON><PERSON><PERSON>", "e", "userId", "nick<PERSON><PERSON>", "handleStateFormatter", "handleDetail", "openDetail", "showHandle", "parseInt", "handleEdit", "_this7", "getAlarm", "attackNum", "handleDelete", "_this8", "ids", "$modal", "confirm", "delAlarm", "success", "catch", "addOrUpdateFlowHandle", "_this9", "formType", "opType", "status", "isWork", "workType", "eventType", "originType", "currentFlowData", "getConfigKey", "flowId", "msg", "getFlowEngineInfo", "finally", "_this10", "FlowEngineInfo", "flowTemplateJson", "error", "_this11", "$emit", "deptName", "deptNameArr", "uniqueArr", "handleClose", "done", "resetFields", "submitHandleForm", "_this12", "validate", "valid", "updateAlarm", "submitForm", "_this13", "addAlarm", "cancel", "closeDialog", "closeAssetDialog", "networkOpen", "closeFlow", "isrRefresh", "flowTemplateSelectChange", "_this14", "handleAtcAgeClick", "attackSeg", "handle", "datasource", "dataSource", "handleApplicationTagShow", "applicationList", "result", "handleBlocking", "arr", "Array", "from", "Set", "blockingSubmit", "_this15", "addBlockIp", "multipleTable", "clearSelection", "_this16", "listDeviceConfig", "queryAllData"], "sources": ["src/views/frailty/event/component/eventList.vue"], "sourcesContent": ["<template>\n  <div class=\"custom-container\">\n    <div class=\"custom-content-container-right\">\n      <div class=\"custom-content-search-box\">\n        <el-form\n          :model=\"queryParams\"\n          ref=\"queryForm\"\n          size=\"small\"\n          :inline=\"true\"\n          label-position=\"right\"\n          label-width=\"70px\"\n        >\n          <el-row :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"最近告警时间\" label-width=\"98px\">\n                <el-date-picker\n                  v-model=\"rangeTime\"\n                  type=\"datetimerange\"\n                  range-separator=\"至\"\n                  start-placeholder=\"开始日期\"\n                  end-placeholder=\"结束日期\"\n                  :default-time=\"['00:00:00', '23:59:59']\">\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n<!--            <el-col :span=\"6\">\n              <el-form-item label=\"告警等级\" prop=\"alarmLevel\">\n                <el-select\n                  clearable\n                  v-model=\"queryParams.alarmLevel\"\n                  placeholder=\"请选择告警等级\"\n                >\n                  <el-option\n                    v-for=\"dict in dict.type.threaten_type\"\n                    :key=\"dict.value\"\n                    :label=\"dict.label\"\n                    :value=\"dict.value\"\n                  ></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>-->\n            <el-col :span=\"6\">\n              <el-form-item label=\"处置状态\" prop=\"\">\n                <el-select v-model=\"queryParams.handleState\" placeholder=\"请选择处置状态\" clearable>\n                  <el-option :key=\"item.value\" :label=\"item.label\" :value=\"item.value\"\n                             v-for=\"(item,index) in handleStateOptions\"/>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"告警类型\" prop=\"threatenType\">\n                <el-cascader v-model=\"queryParams.threatenType\" :options=\"threatenDict\" clearable\n                             :props=\"{ label: 'dictLabel', value: 'dictValue' }\" placeholder=\"请选择告警类型\">\n                  <template slot-scope=\"{ node, data }\">\n                    <span>{{ data.dictLabel }}</span>\n                    <span v-if=\"!node.isLeaf\"> ({{ data.children.length }}) </span>\n                  </template>\n                </el-cascader>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item class=\"custom-search-btn\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  @click=\"handleQuery\">查询\n                </el-button>\n                <el-button class=\"btn2\" size=\"small\" @click=\"resetQuery\">重置</el-button>\n                <el-button class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-down\" @click=\"showAll=true\" v-if=\"!showAll\">\n                  展开\n                </el-button>\n                <el-button class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-up\" @click=\"showAll=false\" v-else>收起\n                </el-button>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"10\" v-if=\"showAll\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"告警名称\" prop=\"threatenName\">\n                <el-input\n                  v-model=\"queryParams.threatenName\"\n                  placeholder=\"请输入告警名称\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"源IP\" prop=\"srcIp\">\n                <el-input\n                  v-model=\"queryParams.srcIp\"\n                  placeholder=\"请输入源IP\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"目标IP\" prop=\"destIp\">\n                <el-input\n                  v-model=\"queryParams.destIp\"\n                  placeholder=\"请输入目标IP\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"10\" v-if=\"showAll\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"数据来源\" prop=\"dataSource\">\n                <el-select\n                  v-model=\"queryParams.dataSource\"\n                  placeholder=\"请选择数据来源\"\n                  clearable\n                  @change=\"$forceUpdate()\"\n                >\n                  <el-option :key=\"1\" label=\"探测\" :value=\"1\"/>\n                  <el-option :key=\"2\" label=\"手动\" :value=\"2\"/>\n                  <el-option :key=\"8\" label=\"流量\" :value=\"8\"/>\n                  <el-option :key=\"9\" label=\"探针\" :value=\"9\"/>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"所属部门\" prop=\"deptId\">\n                <dept-select v-model=\"queryParams.deptId\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"同步状态\">\n                <el-select v-model=\"queryParams.synchronizationStatus\" placeholder=\"请选择同步状态\" filterable clearable>\n                  <el-option\n                    v-for=\"dict in dict.type.synchronization_status\"\n                    :key=\"dict.value\"\n                    :label=\"dict.label\"\n                    :value=\"dict.value\"\n                  ></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"10\" v-if=\"showAll\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"通报状态\" prop=\"flowState\">\n                <el-select v-model=\"queryParams.flowState\" placeholder=\"请选择通报状态\" clearable>\n                  <el-option :key=\"item.value\" :label=\"item.label\" :value=\"item.value\"\n                             v-for=\"(item,index) in flowStateOptions\"/>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"处置人\">\n                <el-input\n                  v-model=\"queryParams.disposer\"\n                  placeholder=\"请输入处置人\"\n                  clearable\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"所属探针\">\n                <el-select v-model=\"queryParams.deviceConfigId\" filterable clearable placeholder=\"请选择\">\n                  <el-option\n                    v-for=\"item in deviceConfigList\"\n                    :key=\"item.id\"\n                    :label=\"item.deviceName\"\n                    :value=\"item.id\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n      </div>\n<!--      <div class=\"custom-content-search-chunk\" style=\"margin-bottom: 8px\">\n        <attack-stage ref=\"atcAge\" @handleClick=\"handleAtcAgeClick\"/>\n      </div>-->\n      <div class=\"custom-content-container\"\n           :style=\"showAll ? { height: 'calc(100% - 298px)' } :{ height: 'calc(100% - 208px)' }\">\n        <div class=\"common-header\">\n          <div><span class=\"common-head-title\">告警列表</span></div>\n          <div style=\"width: 60%; margin-left: 10%\">\n<!--            <attack-stage-text ref=\"atcAge\" @handleClick=\"handleAtcAgeClick\" />-->\n            <attack-stage-text ref=\"atcAge\"/>\n          </div>\n          <div class=\"common-head-right\">\n            <el-row :gutter=\"10\">\n              <el-col :span=\"1.5\">\n                <el-col :span=\"1.5\">\n                  <el-button\n                    type=\"primary\"\n                    size=\"small\"\n                    @click=\"handleAdd\"\n                    v-hasPermi=\"['system:threadten:add']\"\n                  >新增\n                  </el-button>\n                </el-col>\n                <el-col :span=\"1.5\">\n                  <el-button\n                    class=\"btn1\"\n                    size=\"small\"\n                    @click=\"handleBlocking\"\n                  >批量阻断</el-button>\n                </el-col>\n                <el-col :span=\"1.5\">\n                  <el-button\n                    class=\"btn1\"\n                    size=\"small\"\n                    @click=\"handleImport\"\n                    v-hasPermi=\"['system:threadten:import']\"\n                  >导入\n                  </el-button>\n                </el-col>\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  @click=\"handleExport\"\n                  v-hasPermi=\"['system:threadten:export']\"\n                >导出\n                </el-button>\n              </el-col>\n            </el-row>\n          </div>\n        </div>\n        <el-table\n          height=\"100%\"\n          v-loading=\"loading\"\n          ref=\"multipleTable\"\n          @row-click=\"handleRowClick\"\n          @selection-change=\"handleSelectionChange\"\n          :data=\"threatenWarnList\">\n          <el-table-column type=\"selection\" width=\"55\"></el-table-column>\n<!--          <el-table-column type=\"index\" width=\"100\" label=\"序号\"/>-->\n          <el-table-column label=\"最近告警时间\" width=\"200\" prop=\"updateTime\"/>\n          <el-table-column label=\"告警名称\" prop=\"threatenName\" min-width=\"260\"/>\n          <el-table-column label=\"告警类型\" prop=\"threatenType\" width=\"150\"/>\n          <el-table-column label=\"告警等级\" prop=\"alarmLevel\" width=\"150\">\n            <template slot-scope=\"scope\">\n              <dict-tag :options=\"dict.type.threaten_type\" :value=\"scope.row.alarmLevel\"/>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"源IP\" prop=\"srcIp\" width=\"180\">\n            <template slot-scope=\"scope\">\n              <div style=\"display: flex; align-items: center; justify-content: flex-start\">\n                <span>{{ scope.row.srcIp }}</span>\n                <img v-if=\"scope.row.isBlocking\" style=\"width: 24px;margin-left: 10px\" src=\"@/assets/images/block.png\" alt=\"\">\n              </div>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"目标IP/应用\" width=\"150\" prop=\"destIp\">\n          </el-table-column>\n          <el-table-column label=\"处置人\" prop=\"disposer\" width=\"150\" :formatter=\"disposerFormatter\">\n          </el-table-column>\n          <el-table-column label=\"关联业务系统\" prop=\"businessApplicationList\" width=\"200\">\n            <template slot-scope=\"scope\">\n              <el-tooltip placement=\"bottom-end\" effect=\"light\"\n                          v-if=\"scope.row.businessApplications && scope.row.businessApplications.length > 0\">\n                <div slot=\"content\">\n                  <div v-for=\"(item,tagIndex) in scope.row.businessApplications\" :key=\"item.assetId\"\n                       class=\"overflow-tag\" v-if=\"tagIndex <= 5\">\n                    <el-tag type=\"primary\"><span>{{ item.assetName }}</span></el-tag>\n                  </div>\n                  <div v-if=\"scope.row.businessApplications.length > 5\">\n                    <el-tag type=\"primary\"><span>...</span></el-tag>\n                  </div>\n                </div>\n                <el-tag type=\"primary\" class=\"asset-tag\">\n                  <span>{{ handleApplicationTagShow(scope.row.businessApplications) }}</span></el-tag>\n              </el-tooltip>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"通报状态\" prop=\"flowState\" width=\"150\" :formatter=\"flowStateFormatter\"/>\n          <el-table-column label=\"处置状态\" prop=\"handleState\" width=\"150\" :formatter=\"handleStateFormatter\"/>\n          <el-table-column label=\"所属部门\" prop=\"deptName\" width=\"150\"/>\n          <el-table-column label=\"数据来源\" prop=\"dataSource\" width=\"150\">\n            <template slot-scope=\"scope\">\n              <span v-if=\"scope.row.dataSource == '1'\">探测</span>\n              <span v-else-if=\"scope.row.dataSource == '2'\">手动</span>\n              <span v-else-if=\"scope.row.dataSource == '8'\">流量</span>\n              <span v-else-if=\"scope.row.dataSource == '9'\">探针</span>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"发现次数\" prop=\"alarmNum\" width=\"150\"/>\n          <el-table-column label=\"同步状态\" prop=\"synchronizationStatus\" width=\"150\">\n            <template slot-scope=\"scope\">\n              <span v-if=\"scope.row.synchronizationStatus === '0'\">未同步</span>\n              <span v-else-if=\"scope.row.synchronizationStatus === '1'\">已同步</span>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"操作\" width=\"250\" fixed=\"right\" :show-overflow-tooltip=\"false\"\n                           class-name=\"small-padding fixed-width\">\n            <template slot-scope=\"scope\">\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleDetail(scope.row)\"\n                v-hasPermi=\"['system:threadten:query']\"\n              >详情\n              </el-button>\n              <el-button v-if=\"scope.row.workId==null && !(scope.row.handleState === '1' || scope.row.handleState === '3')\"\n                         size=\"mini\"\n                         type=\"text\"\n                         @click=\"handleEdit(scope.row)\"\n                         v-hasPermi=\"['system:threadten:edit']\"\n              >编辑\n              </el-button>\n              <el-button v-if=\"scope.row.workId==null && !(scope.row.handleState === '3')\"\n                         size=\"mini\"\n                         type=\"text\"\n                         class=\"table-delBtn\"\n                         @click=\"handleDelete(scope.row)\"\n                         v-hasPermi=\"['system:threadten:remove']\"\n              >删除\n              </el-button>\n              <el-button\n                v-if=\"scope.row.workId==null && !(scope.row.handleState === '1' || scope.row.handleState === '3')\"\n                size=\"mini\"\n                type=\"text\"\n                @click=\"showHandle(scope.row)\"\n                v-hasPermi=\"['system:threadten:edit']\"\n              >处置\n              </el-button>\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                v-if=\"!(scope.row.handleState === '1' || scope.row.handleState === '3') && (scope.row.flowState == null || scope.row.flowState === '99')\"\n                @click=\"addOrUpdateFlowHandle(null,null,scope.row)\"\n              >创建通报\n              </el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n        <pagination\n          v-show=\"total>0\"\n          :total=\"total\"\n          :page.sync=\"queryParams.pageNum\"\n          :limit.sync=\"queryParams.pageSize\"\n          @pagination=\"getList\"\n        />\n      </div>\n    </div>\n    <!-- 处置威胁情报对话框! -->\n    <el-dialog\n      title=\"快速处置\"\n      :visible.sync=\"showHandleDialog\"\n      width=\"600px\"\n      append-to-body\n    >\n      <el-form ref=\"handleStateForm\" :model=\"handleForm\" :rules=\"handleRules\" label-width=\"106px\">\n        <el-form-item label=\"处置状态\" prop=\"handleState\">\n          <el-select v-model=\"handleForm.handleState\" clearable placeholder=\"请选择处置状态\">\n            <el-option v-for=\"dict in dict.type.handle_state\"\n                       :key=\"dict.value\" :label=\"dict.label\"\n                       :value=\"parseInt(dict.value)\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"处置说明\" prop=\"handleDesc\">\n          <el-input type=\"textarea\" :rows=\"2\" v-model=\"handleForm.handleDesc\" placeholder=\"请输入处置说明\"></el-input>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitHandleForm\">确 定</el-button>\n        <el-button @click=\"showHandleDialog = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n    <!-- 添加或修改威胁情报对话框! -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"openThrenten\"\n      width=\"80%\"\n      append-to-body\n      :before-close=\"handleClose\"\n    >\n      <el-row>\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"106px\" :disabled=\"!editable\">\n          <el-col :span=\"24\" class=\"mb8\">\n            <el-divider direction=\"vertical\"></el-divider>\n            <div class=\"my-title\">基本信息</div>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"告警名称\" prop=\"threatenName\">\n              <el-input v-model=\"form.threatenName\" placeholder=\"请输入告警名称\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"告警等级\" prop=\"alarmLevel\">\n              <el-select v-model=\"form.alarmLevel\" placeholder=\"请选择告警等级\" clearable>\n                <el-option\n                  v-for=\"dict in dict.type.threaten_type\"\n                  :key=\"dict.value\"\n                  :label=\"dict.label\"\n                  :value=\"dict.value\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"告警类型\" prop=\"threatenType\">\n              <el-cascader v-model=\"form.threatenType\" :options=\"threatenDict\" clearable placeholder=\"请选择告警类型\"\n                           :props=\"{ label: 'dictLabel', value: 'dictValue' }\" style=\"width: 100%\">\n                <template slot-scope=\"{ node, data }\">\n                  <span>{{ data.dictLabel }}</span>\n                  <span v-if=\"!node.isLeaf\"> ({{ data.children.length }}) </span>\n                </template>\n              </el-cascader>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"告警原因\" prop=\"reason\">\n              <el-input v-model=\"form.reason\" :autosize=\"{minRows: 3, maxRows: 3}\" type=\"textarea\"\n                        placeholder=\"请输入告警原因\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"处置建议\" prop=\"handSuggest\">\n              <el-input v-model=\"form.handSuggest\" :autosize=\"{minRows: 3, maxRows: 3}\" type=\"textarea\"\n                        placeholder=\"请输入告警建议\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"告警时间\" prop=\"createTime\">\n              <el-date-picker\n                v-model=\"form.createTime\"\n                type=\"date\"\n                placeholder=\"选择告警时间\"\n                format=\"yyyy 年 MM 月 dd 日\"\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"最近告警时间\" prop=\"updateTime\">\n              <el-date-picker\n                v-model=\"form.updateTime\"\n                type=\"date\"\n                placeholder=\"选择最近告警时间\"\n                format=\"yyyy 年 MM 月 dd 日\"\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"威胁标签\" prop=\"label\">\n              <DynamicTag v-model=\"form.label\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"关联设备\" prop=\"associaDevice\">\n              <el-input\n                v-model=\"form.associaDevice\"\n                placeholder=\"请输入关联设备\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\" class=\"mb8\">\n            <el-divider direction=\"vertical\"></el-divider>\n            <div class=\"my-title\">攻击关系</div>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"源IP\" prop=\"srcIp\">\n              <el-input\n                style=\"width: 50%\"\n                v-model=\"form.srcIp\"\n                placeholder=\"请输入源IP\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"源IP端口\" prop=\"srcPort\">\n              <el-input\n                style=\"width: 30%\"\n                v-model=\"form.srcPort\"\n                placeholder=\"请输入源IP端口\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"目标IP/应用\" prop=\"destIp\">\n              <el-input\n                style=\"width: 50%\"\n                v-model=\"form.destIp\"\n                placeholder=\"请输入目标IP\"\n              ></el-input>\n              <!--目标ip查询后有多个及显示资产数据，提交表单传(assetId： 资产id,deptId：部门id)-->\n              <el-select style=\"width: 50%;\" v-show=\"assetInfoList.length >= 2\" v-model=\"form.assetId\"\n                         placeholder=\"请确认疑似资产\">\n                <el-option v-for=\"item in assetInfoList\" :key=\"item.assetId\" :label=\"item.value\"\n                           :value=\"item.assetId\"></el-option>\n              </el-select>\n              <el-select style=\"width: 50%;\" v-show=\"false\" v-model=\"form.deptId\" placeholder=\"请选择资产\">\n                <el-option v-for=\"item in assetInfoList\" :label=\"item.value\" :value=\"item.deptId\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"目标IP端口\" prop=\"destPort\">\n              <el-input\n                style=\"width: 30%\"\n                v-model=\"form.destPort\"\n                placeholder=\"请输入目标IP端口\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col v-if=\"form.fileUrl!=null||editable\">\n            <el-col :span=\"24\" class=\"mb8\">\n              <el-divider direction=\"vertical\"></el-divider>\n              <div class=\"my-title\">文件上传</div>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"上传文件\" prop=\"fileUrl\">\n                <file-upload v-model=\"form.fileUrl\"\n                             :disUpload=\"!editable\"\n                             :limit=\"5\"\n                             :file-type=\"['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-col>\n        </el-form>\n      </el-row>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button v-if=\"editable\" type=\"primary\" @click=\"submitForm\"\n        >确 定\n        </el-button\n        >\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <el-dialog\n      v-if=\"openDialog\"\n      :title=\"title\"\n      :visible.sync=\"openDialog\"\n      width=\"80%\"\n      append-to-body\n    >\n      <el-tabs v-model=\"activeName\">\n        <el-tab-pane label=\"事件详情\" name=\"detail\">\n          <alarm-detail\n            v-if=\"openDialog\"\n            @openDetail=\"openDetail\"\n            :asset-data=\"assetData\"\n          />\n        </el-tab-pane>\n        <el-tab-pane v-if=\"assetData.srcIp\" label=\"攻击IP关联事件\" name=\"attack\">\n          <attack-detail :detail-type=\"'attack'\" :host-ip=\"assetData.srcIp\" :current-asset-data=\"assetData\"/>\n        </el-tab-pane>\n        <el-tab-pane v-if=\"assetData.destIp\" label=\"受害IP关联事件\" name=\"suffer\">\n          <suffer-detail :detail-type=\"'suffer'\" :host-ip=\"assetData.destIp\" :current-asset-data=\"assetData\"/>\n        </el-tab-pane>\n      </el-tabs>\n    </el-dialog>\n\n    <el-dialog title=\"导入威胁告警\" :visible.sync=\"importDialog\" width=\"800px\" append-to-body>\n      <import-threaten @closeDialog=\"closeDialog\" v-if=\"importDialog\"/>\n    </el-dialog>\n\n    <el-dialog title=\"查看服务器资产\" :visible.sync=\"serverOpen\" width=\"80%\" append-to-body>\n      <server-add :asset-id=\"assetId\" @cancel=\"closeAssetDialog()\" :editable=\"editable\" v-if=\"serverOpen\"/>\n    </el-dialog>\n\n    <el-dialog title=\"查看安全设备资产\" :visible.sync=\"safeOpen\" width=\"80%\" append-to-body>\n      <safe-add :asset-id=\"assetId\" @cancel=\"closeAssetDialog()\" :editable=\"editable\" v-if=\"safeOpen\"/>\n    </el-dialog>\n\n    <el-dialog title=\"查看告警策略\" :visible.sync=\"viewStrategy\" width=\"400\" append-to-body>\n      <view-strategy v-if=\"viewStrategy\" @close=\"viewStrategy=false\"/>\n    </el-dialog>\n\n\n    <el-dialog title=\"告警策略配置\" :visible.sync=\"threatenConfigFlag\" width=\"800\" append-to-body>\n      <threaten-config-list v-if=\"threatenConfigFlag\" @close=\"threatenConfigFlag=false\"/>\n    </el-dialog>\n\n    <el-dialog title=\"批量阻断\" :visible.sync=\"blockingDialogVisible\" width=\"400\">\n      <el-form :model=\"blockingForm\" :rules=\"blockingRules\" ref=\"blockingForm\" class=\"blocking-form\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"阻断ip\" prop=\"block_ip\">\n              <span slot=\"label\">\n                阻断ip\n                <template>\n                  <el-tooltip placement=\"top\">\n                    <div slot=\"content\">默认加载选择的事件的源IP，多个则以“;”隔开</div>\n                    <i class=\"el-icon-info\"></i>\n                  </el-tooltip>\n                </template>\n              </span>\n              <el-input v-model=\"blockingForm.block_ip\" placeholder=\"请输入ip\">\n                <el-popover\n                  slot=\"suffix\"\n                  placement=\"bottom\"\n                  width=\"100\"\n                  trigger=\"hover\"\n                >\n                  <ul>\n                    <li v-for=\"(ip, index) in blockingIpList\" :key=\"index\">{{ ip }}</li>\n                  </ul>\n                  <i slot=\"reference\" class=\"el-icon-more\"></i>\n                </el-popover>\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"阻断时长\" prop=\"duration_time\">\n              <el-select v-model=\"blockingForm.duration_time\" placeholder=\"请选择阻断时长\">\n                <el-option\n                  v-for=\"item in blockingDuration\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-form-item label=\"备注\" prop=\"remarks\">\n            <el-input v-model=\"blockingForm.remarks\" type=\"textarea\" maxlength=\"500\" show-word-limit placeholder=\"请输入阻断描述\"></el-input>\n          </el-form-item>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"blockingSubmit\">确 定</el-button>\n        <el-button @click=\"blockingDialogVisible = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <FlowBox v-if=\"flowVisible\" ref=\"FlowBox\" @close=\"closeFlow\"/>\n    <flow-template-select :show.sync=\"flowTemplateSelectVisible\" @change=\"flowTemplateSelectChange\"/>\n\n    <publish-click-dialog\n      :publish-dialog-visible=\"publishDialogVisible\"\n      @updateVisible=\"(val) => { this.publishDialogVisible = val}\"\n      title=\"发布告警事件\"\n      width=\"30%\"/>\n  </div>\n</template>\n\n<script>\nimport {parseTime} from \"@/utils/ruoyi\";\nimport {getMulTypeDict} from \"../../../../api/system/dict/data\";\nimport {getDeptSystem} from \"../../../../api/monitor2/applicationAssets\";\nimport {getAlarm, delAlarm, listAlarm, addAlarm, updateAlarm,addBlockIp} from \"../../../../api/threaten/threatenWarn\";\nimport {getAssetInfoByIp} from \"../../../../api/safe/overview\";\nimport DynamicTag from \"../../../../components/DynamicTag\";\nimport AlarmDetail from \"../../../basis/securityWarn/alarmDetail\";\nimport importThreaten from \"@/views/basis/securityWarn/importThreaten.vue\"\nimport ThreatenConfigList from \"@/views/basis/securityWarn/threatenConfigList.vue\"\nimport ServerAdd from \"../../../hhlCode/component/application/adds/serverAdd\";\nimport SafeAdd from \"../../../hhlCode/component/application/adds/safeAdd\";\nimport ViewStrategy from \"../../../basis/securityWarn/viewStrategy\";\nimport PublishClickDialog from \"../../../basis/securityWarn/publishClickDialog\";\nimport FlowBox from \"../../../zeroCode/workFlow/components/FlowBox\";\nimport FlowTemplateSelect from \"../../../../components/FlowTemplateSelect\";\nimport AttackStage from \"../../../threat/overview/attackStage\";\nimport AttackViewList from \"./attackViewList\";\nimport SufferViewList from \"./sufferViewList\";\nimport attackDetail from \"./detail/index.vue\";\nimport sufferDetail from \"./detail/index.vue\";\nimport DeptSelect from '@/views/components/select/deptSelect.vue'\nimport {uniqueArr} from '@/utils'\nimport {FlowEngineInfo} from \"@/api/lowCode/FlowEngine\";\nimport {listUser} from \"@/api/system/user\";\nimport AttackStageText from '@/views/threat/overview/attackStageText.vue'\nimport {\n  listDeviceConfig\n} from '@/api/ffsafe/deviceConfig';\n\nexport default {\n  name: \"eventList\",\n  components: {\n    AttackStageText,\n    DeptSelect,\n    SufferViewList,\n    AttackViewList,\n    AttackStage,\n    FlowTemplateSelect,\n    FlowBox,\n    PublishClickDialog,\n    attackDetail,\n    sufferDetail,\n    ThreatenConfigList, ViewStrategy, SafeAdd, ServerAdd, importThreaten, AlarmDetail, DynamicTag\n  },\n  dicts: ['threaten_type', 'attack_stage', 'attack_result','handle_state', 'synchronization_status'],\n  props: {\n    propsActiveName: {\n      type: String\n    },\n    propsQueryParams: {\n      type: Object,\n      default: function () {\n        return null\n      }\n    },\n    currentBtn: {\n      type: Number,\n      default: null\n    }\n  },\n  data() {\n    let validateBlockIp = (rule, value, callback) => {\n      if (!value) {\n        return callback(new Error('IP不能为空'));\n      }\n      // let pattern = /^((1[0-9]{2}|2[0-4][0-9]|25[0-5]|(\\d){1,2})\\.(1[0-9]{2}|2[0-4][0-9]|25[0-5]|(\\d){1,2}|0)\\.(1[0-9]{2}|2[0-4][0-9]|25[0-5]|(\\d){1,2}|0)\\.(1[0-9]{2}|2[0-4][0-9]|25[0-5]|(\\d){1,2}|0))$/;\n      let pattern = /^\\s*((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(\\s*;\\s*((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))*\\s*$/;\n      if (!pattern.test(value)) {\n        return callback(new Error('请输入正确的IP'));\n      }\n      return callback();\n    };\n    return {\n      userList: [],\n      showHandleDialog: false,\n      handleForm: {\n        id: '',\n        handleDesc: '',\n        handleState: ''\n      },\n      handleRules: {\n        handleState: [\n          {required: true, message: '请选择处理状态', trigger: 'blur'},\n        ]\n      },\n      showAll: false,\n      threatenDict: [],\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10\n      },\n      deptOptions: [],\n      rangeTime: [],\n      loading: false,\n      threatenWarnList: [],\n      total: 0,\n      title: '',\n      openThrenten: false,\n      form: {},\n      rules: {\n        threatenName: [\n          {required: false, min: 0, max: 500, message: '告警名称不能超过500字符', trigger: 'blur'},\n          {required: true, message: '请输入告警名称', trigger: 'blur'},\n          {\n            required: true,\n            pattern: /^[^\\s]+/,\n            message: '不能以空格开头！',\n            trigger: 'blur'\n          }\n        ],\n        alarmLevel: [\n          {required: true, message: '请输入告警等级', trigger: 'blur'},\n        ],\n        threatenType: [\n          {required: true, message: '请输入告警类型', trigger: 'blur'},\n        ],\n        reason: [\n          {required: false, min: 0, max: 2000, message: '告警原因不能超过2000字符', trigger: 'blur'},\n          {required: true, message: '请输入告警原因', trigger: 'blur'},\n        ],\n        handSuggest: [\n          {required: false, min: 0, max: 2000, message: '告警建议不能超2000字符', trigger: 'blur'},\n          {required: true, message: '请输入告警建议', trigger: 'blur'},\n        ],\n        logTime: [\n          {required: true, message: '请输入日志时间', trigger: 'blur'},\n        ],\n        createTime: [\n          {required: true, message: '请输入告警时间', trigger: 'blur'},\n        ],\n        srcIp: [\n          {required: false, min: 0, max: 30, message: '源IP不能超过30字符', trigger: 'blur'},\n          {\n            required: true,\n            pattern: '^(25[0-5]|2[0-4]\\\\d|[0-1]?\\\\d?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|[0-1]?\\\\d?\\\\d)){3}$',\n            message: \"IP地址不能为空或格式不正确\",\n            trigger: \"blur\"\n          },\n        ],\n        srcPort: [\n          {required: false, min: 0, max: 11, message: '源IP端口不能超过11字符', trigger: 'blur'},\n          {required: true, pattern: '^[0-9]*[1-9][0-9]*$', message: '源IP端口不能为空或格式不正确', trigger: 'blur'},\n        ],\n        destIp: [\n          {required: false, min: 0, max: 30, message: '目标IP不能超过30字符', trigger: 'blur'},\n          {\n            required: true,\n            pattern: '^(25[0-5]|2[0-4]\\\\d|[0-1]?\\\\d?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|[0-1]?\\\\d?\\\\d)){3}$',\n            message: \"IP地址不能为空或格式不正确\",\n            trigger: \"blur\"\n          },\n        ],\n        destPort: [\n          {required: false, min: 0, max: 11, message: '目标IP端口不能超过11字符', trigger: 'blur'},\n          {required: true, pattern: '^[0-9]*[1-9][0-9]*$', message: '目标IP端口不能为空或格式不正确', trigger: 'blur'},\n        ],\n        mateRule: [\n          {required: false, min: 0, max: 200, message: '分析规则不能超过200字符', trigger: 'blur'},\n        ],\n        associaDevice: [\n          {required: false, min: 0, max: 200, message: '关联设备不能超过200字符', trigger: 'blur'},\n        ],\n        attackType: [\n          {required: false, min: 0, max: 100, message: '攻击方式不能超过100字符', trigger: 'blur'},\n          {required: true, message: '请输入攻击方式', trigger: 'blur'},\n        ],\n        attackStage: [\n          {required: false, min: 0, max: 100, message: '攻击链阶段不能超过100字符', trigger: 'blur'},\n          {required: true, message: '请输入攻击链阶段', trigger: 'blur'},\n        ],\n        attackResult: [\n          {required: false, min: 0, max: 100, message: '攻击结果不能超过100字符', trigger: 'blur'},\n          {required: true, message: '请输入攻击结果', trigger: 'blur'},\n        ],\n      },\n      blockingForm: {},\n      blockingRules: {\n        block_ip: [\n          //可同时传多个，用\";\"隔开\n          { validator: validateBlockIp, trigger: 'blur' },\n        ],\n        duration_time: [\n          {required: true, message: '请选择阻断时长', trigger: 'blur'},\n        ],\n        remarks: [\n          {required: false, min: 0, max: 500, message: '备注不能超过500字符', trigger: 'blur'},\n        ]\n      },\n      blockingIpList: [],\n      blockingDialogVisible: false, // 批量阻断弹窗\n      editable: true,\n      assetInfoList: [],\n      openDialog: false,\n      assetData: {},\n      importDialog: false,\n      serverOpen: false,\n      assetId: null,\n      safeOpen: false,\n      threatenConfigFlag: false,\n      viewStrategy: false,\n      publishDialogVisible: false,\n      flowVisible: false,\n      flowTemplateSelectVisible: false,\n      flowStateOptions: [\n        {\n          label: '待审核',\n          value: 0\n        },\n        {\n          label: '待处置',\n          value: 1\n        },\n        {\n          label: \"待反馈审核\",\n          value: 2,\n        },\n        {\n          label: '待验证',\n          value: 3\n        },\n        {\n          label: '已完成',\n          value: 4\n        },\n        {\n          label: '待提交',\n          value: -1\n        },\n        {\n          label: '未分配',\n          value: 99\n        }\n      ],\n      handleStateOptions: [\n        {\n          label: '未处置',\n          value: '0'\n        },\n        {\n          label: '已处置',\n          value: '1'\n        },\n        {\n          label: '忽略',\n          value: '2'\n        },\n        {\n          label: '处置中',\n          value: '3'\n        }\n      ],\n      activeName: 'detail',\n      syncStateOptions: [\n        {\n          label: '未同步',\n          value: 0\n        },\n        {\n          label: '已同步',\n          value: 1\n        }\n      ],\n      blockingDuration: [\n        {\n          label: '30分钟',\n          value: '30m'\n        },\n        {\n          label: '24小时',\n          value: '24h'\n        },\n        {\n          label: '48小时',\n          value: '48h'\n        },\n        {\n          label: '7天',\n          value: '168h'\n        },\n        {\n          label: '永久',\n          value: '永久'\n        }\n      ],\n      multipleSelection: [],\n      deviceConfigList: [],\n    }\n  },\n  watch: {\n    // 监听目标ip\n    'form.destIp'(value, oldValue) {\n      var rg = /^(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)(\\.(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)){3}$/;\n      var reg = rg.test(value);\n      if (reg) {\n        // 根据ip获取资产数据\n        getAssetInfoByIp(value).then(response => {\n          if (response.data.length) {\n            let assetData = response.data;\n            assetData.forEach(item => item.value = item.assetName + '-' + item.assetTypeDesc);\n            if (value !== oldValue && oldValue) {\n              this.form.assetId = ''\n              this.form.deptId = ''\n            }\n            // 资产数据有多条显示下拉框，只有一条不显示\n            if (assetData.length === 1) {\n              this.form.assetId = assetData[0].assetId\n              this.form.deptId = assetData[0].deptId\n            }\n            if (assetData.length > 1 && !this.form.assetId) {\n              this.form.assetId = ''\n              this.form.deptId = ''\n            }\n            this.assetInfoList = assetData;\n          } else {\n            this.assetInfoList = [];\n            return this.$message.warning('未查询到资产数据');\n          }\n        })\n      } else {\n        this.assetInfoList = [];\n        this.form.assetId = '';\n        this.form.deptId = '';\n      }\n    },\n    propsActiveName() {\n      this.init()\n    },\n    propsQueryParams: {\n      handler(val) {\n        this.handlePropsQuery(val);\n      }\n    },\n    /*rangeTime(val) {\n      console.log(val)\n    },*/\n    'blockingForm.block_ip': {\n      handler(value) {\n        if (value) {\n          this.blockingIpList = value.split(';').map(ip => ip.trim()).filter(ip => ip);\n        }\n      },\n      immediate: true\n    }\n  },\n  created() {\n    this.getDeviceConfigList();\n  },\n  mounted() {\n    if (!this.$route.query || Object.keys(this.$route.query).length < 1) {\n      this.init()\n    } else {\n      this.handlePropsQuery(this.$route.query);\n    }\n  },\n  methods: {\n    init() {\n      //this.resetQuery()\n      this.getThreatenDict()\n      this.handleQuery()\n      this.getDeptsData()\n      this.getUserList()\n    },\n    getUserList(){\n      listUser({pageNum:1,pageSize:1000}).then(res=>{\n        if (res.rows){\n          this.userList = res.rows\n        }\n      })\n    },\n    handleQuery() {\n      console.log(\"queryParms1:\",this.queryParams)\n      this.propsQueryParams.alarmLevel = this.queryParams.alarmLevel\n      this.propsQueryParams.referenceId = this.queryParams.referenceId\n      //this.$emit('update:currentBtn',this.queryParams.alarmLevel?parseInt(this.queryParams.alarmLevel) : null)\n      this.queryParams = {...this.queryParams,...this.propsQueryParams};\n      if (this.rangeTime != null) {\n        this.queryParams.startTime = parseTime(this.rangeTime[0]);\n        this.queryParams.endTime = parseTime(this.rangeTime[1]);\n      } else {\n        this.queryParams.startTime = null;\n        this.queryParams.endTime = null;\n      }\n      this.queryParams.pageNum = 1;\n      this.queryParams.pageSize = 10;\n\n      if(!this.queryParams.startTime){\n        this.queryParams.startTime = parseTime(new Date().setHours(-168, 0, 0, 0), '{y}-{m}-{d} 00:00:00'); // 一周前，时间部分为 00:00:00\n      }\n      if(!this.queryParams.endTime){\n        this.queryParams.endTime = parseTime(new Date().setHours(23, 59, 59, 999), '{y}-{m}-{d} 23:59:59'); // 当前日期，时间部分为 23:59:59\n      }\n      this.rangeTime = [this.queryParams.startTime, this.queryParams.endTime];\n      this.total = 0;\n      this.getList();\n      console.log(\"queryParms2:\",this.queryParams)\n      this.$nextTick(() => {\n        const data = JSON.parse(JSON.stringify(this.queryParams))\n        if (data.threatenType != null) {\n          data.threatenType = data.threatenType.join('/');\n        }\n        this.$refs.atcAge.initAttackStage(data)\n      })\n    },\n    // 获取告警类型多级字典数据\n    getThreatenDict() {\n      getMulTypeDict({\n        dictType: 'threaten_alarm_type'\n      }).then(res => {\n        this.threatenDict = res.data;\n      })\n    },\n    // 获取部门数据\n    getDeptsData() {\n      getDeptSystem().then(res => this.deptOptions = res.data)\n    },\n    handleChange(val) {\n      // 获取所属部门最后id\n      if (val) {\n        this.queryParams.deptId = val[val.length - 1];\n      } else {\n        this.queryParams.deptId = '';\n      }\n    },\n    resetQuery() {\n      this.queryParams = {\n        threatenName: null,\n        threatenType: null,\n        alarmLevel: null,\n        referenceId: null,\n        srcIp: null,\n        destIp: null,\n        handleState: null,\n        flowState: null,\n        updateTime: null,\n        pageNum: 1,\n        pageSize: 10\n      };\n      let atcAge = this.$refs.atcAge;\n      if (atcAge) {\n        atcAge.currentSelectedCard = null;\n      }\n      this.rangeTime = null;\n      this.handleQuery();\n    },\n    //新增威胁情报\n    handleAdd() {\n      this.openThrenten = true;\n      this.form = {};\n      this.editable = true;\n      this.title = \"新增威胁情报\";\n      this.$set(this.form, 'assetId', ''); // 解决el-select无法视图与数据的更新\n    },\n    // 导入功能\n    handleImport() {\n      this.importDialog = true;\n    },\n    handleExport() {\n      this.download(\n        \"/system/threadten/export\",\n        {\n          ...this.queryParams,\n        },\n        `威胁告警_${new Date().getTime()}.xlsx`\n      );\n    },\n\n    // 获取列表数据查询\n    handleRowClick(row, column, event) {\n      // 获取告警详情单个单元格数据进行筛选\n      if (row && row.id) {\n        if (column.property) {\n          if (column.property === 'flowState') {\n            this.queryParams[column.property] = !row[column.property] ? 99 : Number(row[column.property]);\n            listAlarm({\n              [column.property]: !row[column.property] ? 99 : Number(row[column.property]),\n              pageNum: 1,\n              pageSize: 10,\n              startTime: parseTime(this.rangeTime[0]),\n              endTime: parseTime(this.rangeTime[1]),\n            }).then(response => {\n              this.threatenWarnList = response.rows;\n              this.threatenWarnList.forEach(item => {\n                item.assetType = item.assetClassDesc + '-' + item.assetTypeDesc;\n                if (item.assetType == 'null-null') {\n                  item.assetType = null;\n                }\n              });\n              this.total = response.total;\n              this.loading = false;\n            });\n            return;\n          } else if (column.property === 'threatenType') {\n            this.queryParams[column.property] = row[column.property].split('/');\n          } else if (column.property === 'alarmLevel') {\n            this.queryParams[column.property] = row[column.property].toString();\n          } else {\n            this.queryParams[column.property] = row[column.property];\n          }\n          listAlarm({\n            [column.property]: row[column.property],\n            pageNum: 1,\n            pageSize: 10,\n            startTime: parseTime(this.rangeTime[0]),\n            endTime: parseTime(this.rangeTime[1]),\n          }).then(response => {\n            this.threatenWarnList = response.rows;\n            this.threatenWarnList.forEach(item => {\n              item.assetType = item.assetClassDesc + '-' + item.assetTypeDesc;\n              if (item.assetType == 'null-null') {\n                item.assetType = null;\n              }\n            });\n            this.total = response.total;\n            this.loading = false;\n          });\n        }\n      }\n    },\n\n    // 多选\n    handleSelectionChange(val) {\n      this.multipleSelection = val;\n    },\n\n    flowStateFormatter(row, column, cellValue, index) {\n      let name = '未分配';\n      let match = this.flowStateOptions.find(item => item.value == cellValue);\n      if (match) {\n        name = match.label;\n      }\n      return name;\n    },\n    disposerFormatter(row, column, cellValue, index){\n      let name = '';\n      if (cellValue){\n        this.userList.forEach(e => {\n          if (e.userId == cellValue){\n            name = e.nickName\n          }\n        })\n        return name;\n      }\n      return name;\n    },\n\n\n    handleStateFormatter(row, column, cellValue, index) {\n      let name = '未处置';\n      let match = this.handleStateOptions.find(item => item.value == cellValue);\n      if (match) {\n        name = match.label;\n      }\n      return name;\n    },\n    handleDetail(row) {\n      this.assetData = {...row};\n      this.title = \"查看告警详情\";\n      this.openDetail(true);\n    },\n    showHandle(row) {\n      // 获取事件详情单个单元格数据进行筛选\n      if (row.handleState === '1' || row.handleState === '2' ) {\n        this.handleForm.handleState = parseInt(row.handleState);\n        this.handleForm.handleDesc = row.handleDesc;\n      }else {\n        this.handleForm = {\n          handleDesc: '',\n          handleState: ''\n        }\n      }\n      this.handleForm.id = row.id;\n      this.showHandleDialog = true;\n    },\n    handleEdit(row) {\n      getAlarm(row.id).then(res => {\n        this.form = {...res.data};\n        if (this.form.alarmLevel != null) {\n          this.form.alarmLevel = (this.form.alarmLevel).toString();\n        }\n        if (this.form.threatenType != null) {\n          this.form.threatenType = this.form.threatenType.split('/');\n        }\n        if (this.form.attackNum != null) {\n          this.form.attackNum = (this.form.attackNum).toString();\n        }\n        if (this.form.srcPort != null) {\n          this.form.srcPort = (this.form.srcPort).toString();\n        }\n        if (this.form.destPort != null) {\n          this.form.destPort = (this.form.destPort).toString();\n        }\n        this.title = \"修改威胁情报\";\n        this.openThrenten = true;\n      })\n    },\n    handleDelete(row) {\n      const ids = row.id;\n      const title = row.threatenName;\n      this.$modal.confirm('是否确认删除告警名称为【' + title + '】的数据项?').then(() => {\n        return delAlarm(ids);\n      }).then(() => {\n        this.$message.success(\"删除成功\");\n        this.getList();\n      }).catch(() => {\n\n      })\n    },\n    addOrUpdateFlowHandle(id, flowState, row) {\n      let data = {\n        id: id || '',\n        formType: 1,\n        opType: flowState ? 0 : '-1',\n        status: flowState,\n        row: row,\n        isWork: true\n      }\n      data.row.workType = '2';\n      data.row.eventType = 3;\n      data.originType = 'event';\n      this.currentFlowData = data;\n      this.loading = true;\n      this.getConfigKey(\"default.flowTemplateId\").then(res => {\n        let flowId = res.msg;\n        if (flowId) {\n          this.getFlowEngineInfo(flowId);\n        } else {\n          this.flowTemplateSelectVisible = true;\n        }\n      }).finally(() => {\n        this.loading = false;\n      })\n    },\n    getFlowEngineInfo(val) {\n      FlowEngineInfo(val).then(res => {\n        if (res.data && res.data.flowTemplateJson) {\n          let data = JSON.parse(res.data.flowTemplateJson);\n          if (!data[0].flowId) {\n            this.$message.error('该流程模板异常,请重新选择');\n          } else {\n            this.currentFlowData.flowId = data[0].flowId;\n            this.flowVisible = true;\n            this.$nextTick(() => {\n              this.$refs.FlowBox.init(this.currentFlowData);\n            });\n          }\n        }\n      }).finally(() => {\n        this.loading = false;\n      })\n    },\n    getList() {\n      this.loading = true;\n      let queryParams = {\n        ...this.queryParams\n      };\n      if (queryParams.threatenType != null) {\n        queryParams.threatenType = queryParams.threatenType.join('/');\n      }\n      //同步请求类型统计数据\n      this.$emit('getList',{...queryParams});\n      listAlarm(queryParams).then(response => {\n        this.threatenWarnList = response.rows;\n        this.threatenWarnList.forEach(item => {\n          item.assetType = item.assetClassDesc + '-' + item.assetTypeDesc;\n          if (item.assetType == 'null-null') {\n            item.assetType = null;\n          }\n          if (item.deptName) {\n            let deptNameArr = uniqueArr(item.deptName.split(','));\n            item.deptName = deptNameArr.join(',');\n          }\n        });\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    handleClose(done) {\n      done();\n      this.form = {};\n      this.$refs.form.resetFields();\n    },\n    submitHandleForm() {\n      this.$refs[\"handleStateForm\"].validate(valid => {\n        if (valid) {\n          updateAlarm(this.handleForm).then(res => {\n            this.$message.success(\"处置成功\");\n            this.handleForm = {};\n            this.showHandleDialog = false;\n            this.getList();\n          })\n        }\n      });\n    },\n    submitForm() {\n      if (this.form.threatenType != null) {\n        this.form.threatenType = this.form.threatenType.join('/');\n      }\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.id == null) {\n            addAlarm(this.form).then(res => {\n              this.$message.success(\"新增成功\");\n              this.form = {};\n              this.openThrenten = false;\n              this.getList();\n            })\n          } else {\n            updateAlarm(this.form).then(res => {\n              this.$message.success(\"修改成功\");\n              this.form = {};\n              this.openThrenten = false;\n              this.getList();\n            })\n          }\n        }\n      })\n    },\n    cancel() {\n      this.openThrenten = false;\n      this.$refs.form.resetFields();\n    },\n    openDetail(val) {\n      this.openDialog = val;\n    },\n    closeDialog() {\n      this.importDialog = false;\n      this.handleQuery();\n    },\n    closeAssetDialog() {\n      this.serverOpen = false;\n      this.safeOpen = false;\n      this.networkOpen = false;\n    },\n    closeFlow(isrRefresh) {\n      this.flowVisible = false\n      if (isrRefresh) this.getList();\n    },\n    flowTemplateSelectChange(val) {\n      this.flowTemplateSelectVisible = false;\n      this.flowVisible = true;\n      this.currentFlowData.flowId = val;\n      this.$nextTick(() => {\n        this.$refs.FlowBox.init(this.currentFlowData)\n      })\n    },\n    handleAtcAgeClick(atcAge) {\n      this.queryParams.attackSeg = atcAge;\n      this.handleQuery();\n    },\n    handlePropsQuery(val) {\n      if (val && Object.keys(val).length > 0) {\n        if (val.attackSeg && this.$refs.atcAge) {\n          this.$refs.atcAge.currentSelectedCard = val.attackSeg;\n        }\n        this.queryParams = val;\n        if (val.startTime && val.endTime) {\n          this.rangeTime = [val.startTime, val.endTime];\n        }\n        if (val.handle == '1') {\n          this.queryParams.handleState = '1'\n        } else if (val.handle == '0') {\n          this.queryParams.handleState = '0'\n        }\n        if (val.datasource) {\n          this.queryParams.dataSource = parseInt(val.datasource);\n        }\n        this.getThreatenDict()\n        this.getDeptsData()\n        this.handleQuery()\n      }\n    },\n    handleApplicationTagShow(applicationList) {\n      if (!applicationList || applicationList.length < 1) {\n        return '';\n      }\n      let result = applicationList[0].assetName;\n      if (applicationList.length > 1) {\n        result += '...';\n      }\n      return result;\n    },\n\n    handleBlocking() {\n      if (this.multipleSelection.length < 1) return this.$message.warning('请选择要阻断的ip');\n      this.blockingDialogVisible = true;\n      let arr = this.multipleSelection.map(item => item.srcIp);\n      arr = Array.from(new Set(arr));\n      this.$set(this.blockingForm,'block_ip',arr.join(';'));\n    },\n    blockingSubmit() {\n      this.$refs[\"blockingForm\"].validate(valid => {\n        if (valid) {\n          addBlockIp(this.blockingForm).then(res => {\n            this.$message.success('添加成功');\n          }).finally(() => {\n            this.blockingDialogVisible = false;\n            this.$refs.multipleTable.clearSelection();\n            this.multipleSelection = [];\n          })\n        }\n      })\n    },\n    getDeviceConfigList(){\n      listDeviceConfig({queryAllData: true}).then(res => {\n        this.deviceConfigList = res.rows;\n      })\n    },\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.el-divider {\n  background: #0E94EA;\n}\n\n.el-divider--vertical {\n  display: inline-block;\n  width: 5px;\n  height: 2em;\n  margin: 0 8px 0 0;\n  vertical-align: middle;\n  position: relative;\n}\n\n.my-title {\n  display: inline-block;\n  vertical-align: center;\n}\n\n\n.asset-tag {\n  margin-left: 5px;\n}\n\n.asset-tag {\n  max-width: 100%;\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  vertical-align: middle;\n}\n\n.el-tooltip__popper {\n  font-size: 12px;\n  max-width: 300px;\n}\n\n.overflow-tag:not(:first-child) {\n  margin-top: 5px;\n}\n.blocking-form {\n  ::v-deep .el-form-item__label {\n    float: none;\n  }\n}\n\n::v-deep .el-tabs__content {\n  overflow: hidden;\n}\n::v-deep .el-dialog__body {\n  max-height: 80vh;\n  overflow-y: auto;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmoBA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,kBAAA,GAAAF,OAAA;AACA,IAAAG,aAAA,GAAAH,OAAA;AACA,IAAAI,SAAA,GAAAJ,OAAA;AACA,IAAAK,WAAA,GAAAC,sBAAA,CAAAN,OAAA;AACA,IAAAO,YAAA,GAAAD,sBAAA,CAAAN,OAAA;AACA,IAAAQ,eAAA,GAAAF,sBAAA,CAAAN,OAAA;AACA,IAAAS,mBAAA,GAAAH,sBAAA,CAAAN,OAAA;AACA,IAAAU,UAAA,GAAAJ,sBAAA,CAAAN,OAAA;AACA,IAAAW,QAAA,GAAAL,sBAAA,CAAAN,OAAA;AACA,IAAAY,aAAA,GAAAN,sBAAA,CAAAN,OAAA;AACA,IAAAa,mBAAA,GAAAP,sBAAA,CAAAN,OAAA;AACA,IAAAc,QAAA,GAAAR,sBAAA,CAAAN,OAAA;AACA,IAAAe,mBAAA,GAAAT,sBAAA,CAAAN,OAAA;AACA,IAAAgB,YAAA,GAAAV,sBAAA,CAAAN,OAAA;AACA,IAAAiB,eAAA,GAAAX,sBAAA,CAAAN,OAAA;AACA,IAAAkB,eAAA,GAAAZ,sBAAA,CAAAN,OAAA;AACA,IAAAmB,MAAA,GAAAb,sBAAA,CAAAN,OAAA;AAEA,IAAAoB,WAAA,GAAAd,sBAAA,CAAAN,OAAA;AACA,IAAAqB,MAAA,GAAArB,OAAA;AACA,IAAAsB,WAAA,GAAAtB,OAAA;AACA,IAAAuB,KAAA,GAAAvB,OAAA;AACA,IAAAwB,gBAAA,GAAAlB,sBAAA,CAAAN,OAAA;AACA,IAAAyB,aAAA,GAAAzB,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAIA;EACA0B,IAAA;EACAC,UAAA;IACAC,eAAA,EAAAA,wBAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,cAAA,EAAAA,uBAAA;IACAC,cAAA,EAAAA,uBAAA;IACAC,WAAA,EAAAA,oBAAA;IACAC,kBAAA,EAAAA,2BAAA;IACAC,OAAA,EAAAA,gBAAA;IACAC,kBAAA,EAAAA,2BAAA;IACAC,YAAA,EAAAA,cAAA;IACAC,YAAA,EAAAA,cAAA;IACAC,kBAAA,EAAAA,2BAAA;IAAAC,YAAA,EAAAA,qBAAA;IAAAC,OAAA,EAAAA,gBAAA;IAAAC,SAAA,EAAAA,kBAAA;IAAAC,cAAA,EAAAA,uBAAA;IAAAC,WAAA,EAAAA,oBAAA;IAAAC,UAAA,EAAAA;EACA;EACAC,KAAA;EACAC,KAAA;IACAC,eAAA;MACAC,IAAA,EAAAC;IACA;IACAC,gBAAA;MACAF,IAAA,EAAAG,MAAA;MACAC,OAAA,WAAAA,SAAA;QACA;MACA;IACA;IACAC,UAAA;MACAL,IAAA,EAAAM,MAAA;MACAF,OAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA,IAAAC,eAAA,YAAAA,gBAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,KAAAD,KAAA;QACA,OAAAC,QAAA,KAAAC,KAAA;MACA;MACA;MACA,IAAAC,OAAA;MACA,KAAAA,OAAA,CAAAC,IAAA,CAAAJ,KAAA;QACA,OAAAC,QAAA,KAAAC,KAAA;MACA;MACA,OAAAD,QAAA;IACA;IACA;MACAI,QAAA;MACAC,gBAAA;MACAC,UAAA;QACAC,EAAA;QACAC,UAAA;QACAC,WAAA;MACA;MACAC,WAAA;QACAD,WAAA,GACA;UAAAE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,OAAA;MACAC,YAAA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACAC,WAAA;MACAC,SAAA;MACAC,OAAA;MACAC,gBAAA;MACAC,KAAA;MACAC,KAAA;MACAC,YAAA;MACAC,IAAA;MACAC,KAAA;QACAC,YAAA,GACA;UAAAjB,QAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAlB,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UACAF,QAAA;UACAT,OAAA;UACAU,OAAA;UACAC,OAAA;QACA,EACA;QACAkB,UAAA,GACA;UAAApB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAmB,YAAA,GACA;UAAArB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAoB,MAAA,GACA;UAAAtB,QAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAlB,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAqB,WAAA,GACA;UAAAvB,QAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAlB,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAsB,OAAA,GACA;UAAAxB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAuB,UAAA,GACA;UAAAzB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAwB,KAAA,GACA;UAAA1B,QAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAlB,OAAA;UAAAC,OAAA;QAAA,GACA;UACAF,QAAA;UACAT,OAAA;UACAU,OAAA;UACAC,OAAA;QACA,EACA;QACAyB,OAAA,GACA;UAAA3B,QAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAlB,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAF,QAAA;UAAAT,OAAA;UAAAU,OAAA;UAAAC,OAAA;QAAA,EACA;QACA0B,MAAA,GACA;UAAA5B,QAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAlB,OAAA;UAAAC,OAAA;QAAA,GACA;UACAF,QAAA;UACAT,OAAA;UACAU,OAAA;UACAC,OAAA;QACA,EACA;QACA2B,QAAA,GACA;UAAA7B,QAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAlB,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAF,QAAA;UAAAT,OAAA;UAAAU,OAAA;UAAAC,OAAA;QAAA,EACA;QACA4B,QAAA,GACA;UAAA9B,QAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAlB,OAAA;UAAAC,OAAA;QAAA,EACA;QACA6B,aAAA,GACA;UAAA/B,QAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAlB,OAAA;UAAAC,OAAA;QAAA,EACA;QACA8B,UAAA,GACA;UAAAhC,QAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAlB,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA+B,WAAA,GACA;UAAAjC,QAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAlB,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAgC,YAAA,GACA;UAAAlC,QAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAlB,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAiC,YAAA;MACAC,aAAA;QACAC,QAAA;QACA;QACA;UAAAC,SAAA,EAAApD,eAAA;UAAAgB,OAAA;QAAA,EACA;QACAqC,aAAA,GACA;UAAAvC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAsC,OAAA,GACA;UAAAxC,QAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAlB,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAuC,cAAA;MACAC,qBAAA;MAAA;MACAC,QAAA;MACAC,aAAA;MACAC,UAAA;MACAC,SAAA;MACAC,YAAA;MACAC,UAAA;MACAC,OAAA;MACAC,QAAA;MACAC,kBAAA;MACAC,YAAA;MACAC,oBAAA;MACAC,WAAA;MACAC,yBAAA;MACAC,gBAAA,GACA;QACAC,KAAA;QACArE,KAAA;MACA,GACA;QACAqE,KAAA;QACArE,KAAA;MACA,GACA;QACAqE,KAAA;QACArE,KAAA;MACA,GACA;QACAqE,KAAA;QACArE,KAAA;MACA,GACA;QACAqE,KAAA;QACArE,KAAA;MACA,GACA;QACAqE,KAAA;QACArE,KAAA;MACA,GACA;QACAqE,KAAA;QACArE,KAAA;MACA,EACA;MACAsE,kBAAA,GACA;QACAD,KAAA;QACArE,KAAA;MACA,GACA;QACAqE,KAAA;QACArE,KAAA;MACA,GACA;QACAqE,KAAA;QACArE,KAAA;MACA,GACA;QACAqE,KAAA;QACArE,KAAA;MACA,EACA;MACAuE,UAAA;MACAC,gBAAA,GACA;QACAH,KAAA;QACArE,KAAA;MACA,GACA;QACAqE,KAAA;QACArE,KAAA;MACA,EACA;MACAyE,gBAAA,GACA;QACAJ,KAAA;QACArE,KAAA;MACA,GACA;QACAqE,KAAA;QACArE,KAAA;MACA,GACA;QACAqE,KAAA;QACArE,KAAA;MACA,GACA;QACAqE,KAAA;QACArE,KAAA;MACA,GACA;QACAqE,KAAA;QACArE,KAAA;MACA,EACA;MACA0E,iBAAA;MACAC,gBAAA;IACA;EACA;EACAC,KAAA;IACA;IACA,wBAAAC,WAAA7E,KAAA,EAAA8E,QAAA;MAAA,IAAAC,KAAA;MACA,IAAAC,EAAA;MACA,IAAAC,GAAA,GAAAD,EAAA,CAAA5E,IAAA,CAAAJ,KAAA;MACA,IAAAiF,GAAA;QACA;QACA,IAAAC,0BAAA,EAAAlF,KAAA,EAAAmF,IAAA,WAAAC,QAAA;UACA,IAAAA,QAAA,CAAAvF,IAAA,CAAAwF,MAAA;YACA,IAAA3B,SAAA,GAAA0B,QAAA,CAAAvF,IAAA;YACA6D,SAAA,CAAA4B,OAAA,WAAAC,IAAA;cAAA,OAAAA,IAAA,CAAAvF,KAAA,GAAAuF,IAAA,CAAAC,SAAA,SAAAD,IAAA,CAAAE,aAAA;YAAA;YACA,IAAAzF,KAAA,KAAA8E,QAAA,IAAAA,QAAA;cACAC,KAAA,CAAApD,IAAA,CAAAkC,OAAA;cACAkB,KAAA,CAAApD,IAAA,CAAA+D,MAAA;YACA;YACA;YACA,IAAAhC,SAAA,CAAA2B,MAAA;cACAN,KAAA,CAAApD,IAAA,CAAAkC,OAAA,GAAAH,SAAA,IAAAG,OAAA;cACAkB,KAAA,CAAApD,IAAA,CAAA+D,MAAA,GAAAhC,SAAA,IAAAgC,MAAA;YACA;YACA,IAAAhC,SAAA,CAAA2B,MAAA,SAAAN,KAAA,CAAApD,IAAA,CAAAkC,OAAA;cACAkB,KAAA,CAAApD,IAAA,CAAAkC,OAAA;cACAkB,KAAA,CAAApD,IAAA,CAAA+D,MAAA;YACA;YACAX,KAAA,CAAAvB,aAAA,GAAAE,SAAA;UACA;YACAqB,KAAA,CAAAvB,aAAA;YACA,OAAAuB,KAAA,CAAAY,QAAA,CAAAC,OAAA;UACA;QACA;MACA;QACA,KAAApC,aAAA;QACA,KAAA7B,IAAA,CAAAkC,OAAA;QACA,KAAAlC,IAAA,CAAA+D,MAAA;MACA;IACA;IACArG,eAAA,WAAAA,gBAAA;MACA,KAAAwG,IAAA;IACA;IACArG,gBAAA;MACAsG,OAAA,WAAAA,QAAAC,GAAA;QACA,KAAAC,gBAAA,CAAAD,GAAA;MACA;IACA;IACA;AACA;AACA;IACA;MACAD,OAAA,WAAAA,QAAA9F,KAAA;QACA,IAAAA,KAAA;UACA,KAAAqD,cAAA,GAAArD,KAAA,CAAAiG,KAAA,MAAAC,GAAA,WAAAC,EAAA;YAAA,OAAAA,EAAA,CAAAC,IAAA;UAAA,GAAAC,MAAA,WAAAF,EAAA;YAAA,OAAAA,EAAA;UAAA;QACA;MACA;MACAG,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,mBAAA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,UAAAC,MAAA,CAAAC,KAAA,IAAAlH,MAAA,CAAAmH,IAAA,MAAAF,MAAA,CAAAC,KAAA,EAAAtB,MAAA;MACA,KAAAQ,IAAA;IACA;MACA,KAAAG,gBAAA,MAAAU,MAAA,CAAAC,KAAA;IACA;EACA;EACAE,OAAA;IACAhB,IAAA,WAAAA,KAAA;MACA;MACA,KAAAiB,eAAA;MACA,KAAAC,WAAA;MACA,KAAAC,YAAA;MACA,KAAAC,WAAA;IACA;IACAA,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,cAAA;QAAAjG,OAAA;QAAAC,QAAA;MAAA,GAAAgE,IAAA,WAAAiC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAH,MAAA,CAAA7G,QAAA,GAAA+G,GAAA,CAAAC,IAAA;QACA;MACA;IACA;IACAN,WAAA,WAAAA,YAAA;MAAA,IAAAO,MAAA;MACAC,OAAA,CAAAC,GAAA,sBAAAvG,WAAA;MACA,KAAAzB,gBAAA,CAAAwC,UAAA,QAAAf,WAAA,CAAAe,UAAA;MACA,KAAAxC,gBAAA,CAAAiI,WAAA,QAAAxG,WAAA,CAAAwG,WAAA;MACA;MACA,KAAAxG,WAAA,OAAAyG,cAAA,CAAAhI,OAAA,MAAAgI,cAAA,CAAAhI,OAAA,WAAAuB,WAAA,QAAAzB,gBAAA;MACA,SAAA6B,SAAA;QACA,KAAAJ,WAAA,CAAA0G,SAAA,OAAAC,gBAAA,OAAAvG,SAAA;QACA,KAAAJ,WAAA,CAAA4G,OAAA,OAAAD,gBAAA,OAAAvG,SAAA;MACA;QACA,KAAAJ,WAAA,CAAA0G,SAAA;QACA,KAAA1G,WAAA,CAAA4G,OAAA;MACA;MACA,KAAA5G,WAAA,CAAAC,OAAA;MACA,KAAAD,WAAA,CAAAE,QAAA;MAEA,UAAAF,WAAA,CAAA0G,SAAA;QACA,KAAA1G,WAAA,CAAA0G,SAAA,OAAAC,gBAAA,MAAAE,IAAA,GAAAC,QAAA;MACA;MACA,UAAA9G,WAAA,CAAA4G,OAAA;QACA,KAAA5G,WAAA,CAAA4G,OAAA,OAAAD,gBAAA,MAAAE,IAAA,GAAAC,QAAA;MACA;MACA,KAAA1G,SAAA,SAAAJ,WAAA,CAAA0G,SAAA,OAAA1G,WAAA,CAAA4G,OAAA;MACA,KAAArG,KAAA;MACA,KAAAwG,OAAA;MACAT,OAAA,CAAAC,GAAA,sBAAAvG,WAAA;MACA,KAAAgH,SAAA;QACA,IAAApI,IAAA,GAAAqI,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAd,MAAA,CAAArG,WAAA;QACA,IAAApB,IAAA,CAAAoC,YAAA;UACApC,IAAA,CAAAoC,YAAA,GAAApC,IAAA,CAAAoC,YAAA,CAAAoG,IAAA;QACA;QACAf,MAAA,CAAAgB,KAAA,CAAAC,MAAA,CAAAC,eAAA,CAAA3I,IAAA;MACA;IACA;IACA;IACAiH,eAAA,WAAAA,gBAAA;MAAA,IAAA2B,MAAA;MACA,IAAAC,oBAAA;QACAC,QAAA;MACA,GAAAxD,IAAA,WAAAiC,GAAA;QACAqB,MAAA,CAAAzH,YAAA,GAAAoG,GAAA,CAAAvH,IAAA;MACA;IACA;IACA;IACAmH,YAAA,WAAAA,aAAA;MAAA,IAAA4B,MAAA;MACA,IAAAC,gCAAA,IAAA1D,IAAA,WAAAiC,GAAA;QAAA,OAAAwB,MAAA,CAAAxH,WAAA,GAAAgG,GAAA,CAAAvH,IAAA;MAAA;IACA;IACAiJ,YAAA,WAAAA,aAAA/C,GAAA;MACA;MACA,IAAAA,GAAA;QACA,KAAA9E,WAAA,CAAAyE,MAAA,GAAAK,GAAA,CAAAA,GAAA,CAAAV,MAAA;MACA;QACA,KAAApE,WAAA,CAAAyE,MAAA;MACA;IACA;IACAqD,UAAA,WAAAA,WAAA;MACA,KAAA9H,WAAA;QACAY,YAAA;QACAI,YAAA;QACAD,UAAA;QACAyF,WAAA;QACAnF,KAAA;QACAE,MAAA;QACA9B,WAAA;QACAsI,SAAA;QACAC,UAAA;QACA/H,OAAA;QACAC,QAAA;MACA;MACA,IAAAoH,MAAA,QAAAD,KAAA,CAAAC,MAAA;MACA,IAAAA,MAAA;QACAA,MAAA,CAAAW,mBAAA;MACA;MACA,KAAA7H,SAAA;MACA,KAAA0F,WAAA;IACA;IACA;IACAoC,SAAA,WAAAA,UAAA;MACA,KAAAzH,YAAA;MACA,KAAAC,IAAA;MACA,KAAA4B,QAAA;MACA,KAAA9B,KAAA;MACA,KAAA2H,IAAA,MAAAzH,IAAA;IACA;IACA;IACA0H,YAAA,WAAAA,aAAA;MACA,KAAA1F,YAAA;IACA;IACA2F,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,CACA,gCAAA7B,cAAA,CAAAhI,OAAA,MAEA,KAAAuB,WAAA,+BAAAuI,MAAA,CAEA,IAAA1B,IAAA,GAAA2B,OAAA,YACA;IACA;IAEA;IACAC,cAAA,WAAAA,eAAAC,GAAA,EAAAC,MAAA,EAAAC,KAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAH,GAAA,IAAAA,GAAA,CAAAnJ,EAAA;QACA,IAAAoJ,MAAA,CAAAG,QAAA;UACA,IAAAH,MAAA,CAAAG,QAAA;YACA,KAAA9I,WAAA,CAAA2I,MAAA,CAAAG,QAAA,KAAAJ,GAAA,CAAAC,MAAA,CAAAG,QAAA,SAAAnK,MAAA,CAAA+J,GAAA,CAAAC,MAAA,CAAAG,QAAA;YACA,IAAAC,uBAAA,MAAAC,gBAAA,CAAAvK,OAAA,MAAAuK,gBAAA,CAAAvK,OAAA,MAAAuK,gBAAA,CAAAvK,OAAA,MAAAuK,gBAAA,CAAAvK,OAAA,MAAAuK,gBAAA,CAAAvK,OAAA,MACAkK,MAAA,CAAAG,QAAA,GAAAJ,GAAA,CAAAC,MAAA,CAAAG,QAAA,SAAAnK,MAAA,CAAA+J,GAAA,CAAAC,MAAA,CAAAG,QAAA,gBACA,gBACA,kBACA,IAAAnC,gBAAA,OAAAvG,SAAA,kBACA,IAAAuG,gBAAA,OAAAvG,SAAA,KACA,EAAA8D,IAAA,WAAAC,QAAA;cACA0E,MAAA,CAAAvI,gBAAA,GAAA6D,QAAA,CAAAiC,IAAA;cACAyC,MAAA,CAAAvI,gBAAA,CAAA+D,OAAA,WAAAC,IAAA;gBACAA,IAAA,CAAA2E,SAAA,GAAA3E,IAAA,CAAA4E,cAAA,SAAA5E,IAAA,CAAAE,aAAA;gBACA,IAAAF,IAAA,CAAA2E,SAAA;kBACA3E,IAAA,CAAA2E,SAAA;gBACA;cACA;cACAJ,MAAA,CAAAtI,KAAA,GAAA4D,QAAA,CAAA5D,KAAA;cACAsI,MAAA,CAAAxI,OAAA;YACA;YACA;UACA,WAAAsI,MAAA,CAAAG,QAAA;YACA,KAAA9I,WAAA,CAAA2I,MAAA,CAAAG,QAAA,IAAAJ,GAAA,CAAAC,MAAA,CAAAG,QAAA,EAAA9D,KAAA;UACA,WAAA2D,MAAA,CAAAG,QAAA;YACA,KAAA9I,WAAA,CAAA2I,MAAA,CAAAG,QAAA,IAAAJ,GAAA,CAAAC,MAAA,CAAAG,QAAA,EAAAK,QAAA;UACA;YACA,KAAAnJ,WAAA,CAAA2I,MAAA,CAAAG,QAAA,IAAAJ,GAAA,CAAAC,MAAA,CAAAG,QAAA;UACA;UACA,IAAAC,uBAAA,MAAAC,gBAAA,CAAAvK,OAAA,MAAAuK,gBAAA,CAAAvK,OAAA,MAAAuK,gBAAA,CAAAvK,OAAA,MAAAuK,gBAAA,CAAAvK,OAAA,MAAAuK,gBAAA,CAAAvK,OAAA,MACAkK,MAAA,CAAAG,QAAA,EAAAJ,GAAA,CAAAC,MAAA,CAAAG,QAAA,eACA,gBACA,kBACA,IAAAnC,gBAAA,OAAAvG,SAAA,kBACA,IAAAuG,gBAAA,OAAAvG,SAAA,KACA,EAAA8D,IAAA,WAAAC,QAAA;YACA0E,MAAA,CAAAvI,gBAAA,GAAA6D,QAAA,CAAAiC,IAAA;YACAyC,MAAA,CAAAvI,gBAAA,CAAA+D,OAAA,WAAAC,IAAA;cACAA,IAAA,CAAA2E,SAAA,GAAA3E,IAAA,CAAA4E,cAAA,SAAA5E,IAAA,CAAAE,aAAA;cACA,IAAAF,IAAA,CAAA2E,SAAA;gBACA3E,IAAA,CAAA2E,SAAA;cACA;YACA;YACAJ,MAAA,CAAAtI,KAAA,GAAA4D,QAAA,CAAA5D,KAAA;YACAsI,MAAA,CAAAxI,OAAA;UACA;QACA;MACA;IACA;IAEA;IACA+I,qBAAA,WAAAA,sBAAAtE,GAAA;MACA,KAAArB,iBAAA,GAAAqB,GAAA;IACA;IAEAuE,kBAAA,WAAAA,mBAAAX,GAAA,EAAAC,MAAA,EAAAW,SAAA,EAAAC,KAAA;MACA,IAAAxM,IAAA;MACA,IAAAyM,KAAA,QAAArG,gBAAA,CAAAsG,IAAA,WAAAnF,IAAA;QAAA,OAAAA,IAAA,CAAAvF,KAAA,IAAAuK,SAAA;MAAA;MACA,IAAAE,KAAA;QACAzM,IAAA,GAAAyM,KAAA,CAAApG,KAAA;MACA;MACA,OAAArG,IAAA;IACA;IACA2M,iBAAA,WAAAA,kBAAAhB,GAAA,EAAAC,MAAA,EAAAW,SAAA,EAAAC,KAAA;MACA,IAAAxM,IAAA;MACA,IAAAuM,SAAA;QACA,KAAAlK,QAAA,CAAAiF,OAAA,WAAAsF,CAAA;UACA,IAAAA,CAAA,CAAAC,MAAA,IAAAN,SAAA;YACAvM,IAAA,GAAA4M,CAAA,CAAAE,QAAA;UACA;QACA;QACA,OAAA9M,IAAA;MACA;MACA,OAAAA,IAAA;IACA;IAGA+M,oBAAA,WAAAA,qBAAApB,GAAA,EAAAC,MAAA,EAAAW,SAAA,EAAAC,KAAA;MACA,IAAAxM,IAAA;MACA,IAAAyM,KAAA,QAAAnG,kBAAA,CAAAoG,IAAA,WAAAnF,IAAA;QAAA,OAAAA,IAAA,CAAAvF,KAAA,IAAAuK,SAAA;MAAA;MACA,IAAAE,KAAA;QACAzM,IAAA,GAAAyM,KAAA,CAAApG,KAAA;MACA;MACA,OAAArG,IAAA;IACA;IACAgN,YAAA,WAAAA,aAAArB,GAAA;MACA,KAAAjG,SAAA,OAAAgE,cAAA,CAAAhI,OAAA,MAAAiK,GAAA;MACA,KAAAlI,KAAA;MACA,KAAAwJ,UAAA;IACA;IACAC,UAAA,WAAAA,WAAAvB,GAAA;MACA;MACA,IAAAA,GAAA,CAAAjJ,WAAA,YAAAiJ,GAAA,CAAAjJ,WAAA;QACA,KAAAH,UAAA,CAAAG,WAAA,GAAAyK,QAAA,CAAAxB,GAAA,CAAAjJ,WAAA;QACA,KAAAH,UAAA,CAAAE,UAAA,GAAAkJ,GAAA,CAAAlJ,UAAA;MACA;QACA,KAAAF,UAAA;UACAE,UAAA;UACAC,WAAA;QACA;MACA;MACA,KAAAH,UAAA,CAAAC,EAAA,GAAAmJ,GAAA,CAAAnJ,EAAA;MACA,KAAAF,gBAAA;IACA;IACA8K,UAAA,WAAAA,WAAAzB,GAAA;MAAA,IAAA0B,MAAA;MACA,IAAAC,sBAAA,EAAA3B,GAAA,CAAAnJ,EAAA,EAAA2E,IAAA,WAAAiC,GAAA;QACAiE,MAAA,CAAA1J,IAAA,OAAA+F,cAAA,CAAAhI,OAAA,MAAA0H,GAAA,CAAAvH,IAAA;QACA,IAAAwL,MAAA,CAAA1J,IAAA,CAAAK,UAAA;UACAqJ,MAAA,CAAA1J,IAAA,CAAAK,UAAA,GAAAqJ,MAAA,CAAA1J,IAAA,CAAAK,UAAA,CAAAoI,QAAA;QACA;QACA,IAAAiB,MAAA,CAAA1J,IAAA,CAAAM,YAAA;UACAoJ,MAAA,CAAA1J,IAAA,CAAAM,YAAA,GAAAoJ,MAAA,CAAA1J,IAAA,CAAAM,YAAA,CAAAgE,KAAA;QACA;QACA,IAAAoF,MAAA,CAAA1J,IAAA,CAAA4J,SAAA;UACAF,MAAA,CAAA1J,IAAA,CAAA4J,SAAA,GAAAF,MAAA,CAAA1J,IAAA,CAAA4J,SAAA,CAAAnB,QAAA;QACA;QACA,IAAAiB,MAAA,CAAA1J,IAAA,CAAAY,OAAA;UACA8I,MAAA,CAAA1J,IAAA,CAAAY,OAAA,GAAA8I,MAAA,CAAA1J,IAAA,CAAAY,OAAA,CAAA6H,QAAA;QACA;QACA,IAAAiB,MAAA,CAAA1J,IAAA,CAAAc,QAAA;UACA4I,MAAA,CAAA1J,IAAA,CAAAc,QAAA,GAAA4I,MAAA,CAAA1J,IAAA,CAAAc,QAAA,CAAA2H,QAAA;QACA;QACAiB,MAAA,CAAA5J,KAAA;QACA4J,MAAA,CAAA3J,YAAA;MACA;IACA;IACA8J,YAAA,WAAAA,aAAA7B,GAAA;MAAA,IAAA8B,MAAA;MACA,IAAAC,GAAA,GAAA/B,GAAA,CAAAnJ,EAAA;MACA,IAAAiB,KAAA,GAAAkI,GAAA,CAAA9H,YAAA;MACA,KAAA8J,MAAA,CAAAC,OAAA,kBAAAnK,KAAA,aAAA0D,IAAA;QACA,WAAA0G,sBAAA,EAAAH,GAAA;MACA,GAAAvG,IAAA;QACAsG,MAAA,CAAA9F,QAAA,CAAAmG,OAAA;QACAL,MAAA,CAAAzD,OAAA;MACA,GAAA+D,KAAA,cAEA;IACA;IACAC,qBAAA,WAAAA,sBAAAxL,EAAA,EAAAwI,SAAA,EAAAW,GAAA;MAAA,IAAAsC,MAAA;MACA,IAAApM,IAAA;QACAW,EAAA,EAAAA,EAAA;QACA0L,QAAA;QACAC,MAAA,EAAAnD,SAAA;QACAoD,MAAA,EAAApD,SAAA;QACAW,GAAA,EAAAA,GAAA;QACA0C,MAAA;MACA;MACAxM,IAAA,CAAA8J,GAAA,CAAA2C,QAAA;MACAzM,IAAA,CAAA8J,GAAA,CAAA4C,SAAA;MACA1M,IAAA,CAAA2M,UAAA;MACA,KAAAC,eAAA,GAAA5M,IAAA;MACA,KAAAyB,OAAA;MACA,KAAAoL,YAAA,2BAAAvH,IAAA,WAAAiC,GAAA;QACA,IAAAuF,MAAA,GAAAvF,GAAA,CAAAwF,GAAA;QACA,IAAAD,MAAA;UACAV,MAAA,CAAAY,iBAAA,CAAAF,MAAA;QACA;UACAV,MAAA,CAAA9H,yBAAA;QACA;MACA,GAAA2I,OAAA;QACAb,MAAA,CAAA3K,OAAA;MACA;IACA;IACAuL,iBAAA,WAAAA,kBAAA9G,GAAA;MAAA,IAAAgH,OAAA;MACA,IAAAC,0BAAA,EAAAjH,GAAA,EAAAZ,IAAA,WAAAiC,GAAA;QACA,IAAAA,GAAA,CAAAvH,IAAA,IAAAuH,GAAA,CAAAvH,IAAA,CAAAoN,gBAAA;UACA,IAAApN,IAAA,GAAAqI,IAAA,CAAAC,KAAA,CAAAf,GAAA,CAAAvH,IAAA,CAAAoN,gBAAA;UACA,KAAApN,IAAA,IAAA8M,MAAA;YACAI,OAAA,CAAApH,QAAA,CAAAuH,KAAA;UACA;YACAH,OAAA,CAAAN,eAAA,CAAAE,MAAA,GAAA9M,IAAA,IAAA8M,MAAA;YACAI,OAAA,CAAA7I,WAAA;YACA6I,OAAA,CAAA9E,SAAA;cACA8E,OAAA,CAAAzE,KAAA,CAAA9J,OAAA,CAAAqH,IAAA,CAAAkH,OAAA,CAAAN,eAAA;YACA;UACA;QACA;MACA,GAAAK,OAAA;QACAC,OAAA,CAAAzL,OAAA;MACA;IACA;IACA0G,OAAA,WAAAA,QAAA;MAAA,IAAAmF,OAAA;MACA,KAAA7L,OAAA;MACA,IAAAL,WAAA,OAAAyG,cAAA,CAAAhI,OAAA,MACA,KAAAuB,WAAA,CACA;MACA,IAAAA,WAAA,CAAAgB,YAAA;QACAhB,WAAA,CAAAgB,YAAA,GAAAhB,WAAA,CAAAgB,YAAA,CAAAoG,IAAA;MACA;MACA;MACA,KAAA+E,KAAA,gBAAA1F,cAAA,CAAAhI,OAAA,MAAAuB,WAAA;MACA,IAAA+I,uBAAA,EAAA/I,WAAA,EAAAkE,IAAA,WAAAC,QAAA;QACA+H,OAAA,CAAA5L,gBAAA,GAAA6D,QAAA,CAAAiC,IAAA;QACA8F,OAAA,CAAA5L,gBAAA,CAAA+D,OAAA,WAAAC,IAAA;UACAA,IAAA,CAAA2E,SAAA,GAAA3E,IAAA,CAAA4E,cAAA,SAAA5E,IAAA,CAAAE,aAAA;UACA,IAAAF,IAAA,CAAA2E,SAAA;YACA3E,IAAA,CAAA2E,SAAA;UACA;UACA,IAAA3E,IAAA,CAAA8H,QAAA;YACA,IAAAC,WAAA,OAAAC,gBAAA,EAAAhI,IAAA,CAAA8H,QAAA,CAAApH,KAAA;YACAV,IAAA,CAAA8H,QAAA,GAAAC,WAAA,CAAAjF,IAAA;UACA;QACA;QACA8E,OAAA,CAAA3L,KAAA,GAAA4D,QAAA,CAAA5D,KAAA;QACA2L,OAAA,CAAA7L,OAAA;MACA;IACA;IACAkM,WAAA,WAAAA,YAAAC,IAAA;MACAA,IAAA;MACA,KAAA9L,IAAA;MACA,KAAA2G,KAAA,CAAA3G,IAAA,CAAA+L,WAAA;IACA;IACAC,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,OAAA;MACA,KAAAtF,KAAA,oBAAAuF,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAC,yBAAA,EAAAH,OAAA,CAAArN,UAAA,EAAA4E,IAAA,WAAAiC,GAAA;YACAwG,OAAA,CAAAjI,QAAA,CAAAmG,OAAA;YACA8B,OAAA,CAAArN,UAAA;YACAqN,OAAA,CAAAtN,gBAAA;YACAsN,OAAA,CAAA5F,OAAA;UACA;QACA;MACA;IACA;IACAgG,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MACA,SAAAtM,IAAA,CAAAM,YAAA;QACA,KAAAN,IAAA,CAAAM,YAAA,QAAAN,IAAA,CAAAM,YAAA,CAAAoG,IAAA;MACA;MACA,KAAAC,KAAA,SAAAuF,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAG,OAAA,CAAAtM,IAAA,CAAAnB,EAAA;YACA,IAAA0N,sBAAA,EAAAD,OAAA,CAAAtM,IAAA,EAAAwD,IAAA,WAAAiC,GAAA;cACA6G,OAAA,CAAAtI,QAAA,CAAAmG,OAAA;cACAmC,OAAA,CAAAtM,IAAA;cACAsM,OAAA,CAAAvM,YAAA;cACAuM,OAAA,CAAAjG,OAAA;YACA;UACA;YACA,IAAA+F,yBAAA,EAAAE,OAAA,CAAAtM,IAAA,EAAAwD,IAAA,WAAAiC,GAAA;cACA6G,OAAA,CAAAtI,QAAA,CAAAmG,OAAA;cACAmC,OAAA,CAAAtM,IAAA;cACAsM,OAAA,CAAAvM,YAAA;cACAuM,OAAA,CAAAjG,OAAA;YACA;UACA;QACA;MACA;IACA;IACAmG,MAAA,WAAAA,OAAA;MACA,KAAAzM,YAAA;MACA,KAAA4G,KAAA,CAAA3G,IAAA,CAAA+L,WAAA;IACA;IACAzC,UAAA,WAAAA,WAAAlF,GAAA;MACA,KAAAtC,UAAA,GAAAsC,GAAA;IACA;IACAqI,WAAA,WAAAA,YAAA;MACA,KAAAzK,YAAA;MACA,KAAAoD,WAAA;IACA;IACAsH,gBAAA,WAAAA,iBAAA;MACA,KAAAzK,UAAA;MACA,KAAAE,QAAA;MACA,KAAAwK,WAAA;IACA;IACAC,SAAA,WAAAA,UAAAC,UAAA;MACA,KAAAtK,WAAA;MACA,IAAAsK,UAAA,OAAAxG,OAAA;IACA;IACAyG,wBAAA,WAAAA,yBAAA1I,GAAA;MAAA,IAAA2I,OAAA;MACA,KAAAvK,yBAAA;MACA,KAAAD,WAAA;MACA,KAAAuI,eAAA,CAAAE,MAAA,GAAA5G,GAAA;MACA,KAAAkC,SAAA;QACAyG,OAAA,CAAApG,KAAA,CAAA9J,OAAA,CAAAqH,IAAA,CAAA6I,OAAA,CAAAjC,eAAA;MACA;IACA;IACAkC,iBAAA,WAAAA,kBAAApG,MAAA;MACA,KAAAtH,WAAA,CAAA2N,SAAA,GAAArG,MAAA;MACA,KAAAxB,WAAA;IACA;IACAf,gBAAA,WAAAA,iBAAAD,GAAA;MACA,IAAAA,GAAA,IAAAtG,MAAA,CAAAmH,IAAA,CAAAb,GAAA,EAAAV,MAAA;QACA,IAAAU,GAAA,CAAA6I,SAAA,SAAAtG,KAAA,CAAAC,MAAA;UACA,KAAAD,KAAA,CAAAC,MAAA,CAAAW,mBAAA,GAAAnD,GAAA,CAAA6I,SAAA;QACA;QACA,KAAA3N,WAAA,GAAA8E,GAAA;QACA,IAAAA,GAAA,CAAA4B,SAAA,IAAA5B,GAAA,CAAA8B,OAAA;UACA,KAAAxG,SAAA,IAAA0E,GAAA,CAAA4B,SAAA,EAAA5B,GAAA,CAAA8B,OAAA;QACA;QACA,IAAA9B,GAAA,CAAA8I,MAAA;UACA,KAAA5N,WAAA,CAAAP,WAAA;QACA,WAAAqF,GAAA,CAAA8I,MAAA;UACA,KAAA5N,WAAA,CAAAP,WAAA;QACA;QACA,IAAAqF,GAAA,CAAA+I,UAAA;UACA,KAAA7N,WAAA,CAAA8N,UAAA,GAAA5D,QAAA,CAAApF,GAAA,CAAA+I,UAAA;QACA;QACA,KAAAhI,eAAA;QACA,KAAAE,YAAA;QACA,KAAAD,WAAA;MACA;IACA;IACAiI,wBAAA,WAAAA,yBAAAC,eAAA;MACA,KAAAA,eAAA,IAAAA,eAAA,CAAA5J,MAAA;QACA;MACA;MACA,IAAA6J,MAAA,GAAAD,eAAA,IAAAzJ,SAAA;MACA,IAAAyJ,eAAA,CAAA5J,MAAA;QACA6J,MAAA;MACA;MACA,OAAAA,MAAA;IACA;IAEAC,cAAA,WAAAA,eAAA;MACA,SAAAzK,iBAAA,CAAAW,MAAA,kBAAAM,QAAA,CAAAC,OAAA;MACA,KAAAtC,qBAAA;MACA,IAAA8L,GAAA,QAAA1K,iBAAA,CAAAwB,GAAA,WAAAX,IAAA;QAAA,OAAAA,IAAA,CAAAjD,KAAA;MAAA;MACA8M,GAAA,GAAAC,KAAA,CAAAC,IAAA,KAAAC,GAAA,CAAAH,GAAA;MACA,KAAAhG,IAAA,MAAArG,YAAA,cAAAqM,GAAA,CAAA/G,IAAA;IACA;IACAmH,cAAA,WAAAA,eAAA;MAAA,IAAAC,OAAA;MACA,KAAAnH,KAAA,iBAAAuF,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAA4B,wBAAA,EAAAD,OAAA,CAAA1M,YAAA,EAAAoC,IAAA,WAAAiC,GAAA;YACAqI,OAAA,CAAA9J,QAAA,CAAAmG,OAAA;UACA,GAAAgB,OAAA;YACA2C,OAAA,CAAAnM,qBAAA;YACAmM,OAAA,CAAAnH,KAAA,CAAAqH,aAAA,CAAAC,cAAA;YACAH,OAAA,CAAA/K,iBAAA;UACA;QACA;MACA;IACA;IACA8B,mBAAA,WAAAA,oBAAA;MAAA,IAAAqJ,OAAA;MACA,IAAAC,8BAAA;QAAAC,YAAA;MAAA,GAAA5K,IAAA,WAAAiC,GAAA;QACAyI,OAAA,CAAAlL,gBAAA,GAAAyC,GAAA,CAAAC,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}