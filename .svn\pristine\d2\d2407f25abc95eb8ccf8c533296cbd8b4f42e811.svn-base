package com.ruoyi.ffsafe.scantaskapi.domain;

import com.ruoyi.common.utils.sign.Base64;
import com.ruoyi.ffsafe.api.domain.FfsafeApiConfig;
import lombok.Data;
import lombok.ToString;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.entity.StringEntity;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

@Data
@ToString
@Component
public class WebScanTaskParam extends ParamBase implements RequestBase {
    private String taskName;
    private String taskUrl;
    private String userId;
    private String scanSpeed;
    private Long deviceConfigId;

    private HttpPost httpPost;


    public void parseParam(String param) throws Exception {
        if (param == null) {
            throw new Exception("参数不能为空!");
        }

        String[] fields = param.split("\\|");
        if (fields.length != 3) {
            throw new Exception("参数格式错误!");
        }

        userId = "1";
        taskName = Base64.encode(fields[0].getBytes());
        taskUrl = fields[1];
        scanSpeed = fields[2];
    }

    @Override
    public HttpRequestBase getRequestBase(Long deviceId) {
        /*if (ffurl == null) {
            if (!updateFfsafeApiConfig(deviceId)) {
                return null;
            }
        }*/
        FfsafeApiConfig ffsafeApiConfig = getFfsafeApiConfig();
        String ffurl = ffsafeApiConfig.getUrl();
        String fftoken = ffsafeApiConfig.getToken();

        List<NameValuePair> params = new ArrayList<NameValuePair>();
        HttpPost httpPost = new HttpPost(ffurl + "/v1/webscan/" + userId);
        try {
            params.add(new BasicNameValuePair("access_token", fftoken));
            params.add(new BasicNameValuePair("task_name", taskName));
            params.add(new BasicNameValuePair("task_url", taskUrl));
            params.add(new BasicNameValuePair("scan_speed", scanSpeed));
            StringEntity entity = new UrlEncodedFormEntity(params, StandardCharsets.UTF_8);
            httpPost.setEntity(entity);
            httpPost.setHeader("Content-type", "application/x-www-form-urlencoded");
        } catch (Exception e) {
            e.printStackTrace();
        }

        return httpPost;
    }
}
