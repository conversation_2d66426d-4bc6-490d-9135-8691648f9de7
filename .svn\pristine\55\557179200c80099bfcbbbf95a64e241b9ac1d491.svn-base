<template>
  <div class="custom-container">
    <div class="custom-content-container-right">
      <div class="custom-content-search-box">
        <el-form
          :model="queryParams"
          ref="queryForm"
          size="small"
          :inline="true"
          label-position="right"
          label-width="70px"
        >
          <el-row :gutter="10">
            <el-col :span="6">
              <el-form-item label="任务名称" prop="jobName">
                <el-input
                  v-model="queryParams.jobName"
                  placeholder="请输入任务名称"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="扫描目标" prop="invokeTarget">
                <el-input
                  v-model="queryParams.scanTagert"
                  placeholder="扫描目标"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
            </el-col>
<!--            <el-col :span="6">
              <el-form-item label="任务状态" prop="status">
                <el-select v-model="queryParams.status" placeholder="请选择任务状态" clearable>
                  <el-option
                    v-for="dict in dict.type.sys_job_status"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>-->
            <el-col :span="12">
              <el-form-item class="custom-search-btn">
                <el-button class="btn1" size="small" @click="handleQuery">查询</el-button>
                <el-button class="btn2" size="small" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <div class="custom-content-container">
        <div class="common-header">
          <div><span class="common-head-title">{{ listType === 4 ? '主机' : 'Web' }}漏扫记录列表</span></div>
          <div class="common-head-right">
            <el-row :gutter="10">
              <el-col :span="1.5">
                <el-button
                  type="primary"
                  size="small"
                  @click="handleReportRecord"
                >报告生成记录
                </el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                  class="btn1"
                  size="small"
                  :disabled="multiple"
                  @click="handleCreateReport"
                >批量生成报告
                </el-button>
              </el-col>
            </el-row>
          </div>
        </div>
        <el-table height="100%" v-loading="loading" :data="jobList" ref="multipleTable" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" />
          <el-table-column label="任务名称" align="left" prop="jobName"/>
          <el-table-column label="扫描目标" align="left" prop="scanTarget" width="150px" :show-overflow-tooltip="false"/>
          <el-table-column label="扫描状态" align="left" prop="taskStatus">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.taskStatus === 1">正在调度</el-tag>
              <el-tag type="primary" v-else-if="scope.row.taskStatus === 2">运行中</el-tag>
              <el-tag type="danger" v-else-if="scope.row.taskStatus === 3">任务异常</el-tag>
              <el-tag type="success" v-else-if="scope.row.taskStatus === 4">扫描完成</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="扫描进度"
            prop="finishRate"
            width="120"
            align="left"
          >
            <template slot-scope="scope">
              <el-progress :text-inside="true" :stroke-width="18" :percentage="scope.row.finishRate"></el-progress>
            </template>
          </el-table-column>
          <el-table-column label="存活主机" align="left" prop="hostNum"/>
          <el-table-column label="弱口令" align="left" prop="pwNum"/>
          <el-table-column label="可入侵漏洞" align="left" prop="pocRiskNum"/>
          <el-table-column label="高危漏洞" align="left" prop="highRiskNum"/>
          <el-table-column label="中危漏洞" align="left" prop="lowRiskNum"/>
          <el-table-column label="低危漏洞" align="left" prop="lowRiskNum"/>
          <el-table-column label="开始时间" align="left" prop="startTime"/>
          <el-table-column label="结束时间" align="left" prop="endTime"/>
          <el-table-column label="操作" width="150" fixed="right" :show-overflow-tooltip="false">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleView(scope.row)"
                v-hasPermi="['monitor:ipschedule:query']"
              >详情
              </el-button>
              <el-button
                size="mini"
                type="text"
                :disabled="scope.row.currentStatus === 1"
                @click="handleCreateReport(scope.row)"
              >生成报告
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>

    <el-dialog title="报告生成记录" :visible.sync="reportRecordDialogVisible" width="80%" append-to-body>
      <div class="custom-content-container">
        <el-table height="100%" v-loading="reportLoading" :data="reportList" ref="reportTable">
          <el-table-column label="扫描目标" align="left" prop="scanTarget" />
          <el-table-column label="创建时间" align="left" prop="createTime" />
          <el-table-column label="生成时间" align="left" prop="generateTime" />
          <el-table-column label="生成状态" align="left" prop="reportStatus">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.reportStatus === 0">生成中</el-tag>
              <el-tag type="success" v-else-if="scope.row.reportStatus === 1">已完成</el-tag>
              <el-tag type="danger" v-else-if="scope.row.reportStatus === 2">失败</el-tag>
            </template>
          </el-table-column>
<!--          <el-table-column label="操作" width="150" fixed="right">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                :disabled="scope.row.reportStatus !== 1"
                @click="downloadReport(scope.row)"
              >下载
              </el-button>
            </template>
          </el-table-column>-->
        </el-table>
        <pagination
          v-show="reportTotal>0"
          :total="reportTotal"
          :page.sync="reportQueryParams.pageNum"
          :limit.sync="reportQueryParams.pageSize"
          @pagination="getReportList"
        />
      </div>
    </el-dialog>

    <!-- 任务日志详细 -->
    <el-dialog title="任务详细" v-if="openView" :visible.sync="openView" v-dialog-drag width="1200px" append-to-body>
      <ff-job-tasks  v-if="openView" :jobId="jobId" :job-type="jobType" :job-row="editForm" />
    </el-dialog>
  </div>
</template>

<script>
import {batchGenerateReport, getListWithDetails, getReportList} from "@/api/safe/monitor";
import QuestResultDetails from '../../safe/server/questResultDetails'
import LeakScanDialog from '../../safe/server/components/LeakScanDialog'
import FfJobTasks from './ffJobTasks'
import JobLog from '../../monitor/job/log'

export default {
  name: "hostLeakyRecord",
  components: { JobLog, FfJobTasks, LeakScanDialog, QuestResultDetails },
  dicts: ['sys_job_group', 'sys_job_status'],
  props: {
    toParams: {
      type: Object,
      default: () => {}
    },
    listType: {
      type: Number,
      default: 4
    }
  },
  data() {
    return {
      jobType: undefined,
      // 是否显示Cron表达式弹出层
      openCron: false,
      // 展示最近一次运行结果
      // 遮罩层
      loading: true,
      // 任务ID
      jobId: '',
      totalScan: 0,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 定时任务表格数据
      jobList: [],

      // 是否显示弹出层
      open: false,
      // 是否显示详细弹出层
      openView: false,
      editForm: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      isDisabled: false,
      // 周期转换文字
      cronText: '',
      rows: [],
      getListInterval: null,
      // 报告生成记录相关数据
      reportRecordDialogVisible: false,
      reportLoading: false,
      reportList: [],
      reportTotal: 0,
      reportQueryParams: {
        pageNum: 1,
        pageSize: 10,
        taskType: undefined
      }
    };
  },
  watch: {
    toParams: {
      handler(newVal) {
        if(newVal && newVal.id){
          this.handleJobLog({
            jobId: newVal.id
          });
        }
      },
      immediate: true
    }
  },
  created() {
    this.getList()
    this.getListInterval = setInterval(() => {
      this.loopGetList()
    }, 5000)
  },
  destroyed() {
    if(this.getListInterval){
      clearInterval(this.getListInterval);
    }
  },
  methods: {
    /** 查询主机漏扫记录列表 */
    getList() {
      this.loading = true;
      this.queryParams.taskType = this.listType === 4 ? 2 : 1;
      getListWithDetails(this.queryParams).then(response => {
        this.jobList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    loopGetList() {
      this.queryParams.taskType = this.listType === 4 ? 2 : 1;
      getListWithDetails(this.queryParams).then(response => {
        this.jobList = response.rows;
        this.total = response.total;
      });
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.jobId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
      this.rows = selection;
    },

    /** 批量生成报告 */
    handleCreateReport(row) {
      // 单个生成报告
      if (row.id) {
        // 单个生成报告
        batchGenerateReport({
          ids: [row.id],
          taskType: this.listType === 4 ? 2 : 1
        }).then(res => {
          this.$modal.msgSuccess("报告生成任务已提交");
        }).catch(err => {
          this.$modal.msgError("报告生成失败");
        });
      } else {
        // 批量生成报告
        if (this.rows.length === 0) {
          this.$modal.msgWarning("请先选择要生成报告的记录");
          return;
        }
        const jobIds = this.rows.map(item => item.id);
        batchGenerateReport({
          ids: jobIds,
          taskType: this.listType === 4 ? 2 : 1
        }).then(res => {
          this.$modal.msgSuccess("批量报告生成任务已提交");
        }).catch(err => {
          this.$modal.msgError("批量报告生成失败");
        });
      }

    },

    /** 报告生成记录 */
    handleReportRecord() {
      this.reportRecordDialogVisible = true;
      this.reportQueryParams.taskType = this.listType === 4 ? 2 : 1;
      this.getReportList();
    },

    /** 获取报告生成记录列表 */
    getReportList() {
      this.reportLoading = true;
      getReportList(this.reportQueryParams).then(response => {
        this.reportList = response.rows;
        this.reportTotal = response.total;
        this.reportLoading = false;
      }).catch(() => {
        this.reportLoading = false;
      });
    },

    /** 任务详细信息 */
    handleView(row) {
      this.openView = true;
      this.jobType = 2;
      this.jobId = row.jobId;
      this.editForm = {...row}
    },
  }
};
</script>

<style scoped lang="scss">
@import "@/assets/styles/assetIndex.scss";
.policyCol {
  min-width: 330px;
  margin-top: 10px;
}

.policyDesc {
  display: flex;
  height: 80px;
}

.policyTxt {
  margin-left: 10px;
  line-height: 20px;
}

.policyTitle {
  height: 40px;
  line-height: 40px;
}

.oneLine {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

::v-deep .el-table {
  display: flex;
  flex-direction: column;
}

::v-deep .el-table__body-wrapper {
  overflow-y: auto;
  flex: 1;
}
</style>
