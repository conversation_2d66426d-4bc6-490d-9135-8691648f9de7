import request from '@/utils/request'

// 查询服务器列表
export function listMonitor(query) {
  return request({
    url: '/asset/monitor/list',
    method: 'get',
    params: query
  })
}

// 查询服务器详细
export function getMonitor(pid) {
  return request({
    url: '/asset/monitor/' + pid,
    method: 'get'
  })
}

// 新增服务器
export function addMonitor(data) {
  return request({
    url: '/asset/monitor',
    method: 'post',
    data: data
  })
}

// 修改服务器
export function updateMonitor(data) {
  return request({
    url: '/asset/monitor',
    method: 'put',
    data: data
  })
}

// 删除服务器
export function delMonitor(pid) {
  return request({
    url: '/asset/monitor/' + pid,
    method: 'delete'
  })
}

// 查询扫描主机结果列表
export function listExploreServer(query) {
  return request({
    url: '/asset/monitor/exploreServer/list',
    method: 'get',
    params: query
  })
}


// 查询定时任务调度列表
export function listJob(query) {
  return request({
    url: '/monitor/schedule/list',
    method: 'get',
    params: query
  })
}

export function listJobByType(query) {
  return request({
    url: '/monitor/schedule/listByType',
    method: 'get',
    params: query
  })
}

// 查询定时任务调度详细
export function getJob(jobId) {
  return request({
    url: '/monitor/schedule/' + jobId,
    method: 'get'
  })
}

// 新增定时任务调度
export function addJob(data) {
  return request({
    url: '/monitor/schedule',
    method: 'post',
    data: data
  })
}

// 修改定时任务调度
export function updateJob(data) {
  return request({
    url: '/monitor/schedule',
    method: 'put',
    data: data
  })
}

// 删除定时任务调度
export function delJob(jobId) {
  return request({
    url: '/monitor/schedule/' + jobId,
    method: 'delete'
  })
}

// 任务状态修改
export function changeJobStatus(jobId, status) {
  const data = {
    jobId,
    status
  }
  return request({
    url: '/monitor/schedule/changeStatus',
    method: 'put',
    data: data
  })
}
// 终止探活任务
export function stopExplore(jobId, status) {
  const data = {
    jobId
  }
  return request({
    url: '/monitor/schedule/stopExplore',
    method: 'put',
    data: data
  })
}


// 定时任务立即执行一次
export function runJob(jobId, jobGroup) {
  const data = {
    jobId,
    jobGroup
  }
  return request({
    url: '/monitor/schedule/run',
    method: 'put',
    data: data
  })
}

// 停止实例
export function jobStopProcess(data) {
  return request({
    url: '/monitor2/bssprocess/stop',
    method: 'post',
    data: data
  })
}

// 主机/web漏扫记录列表
export function getListWithDetails(query) {
  return request({
    url: '/hostscan/tasksummary/listWithDetails',
    method: 'get',
    params: query
  })
}

// 批量生成报告
export function batchGenerateReport(data) {
  return request({
    url: '/hostscan/tasksummary/createBatchTaskReport',
    method: 'post',
    data: data
  })
}

// 报告生成记录分页列表
export function getReportList(query) {
  return request({
    url: '/ffsafe/scanreportrecord/list',
    method: 'get',
    params: query
  })
}
