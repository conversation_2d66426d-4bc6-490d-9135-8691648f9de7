package com.ruoyi.ffsafe.scantaskapi.domain;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

/**
 * 扫描报告记录VO类
 * 包含用户要求的返回字段：扫描目标、创建时间、生成时间、生成状态等
 * 用于分页查询接口的数据返回
 *
 * <AUTHOR>
 * @date 2025-08-16
 */
@Data
public class FfsafeScanReportRecordVO extends FfsafeScanReportRecord {
    private static final long serialVersionUID = 1L;

    /** 生成状态描述 - 根据 reportStatus 转换的可读描述 */
    @Excel(name = "生成状态")
    private String generateStatusDesc;

    /** 报表类型描述 - 根据 reportType 转换的可读描述 */
    @Excel(name = "报表类型描述")
    private String reportTypeDesc;

    /** 生成入口描述 - 根据 generateSource 转换的可读描述 */
    @Excel(name = "生成入口描述")
    private String generateSourceDesc;

    /** 报表进度百分比显示 - 格式化的进度显示 */
    @Excel(name = "报表进度")
    private String reportPercentDisplay;

    /** 扫描目标数量 - 统计扫描目标的数量（分号分隔时） */
    @Excel(name = "扫描目标数量")
    private Integer targetCount;

    /** 文件大小 - 报表文件大小（如果可获取） */
    @Excel(name = "文件大小")
    private String fileSize;

    /** 耗时 - 从创建到生成完成的耗时（如果生成完成） */
    @Excel(name = "生成耗时")
    private String duration;

    /**
     * 获取生成状态描述
     * @return 生成状态的可读描述
     */
    public String getGenerateStatusDesc() {
        if (getReportStatus() == null) {
            return "未知";
        }
        switch (getReportStatus()) {
            case 0:
                return "生成中";
            case 1:
                return "生成完毕";
            case 2:
                return "下载完毕";
            default:
                return "未知状态";
        }
    }

    /**
     * 获取报表类型描述
     * @return 报表类型的可读描述
     */
    public String getReportTypeDesc() {
        if (getReportType() == null) {
            return "未知";
        }
        switch (getReportType()) {
            case 1:
                return "web漏扫报表";
            case 2:
                return "主机漏扫报表";
            default:
                return "未知类型";
        }
    }

    /**
     * 获取生成入口描述
     * @return 生成入口的可读描述
     */
    public String getGenerateSourceDesc() {
        if (getGenerateSource() == null) {
            return "未知";
        }
        switch (getGenerateSource()) {
            case 1:
                return "单条生成";
            case 2:
                return "批量生成";
            default:
                return "未知入口";
        }
    }

    /**
     * 获取报表进度显示
     * @return 格式化的进度显示
     */
    public String getReportPercentDisplay() {
        if (getReportPercent() == null) {
            return "0%";
        }
        return getReportPercent() + "%";
    }

    /**
     * 获取扫描目标数量
     * @return 扫描目标的数量
     */
    public Integer getTargetCount() {
        // 这个字段现在由SQL查询动态计算，VO类中保留方法以保持兼容性
        // 实际值由Mapper查询中的target_count字段提供
        return targetCount != null ? targetCount : 0;
    }
}
