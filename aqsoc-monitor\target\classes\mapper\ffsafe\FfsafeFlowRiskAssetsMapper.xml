<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ruoyi.ffsafe.api.mapper.FfsafeFlowRiskAssetsMapper">

    <resultMap type="FfsafeFlowRiskAssets" id="FfsafeFlowRiskAssetsResult">
        <result property="id" column="id" />
        <result property="riskAssets" column="risk_assets" />
        <result property="riskType" column="risk_type" />
        <result property="riskInfo" column="risk_info" />
        <result property="engineName" column="engine_name" />
        <result property="startTime" column="start_time" />
        <result property="updateTime" column="update_time" />
        <result property="handleState" column="handle_state" />
        <result property="handleDesc" column="handle_desc" />
        <result property="disposer" column="disposer" />
        <result property="createTime" column="create_time" />
        <result property="createBy" column="create_by" />
        <result property="updateBy" column="update_by" />
        <result property="deviceConfigId" column="device_config_id" />

    </resultMap>

    <sql id="selectFfsafeFlowRiskAssetsVo">
        select id, risk_assets, risk_type, risk_info, engine_name, start_time, update_time,
               handle_state, handle_desc, disposer, create_time, create_by, update_by, device_config_id
        from ffsafe_flow_risk_assets
    </sql>

    <select id="selectFfsafeFlowRiskAssetsList" parameterType="FfsafeFlowRiskAssets" resultMap="FfsafeFlowRiskAssetsResult">
        <include refid="selectFfsafeFlowRiskAssetsVo"/>
        <where>
            <if test="riskAssets != null and riskAssets != ''"> and risk_assets like concat('%', #{riskAssets}, '%')</if>
            <if test="riskType != null and riskType != ''"> and risk_type = #{riskType}</if>
            <if test="handleState != null"> and handle_state = #{handleState}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and update_time &gt;= #{params.beginTime}
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and update_time &lt;= #{params.endTime}
            </if>
            <if test="deviceConfigId != null">
                and device_config_id = #{deviceConfigId}
            </if>
        </where>
        order by update_time desc
    </select>

    <select id="selectFfsafeFlowRiskAssetsById" parameterType="Long" resultMap="FfsafeFlowRiskAssetsResult">
        <include refid="selectFfsafeFlowRiskAssetsVo"/>
        where id = #{id}
    </select>

    <select id="selectRiskTypeStatistics" resultType="java.util.Map">
        SELECT
            COUNT(*) as total,
            SUM(CASE WHEN risk_type = 'weak_password' THEN 1 ELSE 0 END) as weakPassword,
            SUM(CASE WHEN risk_type = 'sensitive_info' THEN 1 ELSE 0 END) as sensitiveInfo,
            SUM(CASE WHEN risk_type = 'high_risk_assets' THEN 1 ELSE 0 END) as highRiskAssets
        FROM ffsafe_flow_risk_assets
        <where>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and update_time &gt;= #{params.beginTime}
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and update_time &lt;= #{params.endTime}
            </if>
            <if test="riskAssets != null and riskAssets != ''"> and risk_assets like concat('%', #{riskAssets}, '%')</if>
            <if test="riskType != null and riskType != ''"> and risk_type = #{riskType}</if>
            <if test="handleState != null"> and handle_state = #{handleState}</if>
            <if test="deviceConfigId != null">
                and device_config_id = #{deviceConfigId}
            </if>
            <if test="deviceConfigId != null">
                and device_config_id = #{deviceConfigId}
            </if>
        </where>
    </select>

    <select id="selectByRiskAssets" parameterType="java.util.List" resultMap="FfsafeFlowRiskAssetsResult">
        <include refid="selectFfsafeFlowRiskAssetsVo"/>
        where risk_assets in
        <foreach collection="list" item="riskAsset" open="(" separator="," close=")">
            #{riskAsset}
        </foreach>
    </select>

    <insert id="insertFfsafeFlowRiskAssets" parameterType="FfsafeFlowRiskAssets" useGeneratedKeys="true" keyProperty="id">
        insert into ffsafe_flow_risk_assets
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="riskAssets != null and riskAssets != ''">risk_assets,</if>
            <if test="riskType != null and riskType != ''">risk_type,</if>
            <if test="riskInfo != null">risk_info,</if>
            <if test="engineName != null">engine_name,</if>
            <if test="startTime != null">start_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="handleState != null">handle_state,</if>
            <if test="handleDesc != null">handle_desc,</if>
            <if test="disposer != null">disposer,</if>
            <if test="createBy != null">create_by,</if>
            <if test="deviceConfigId != null">device_config_id,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="riskAssets != null and riskAssets != ''">#{riskAssets},</if>
            <if test="riskType != null and riskType != ''">#{riskType},</if>
            <if test="riskInfo != null">#{riskInfo},</if>
            <if test="engineName != null">#{engineName},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="handleState != null">#{handleState},</if>
            <if test="handleDesc != null">#{handleDesc},</if>
            <if test="disposer != null">#{disposer},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="deviceConfigId != null">#{deviceConfigId},</if>
            NOW()
        </trim>
    </insert>

    <insert id="batchInsertFfsafeFlowRiskAssets" parameterType="java.util.List">
        insert into ffsafe_flow_risk_assets(risk_assets, risk_type, risk_info, engine_name, start_time, update_time, handle_state, create_time, device_config_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.riskAssets}, #{item.riskType}, #{item.riskInfo}, #{item.engineName}, #{item.startTime}, #{item.updateTime}, #{item.handleState}, NOW(), #{item.deviceConfigId})
        </foreach>
    </insert>

    <update id="updateFfsafeFlowRiskAssets" parameterType="FfsafeFlowRiskAssets">
        update ffsafe_flow_risk_assets
        <trim prefix="SET" suffixOverrides=",">
            <if test="handleState != null">handle_state = #{handleState},</if>
            <if test="handleDesc != null">handle_desc = #{handleDesc},</if>
            <if test="disposer != null">disposer = #{disposer},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="deviceConfigId != null">device_config_id = #{deviceConfigId},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="batchUpdateFfsafeFlowRiskAssets" parameterType="java.util.List">
        <if test="list != null and list.size() > 0">
            update ffsafe_flow_risk_assets
            <trim prefix="set" suffixOverrides=",">
                <trim prefix="risk_type =case" suffix="end,">
                    <foreach collection="list" item="item">
                        when id=#{item.id} then #{item.riskType}
                    </foreach>
                </trim>
                <trim prefix="risk_info =case" suffix="end,">
                    <foreach collection="list" item="item">
                        when id=#{item.id} then #{item.riskInfo}
                    </foreach>
                </trim>
                <trim prefix="engine_name =case" suffix="end,">
                    <foreach collection="list" item="item">
                        when id=#{item.id} then #{item.engineName}
                    </foreach>
                </trim>
                <trim prefix="start_time =case" suffix="end,">
                    <foreach collection="list" item="item">
                        when id=#{item.id} then #{item.startTime}
                    </foreach>
                </trim>
                <trim prefix="update_time =case" suffix="end,">
                    <foreach collection="list" item="item">
                        when id=#{item.id} then #{item.updateTime}
                    </foreach>
                </trim>
                <trim prefix="device_config_id =case" suffix="end,">
                    <foreach collection="list" item="item">
                        when id=#{item.id} then #{item.deviceConfigId}
                    </foreach>
                </trim>
            </trim>
            where id in
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item.id}
            </foreach>
        </if>
    </update>

    <update id="batchUpdateHandleState">
        update ffsafe_flow_risk_assets
        set handle_state = #{handleState}, handle_desc = #{handleDesc}, disposer = #{disposer}
        where id in
        <foreach item="id" collection="eventIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <delete id="deleteFfsafeFlowRiskAssetsById" parameterType="Long">
        delete from ffsafe_flow_risk_assets where id = #{id}
    </delete>

    <delete id="deleteFfsafeFlowRiskAssetsByIds" parameterType="String">
        delete from ffsafe_flow_risk_assets where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectByMultipleFields" parameterType="java.util.List" resultMap="FfsafeFlowRiskAssetsResult">
        <include refid="selectFfsafeFlowRiskAssetsVo"/>
        where
        <foreach collection="list" item="item" separator=" OR " open="(" close=")">
            (risk_assets = #{item.riskAssets}
             AND risk_type = #{item.riskType}
             AND risk_info = #{item.riskInfo}
             AND engine_name = #{item.engineName}
             AND start_time = #{item.startTime})
        </foreach>
    </select>

    <select id="selectByPartialFields" parameterType="java.util.List" resultMap="FfsafeFlowRiskAssetsResult">
        <include refid="selectFfsafeFlowRiskAssetsVo"/>
        where
        <foreach collection="list" item="item" separator=" OR " open="(" close=")">
            (risk_assets = #{item.riskAssets}
             AND risk_type = #{item.riskType}
             AND engine_name = #{item.engineName}
             AND start_time = #{item.startTime})
        </foreach>
    </select>
</mapper>
