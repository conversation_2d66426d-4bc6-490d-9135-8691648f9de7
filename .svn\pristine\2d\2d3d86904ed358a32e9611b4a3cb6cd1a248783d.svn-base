package com.ruoyi.ffsafe.scantaskapi.domain;

import com.ruoyi.ffsafe.api.domain.FfsafeApiConfig;
import lombok.Data;
import lombok.ToString;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpRequestBase;
import org.springframework.stereotype.Component;

@Data
@ToString
@Component
public class WebTaskDetailParam extends ParamBase implements RequestBase{
    private int taskId;
    private int userId;
    private int needEvidence;
    private Long deviceConfigId;


    @Override
    public HttpRequestBase getRequestBase(Long deviceId) {
        /*if (ffurl == null) {
            if (!updateFfsafeApiConfig(deviceId)) {
                return null;
            }
        }*/
        FfsafeApiConfig ffsafeApiConfig = getFfsafeApiConfig();
        String ffurl = ffsafeApiConfig.getUrl();
        String fftoken = ffsafeApiConfig.getToken();

        userId = 1;
        // String fullUrl = ffurl + "/v2/task/" + taskId + "?access_token=" + fftoken + "&user_id=" + userId + "&need_evidence=" + needEvidence + "&need_cpe=" + needCpe + "&num=" + num + "&page=" + page;
        String fullUrl = ffurl + "/v1/webscan/result/" + userId +  "/" + taskId + "?access_token=" + fftoken +  "&need_evidence=" + needEvidence;   // 不进行分页处理。

        HttpGet httpGet = new HttpGet(fullUrl);
        return httpGet;
    }
}
