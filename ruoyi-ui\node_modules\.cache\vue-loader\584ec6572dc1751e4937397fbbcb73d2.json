{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\alertEvent.vue?vue&type=template&id=13dea1c2&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\alertEvent.vue", "mtime": 1755584508944}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751956544249}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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<PERSON>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"}]}