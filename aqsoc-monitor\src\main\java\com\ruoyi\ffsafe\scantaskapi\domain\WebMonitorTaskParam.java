package com.ruoyi.ffsafe.scantaskapi.domain;

import com.ruoyi.common.utils.sign.Base64;
import com.ruoyi.ffsafe.api.domain.FfsafeApiConfig;
import lombok.Data;
import lombok.ToString;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.entity.StringEntity;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

@Data
@ToString
@Component
public class WebMonitorTaskParam  extends ParamBase implements RequestBase {
    private String userId;
    private String url;
    private String systemName;
    private String domain;
    private String ip;
    private String enableAvaiable;
    private String enableDnsInject;
    private String enableModify;
    private String enableWebscan;
    private String enableSensitivePage;
    private String enableSensitiveFile;
    private String enableBlackchain;
    private Long deviceConfigId;

    // 参数格式 url systemName domain ip enableAvaiable enableDnsInject enableModify enableWebscan enableSensitivePage enableSensitiveFile enableBlackchain
    public void parseParam(String param) throws Exception {
        if (param == null) {
            throw new Exception("参数不能为空!");
        }

        String[] fields = param.split(" ");
        if (fields.length != 11) {
            throw new Exception("参数格式错误!");
        }
        userId = "1";
        url = fields[0];
        systemName = Base64.encode(fields[1].getBytes());
        domain = fields[2];
        ip = fields[3];
        enableAvaiable = fields[4];
        enableDnsInject = fields[5];
        enableModify = fields[6];
        enableWebscan = fields[7];
        enableSensitivePage = fields[8];
        enableSensitiveFile = fields[9];
        enableBlackchain = fields[10];
    }

    @Override
    public HttpRequestBase getRequestBase(Long deviceId) {
        /*if (ffurl == null) {
            if (!updateFfsafeApiConfig(deviceId)) {
                return null;
            }
        }*/
        FfsafeApiConfig ffsafeApiConfig = getFfsafeApiConfig();
        String ffurl = ffsafeApiConfig.getUrl();
        String fftoken = ffsafeApiConfig.getToken();

        List<NameValuePair> params = new ArrayList<NameValuePair>();
        HttpPost httpPost = null;

        try {
            httpPost = new HttpPost(ffurl + "/v1/web-period-task/");
            params.add(new BasicNameValuePair("access_token", fftoken));
            params.add(new BasicNameValuePair("user_id", userId));
            params.add(new BasicNameValuePair("url", url));
            params.add(new BasicNameValuePair("system_name", systemName));
            params.add(new BasicNameValuePair("domain", domain));
            params.add(new BasicNameValuePair("ip", ip));
            params.add(new BasicNameValuePair("enable_available", enableAvaiable));
            params.add(new BasicNameValuePair("enable_dns_inject", enableDnsInject));
            params.add(new BasicNameValuePair("enable_modify", enableModify));
            params.add(new BasicNameValuePair("enable_sensitive_page", enableSensitivePage));
            params.add(new BasicNameValuePair("enable_sensitive_file", enableSensitiveFile));
            params.add(new BasicNameValuePair("enable_blackchain", enableBlackchain));
            StringEntity entity = new UrlEncodedFormEntity(params, StandardCharsets.UTF_8);
            httpPost.setEntity(entity);
            httpPost.setHeader("Content-type", "application/x-www-form-urlencoded");
        } catch (Exception e) {
            e.printStackTrace();
        }

        return httpPost;
    }
}
