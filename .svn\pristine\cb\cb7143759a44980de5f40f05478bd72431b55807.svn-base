package com.ruoyi.monitor2.changting.job;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.ffsafe.api.domain.FfsafeApiConfig;
import com.ruoyi.ffsafe.api.domain.TblDeviceConfig;
import com.ruoyi.ffsafe.api.service.ITblDeviceConfigService;
import com.ruoyi.ffsafe.scantaskapi.service.ISyncFfHostEdrService;
import com.ruoyi.safe.domain.TblServer;
import com.ruoyi.safe.mapper.TblServerMapper;
import com.ruoyi.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 定时非凡EDR数据任务
 *
 * <AUTHOR>
 */
@Slf4j
@Component("ffHostEdrSyncTask")
public class FfHostEdrSyncTask {

    @Resource
    private ISyncFfHostEdrService syncFfHostEdrService;

    @Resource
    private ISysConfigService sysConfigService;

    @Resource
    private TblServerMapper tblServerMapper;

    /**
     * 非凡Edr数据
     */
    public void syncFfHostEdr() {
        if (!this.isFeiFanEnable()) {
            log.debug("----- 非凡Edr开关 为关闭状态 ----------");
            return;
        }
        // 开关打开才执行同步
        log.debug("-------- 非凡Edr数据接入开始 --------");
        try {
            // 对非凡EDR与资产ip绑定
            syncFfHostEdrService.syncFfHostEdrData();
            // 非凡EDR数据处理 查询tbl_server表 获取serial
            List<TblServer> tblServer = tblServerMapper.selectTblServerBySerial();
            if ((tblServer != null) && (tblServer.size() > 0)) {
                tblServer.forEach(e -> {
                    try {
                        syncFfHostEdrService.syncFfHostEdrDetails(e);
                    } catch (Exception e1) {
                        e1.printStackTrace();
                        log.error("host_detail_by_serial 接口调用错误: " + e1.getMessage());
                    }
                });
            }

        } catch (Exception e) {
            e.printStackTrace();
            log.error("ffHostEdrSyncTask 接口调用错误: " + e.getMessage());
        }
        log.debug("-------- 非凡Edr数据接入结束 --------");
    }

    /**
     * 是否开启非凡EDR
     *
     * @return 是否开启
     */
    private boolean isFeiFanEnable() {
        try {
            ITblDeviceConfigService deviceConfigService = SpringUtil.getBean(ITblDeviceConfigService.class);
            if (Objects.isNull(deviceConfigService)) {
                return false;
            }
            TblDeviceConfig deviceConfig = deviceConfigService.selectDeviceConfigOrDefault(null);
            if (Objects.isNull(deviceConfig)) {
                return false;
            }
            FfsafeApiConfig ffsafeApiConfig = deviceConfigService.getFfsafeApiConfig(deviceConfig);
            if (Objects.isNull(ffsafeApiConfig)) {
                return false;
            }
            return ffsafeApiConfig.isEnable();
        } catch (Exception e) {
            // 记录异常日志并提供默认返回值
            log.error("解析非凡api接口配置异常，默认返回false", e);
            return false;
        }
    }

}
