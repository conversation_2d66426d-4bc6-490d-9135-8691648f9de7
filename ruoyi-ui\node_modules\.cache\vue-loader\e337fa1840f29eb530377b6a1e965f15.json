{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\honeypotAlarmList.vue?vue&type=template&id=40160132&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\honeypotAlarmList.vue", "mtime": 1755584509047}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751956544249}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}