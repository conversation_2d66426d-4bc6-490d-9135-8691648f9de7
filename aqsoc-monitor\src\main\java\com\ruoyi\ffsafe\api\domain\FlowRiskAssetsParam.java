package com.ruoyi.ffsafe.api.domain;

import cn.hutool.extra.spring.SpringUtil;
import com.ruoyi.ffsafe.api.service.ITblDeviceConfigService;
import com.ruoyi.monitor2.changting.client.FfsafeClientService;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;

import com.ruoyi.ffsafe.scantaskapi.domain.ParamBase;
import com.ruoyi.ffsafe.scantaskapi.domain.RequestBase;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 流量风险资产API参数类
 */
@Data
@Slf4j
public class FlowRiskAssetsParam extends ParamBase implements RequestBase {

    private String accessToken;
    private String riskType;
    private String startTime;
    private String endTime;
    private int page = 1;
    private int pageSize = 30;
    private Long deviceConfigId;

    @Override
    public HttpRequestBase getRequestBase(Long deviceId) {
        TblDeviceConfig deviceConfig = FfsafeClientService.deviceConfigThreadLocal.get();
        ITblDeviceConfigService deviceConfigService = SpringUtil.getBean(ITblDeviceConfigService.class);
        FfsafeApiConfig ffsafeApiConfig = deviceConfigService.getFfsafeApiConfig(deviceConfig);
        String ffurl = ffsafeApiConfig.getUrl();
        String fftoken = ffsafeApiConfig.getToken();
        if (ffurl == null && !updateFfsafeApiConfig(deviceId)) {
            return null;
        }

        StringBuilder url = new StringBuilder(ffurl + "/v2/flow-risk-assets");
        url.append("?access_token=").append(fftoken);
        url.append("&risk_type=").append(riskType != null ? riskType : "all");
        url.append("&start_date=").append(startTime.replaceAll(" ", "%20").replaceAll(":", "%3A"));
        url.append("&end_date=").append(endTime.replaceAll(" ", "%20").replaceAll(":", "%3A"));
        url.append("&page=").append(page);
        url.append("&page_size=").append(pageSize);

        log.info("非凡API流量风险资产请求参数: {}", url.toString());

        return new HttpPost(url.toString());
    }
}
