package com.ruoyi.ffsafe.api.event;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.Threads;
import com.ruoyi.ffsafe.api.domain.*;
import com.ruoyi.ffsafe.api.service.IFfsafeInterfaceConfigService;
import com.ruoyi.ffsafe.api.service.ITblDeviceConfigService;
import com.ruoyi.monitor2.changting.client.FfsafeClientService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
@Component
public class PullDataEvent extends BaseEvent {
    private static final Long DELAY_INTERVAL = 300 * 1000L;

    private boolean bRun;
    private boolean bFirst = true;

    @Autowired
    private FfsafeClientService ffsafeClientService;
    @Autowired
    private IFfsafeInterfaceConfigService ffsafeInterfaceConfigService;
    @Resource
    private ITblDeviceConfigService deviceConfigService;



    private FfsafeInterfaceConfig getFfsafeInterface() {
        FfsafeInterfaceConfig ffsafeInterfaceConfig = new FfsafeInterfaceConfig();
        ffsafeInterfaceConfig.setInterfacePath("/v2/flow-alarm-detail");

        List<FfsafeInterfaceConfig> ffsafeInterfaceConfigList = ffsafeInterfaceConfigService.selectFfsafeInterfaceConfigList(ffsafeInterfaceConfig);
        if ((ffsafeInterfaceConfigList != null) && (ffsafeInterfaceConfigList.size() > 0)) {
            return ffsafeInterfaceConfigList.get(0);
        }

        return null;
    }

    private FlowDetailParam getConfigFlowDetailParam() {
        FfsafeInterfaceConfig ffsafeInterfaceConfig = getFfsafeInterface();
        if (ffsafeInterfaceConfig == null)
            return null;

        FlowDetailParam flowDetailParam = new FlowDetailParam();
        Date lastDataTime = ffsafeInterfaceConfig.getDataLastTime();
        if (lastDataTime == null) {
            lastDataTime = DateUtils.getNowDate();
        }
        flowDetailParam.parseStartTime(lastDataTime);

        return flowDetailParam;
    }

    private boolean checkSchedule(String startTime) {
        boolean bRet = false;

        Date startDateTime = DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, startTime);
        Date now = DateUtils.getNowDate();
        if ((now.getTime() - startDateTime.getTime()) < DELAY_INTERVAL) {
            bRet = true;
        }

        return bRet;
    }

    @PostConstruct
    public void init() {
        log.info("开始获取非凡威胁日志线程。。。");
        bRun = true;
        startEvent();
    }

    private void startEvent() {
        Thread event = new Thread(new Runnable() {
            @Override
            public void run() {
                while (bRun){
                    TblDeviceConfig queryDeviceConfig = new TblDeviceConfig();
                    queryDeviceConfig.setStatus(1);
                    List<TblDeviceConfig> list = deviceConfigService.selectTblDeviceConfigList(queryDeviceConfig);
                    AtomicBoolean error = new AtomicBoolean(false);
                    if(CollUtil.isNotEmpty(list)){
                        List<Runnable> tasks = new ArrayList<>();
                        list.forEach(deviceConfig -> {
                            tasks.add(() -> {
                                try {
                                    log.info("开始流量告警日志同步: {}",deviceConfig.getDeviceName());
                                    if(deviceConfig.getAlarmDetailLastTime() == null){
                                        deviceConfig.setAlarmDetailLastTime(DateUtil.date());
                                    }
                                    FfsafeApiConfig ffsafeApiConfig = deviceConfigService.getFfsafeApiConfig(deviceConfig);
                                    if(!ffsafeApiConfig.isEnable()){
                                        log.info("ffsafe未启用: {}",deviceConfig);
                                        throw new ServiceException("ffsafe未启用");
                                    }
                                    FlowDetailParam flowDetailParam = new FlowDetailParam();
                                    flowDetailParam.parseStartTime(deviceConfig.getAlarmDetailLastTime());
                                    FfsafeClientService.deviceConfigThreadLocal.set(deviceConfig);
                                    boolean bRet = ffsafeClientService.pullFlowDetailLog(flowDetailParam, ffsafeApiConfig);
                                    if (bRet) {
                                        flowDetailParam = flowDetailParam.getNextTimeParam();
                                        //更新最后更新日期
                                        TblDeviceConfig update = new TblDeviceConfig();
                                        update.setId(deviceConfig.getId());
                                        update.setAlarmDetailLastTime(DateUtil.parse(flowDetailParam.getStartTime()));
                                        deviceConfigService.updateLastTime(update);
                                    }
                                } catch (Exception e) {
                                    log.error("流量告警日志同步失败: {},config: {}",e.getMessage(),deviceConfig);
                                    error.set(true);
                                } finally {
                                    FfsafeClientService.deviceConfigThreadLocal.remove();
                                }
                            });
                        });
                        Threads.batchAsyncExecute(tasks);
                    }else {
                        error.set(true);
                    }
                    if(error.get()){
                        ThreadUtil.sleep(10000);
                    }else {
                        ThreadUtil.sleep(10000);
                    }
                }
            }
        });
        event.start();
    }
}
