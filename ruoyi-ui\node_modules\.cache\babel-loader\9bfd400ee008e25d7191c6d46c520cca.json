{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\honeypotAlarmList.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\honeypotAlarmList.vue", "mtime": 1755584509047}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\babel.config.js", "mtime": 1751890080904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_ruoyi", "require", "_data", "_applicationAssets", "_honeypotAlarm", "_overview", "_DynamicTag", "_interopRequireDefault", "_alarmDetail", "_importThreaten", "_threatenConfigList", "_serverAdd", "_safeAdd", "_viewStrategy", "_publishClickDialog", "_FlowBox", "_FlowTemplateSelect", "_attackStage", "_attackViewList", "_sufferViewList", "_index", "_deptSelect", "_utils", "_FlowEngine", "_user", "_attackStageText", "_deviceConfig", "name", "components", "AttackStageText", "DeptSelect", "SufferViewList", "AttackViewList", "AttackStage", "FlowTemplateSelect", "FlowBox", "PublishClickDialog", "attackDetail", "sufferDetail", "ThreatenConfigList", "ViewStrategy", "SafeAdd", "ServerAdd", "importThreaten", "AlarmDetail", "DynamicTag", "dicts", "props", "propsActiveName", "type", "String", "props<PERSON>ueryP<PERSON><PERSON>", "Object", "default", "currentBtn", "Number", "data", "validateBlockIp", "rule", "value", "callback", "Error", "pattern", "test", "deviceConfigList", "userList", "showHandleDialog", "handleForm", "id", "handleDesc", "handleState", "handleRules", "required", "message", "trigger", "showAll", "threatenDict", "queryParams", "pageNum", "pageSize", "deptOptions", "rangeTime", "loading", "threatenWarnList", "total", "title", "open<PERSON>hren<PERSON>", "form", "rules", "<PERSON><PERSON><PERSON>", "min", "max", "alarmLevel", "threatenType", "reason", "handSuggest", "logTime", "createTime", "srcIp", "srcPort", "destIp", "destPort", "mateRule", "associaDevice", "attackType", "attackStage", "attackResult", "blockingForm", "blockingRules", "block_ip", "validator", "duration_time", "remarks", "blockingIpList", "blockingDialogVisible", "editable", "assetInfoList", "openDialog", "assetData", "importDialog", "serverOpen", "assetId", "safeOpen", "threatenConfigFlag", "viewStrategy", "publishDialogVisible", "flowVisible", "flowTemplateSelectVisible", "flowStateOptions", "label", "handleStateOptions", "activeName", "syncStateOptions", "blockingDuration", "multipleSelection", "watch", "formDestIp", "oldValue", "_this", "rg", "reg", "getAssetInfoByIp", "then", "response", "length", "for<PERSON>ach", "item", "assetName", "assetTypeDesc", "deptId", "$message", "warning", "init", "handler", "val", "handlePropsQuery", "split", "map", "ip", "trim", "filter", "immediate", "created", "getDeviceConfigList", "mounted", "$route", "query", "keys", "methods", "_this2", "listDeviceConfig", "queryAllData", "res", "rows", "getThreatenDict", "handleQuery", "getDeptsData", "getUserList", "_this3", "listUser", "_this4", "$emit", "parseInt", "_objectSpread2", "startTime", "parseTime", "endTime", "Date", "setHours", "getList", "$nextTick", "JSON", "parse", "stringify", "join", "$refs", "atcAge", "initAttackStage", "_this5", "getMulTypeDict", "dictType", "_this6", "getDeptSystem", "handleChange", "reset<PERSON><PERSON>y", "flowState", "updateTime", "currentSelectedCard", "handleAdd", "$set", "handleImport", "handleExport", "download", "concat", "getTime", "handleRowClick", "row", "column", "event", "_this7", "property", "listAlarm", "_defineProperty2", "assetType", "assetClassDesc", "toString", "handleSelectionChange", "flowStateFormatter", "cellValue", "index", "match", "find", "disposer<PERSON><PERSON><PERSON><PERSON>", "e", "userId", "nick<PERSON><PERSON>", "handleStateFormatter", "handleDetail", "openDetail", "showHandle", "handleEdit", "_this8", "getAlarm", "attackNum", "handleDelete", "_this9", "ids", "$modal", "confirm", "delAlarm", "success", "catch", "addOrUpdateFlowHandle", "_this10", "formType", "opType", "status", "isWork", "workType", "eventType", "originType", "currentFlowData", "getConfigKey", "flowId", "msg", "getFlowEngineInfo", "finally", "_this11", "FlowEngineInfo", "flowTemplateJson", "error", "_this12", "deptName", "deptNameArr", "uniqueArr", "handleClose", "done", "resetFields", "submitHandleForm", "_this13", "validate", "valid", "updateAlarm", "submitForm", "_this14", "addAlarm", "cancel", "closeDialog", "closeAssetDialog", "networkOpen", "closeFlow", "isrRefresh", "flowTemplateSelectChange", "_this15", "handleAtcAgeClick", "attackSeg", "handle", "datasource", "dataSource", "handleApplicationTagShow", "applicationList", "result", "handleBlocking", "arr", "Array", "from", "Set", "blockingSubmit", "_this16", "addBlockIp", "multipleTable", "clearSelection"], "sources": ["src/views/frailty/event/component/honeypotAlarmList.vue"], "sourcesContent": ["<template>\n  <div class=\"custom-container\">\n    <div class=\"custom-content-container-right\">\n      <div class=\"custom-content-search-box\">\n        <el-form\n          :model=\"queryParams\"\n          ref=\"queryForm\"\n          size=\"small\"\n          :inline=\"true\"\n          label-position=\"right\"\n          label-width=\"70px\"\n        >\n          <el-row :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"最近告警时间\" label-width=\"98px\">\n                <el-date-picker\n                  v-model=\"rangeTime\"\n                  type=\"datetimerange\"\n                  range-separator=\"至\"\n                  start-placeholder=\"开始日期\"\n                  end-placeholder=\"结束日期\"\n                  :default-time=\"['00:00:00', '23:59:59']\">\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n<!--            <el-col :span=\"6\">\n              <el-form-item label=\"告警等级\" prop=\"alarmLevel\">\n                <el-select\n                  clearable\n                  v-model=\"queryParams.alarmLevel\"\n                  placeholder=\"请选择告警等级\"\n                >\n                  <el-option\n                    v-for=\"dict in dict.type.threaten_type\"\n                    :key=\"dict.value\"\n                    :label=\"dict.label\"\n                    :value=\"dict.value\"\n                  ></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>-->\n            <el-col :span=\"6\">\n              <el-form-item label=\"处置状态\" prop=\"\">\n                <el-select v-model=\"queryParams.handleState\" placeholder=\"请选择处置状态\" clearable>\n                  <el-option :key=\"item.value\" :label=\"item.label\" :value=\"item.value\"\n                             v-for=\"(item,index) in handleStateOptions\"/>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"告警类型\" prop=\"threatenType\">\n                <el-cascader v-model=\"queryParams.threatenType\" :options=\"threatenDict\" clearable\n                             :props=\"{ label: 'dictLabel', value: 'dictValue' }\" placeholder=\"请选择告警类型\">\n                  <template slot-scope=\"{ node, data }\">\n                    <span>{{ data.dictLabel }}</span>\n                    <span v-if=\"!node.isLeaf\"> ({{ data.children.length }}) </span>\n                  </template>\n                </el-cascader>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item class=\"custom-search-btn\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  @click=\"handleQuery\">查询\n                </el-button>\n                <el-button class=\"btn2\" size=\"small\" @click=\"resetQuery\">重置</el-button>\n                <el-button class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-down\" @click=\"showAll=true\" v-if=\"!showAll\">\n                  展开\n                </el-button>\n                <el-button class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-up\" @click=\"showAll=false\" v-else>收起\n                </el-button>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"10\" v-if=\"showAll\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"告警名称\" prop=\"threatenName\">\n                <el-input\n                  v-model=\"queryParams.threatenName\"\n                  placeholder=\"请输入告警名称\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"源IP\" prop=\"srcIp\">\n                <el-input\n                  v-model=\"queryParams.srcIp\"\n                  placeholder=\"请输入源IP\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"目标IP\" prop=\"destIp\">\n                <el-input\n                  v-model=\"queryParams.destIp\"\n                  placeholder=\"请输入目标IP\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"10\" v-if=\"showAll\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"通报状态\" prop=\"flowState\">\n                <el-select v-model=\"queryParams.flowState\" placeholder=\"请选择通报状态\" clearable>\n                  <el-option :key=\"item.value\" :label=\"item.label\" :value=\"item.value\"\n                             v-for=\"(item,index) in flowStateOptions\"/>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"处置人\">\n                <el-input\n                  v-model=\"queryParams.disposer\"\n                  placeholder=\"请输入处置人\"\n                  clearable\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"同步状态\">\n                <el-select v-model=\"queryParams.synchronizationStatus\" placeholder=\"请选择同步状态\" filterable clearable>\n                  <el-option\n                    v-for=\"dict in dict.type.synchronization_status\"\n                    :key=\"dict.value\"\n                    :label=\"dict.label\"\n                    :value=\"dict.value\"\n                  ></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"10\" v-if=\"showAll\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"所属部门\" prop=\"deptId\">\n                <dept-select v-model=\"queryParams.deptId\"/>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"6\">\n              <el-form-item label=\"所属探针\">\n                <el-select v-model=\"queryParams.deviceConfigId\" filterable clearable placeholder=\"请选择\">\n                  <el-option\n                    v-for=\"item in deviceConfigList\"\n                    :key=\"item.id\"\n                    :label=\"item.deviceName\"\n                    :value=\"item.id\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n      </div>\n<!--      <div class=\"custom-content-search-chunk\" style=\"margin-bottom: 8px\">\n        <attack-stage ref=\"atcAge\" @handleClick=\"handleAtcAgeClick\"/>\n      </div>-->\n      <div class=\"custom-content-container\"\n           :style=\"showAll ? { height: 'calc(100% - 298px)' } :{ height: 'calc(100% - 208px)' }\">\n        <div class=\"common-header\">\n          <div><span class=\"common-head-title\">蜜罐告警列表</span></div>\n          <div style=\"width: 60%; margin-left: 10%;\">\n<!--            <attack-stage-text ref=\"atcAge\" @handleClick=\"handleAtcAgeClick\" :dataSource=\"7\" />-->\n            <attack-stage-text ref=\"atcAge\" :dataSource=\"7\" />\n          </div>\n          <div class=\"common-head-right\">\n            <el-row :gutter=\"10\">\n              <el-col :span=\"1.5\">\n                <el-col :span=\"1.5\">\n                  <el-button\n                    class=\"btn1\"\n                    size=\"small\"\n                    @click=\"handleBlocking\"\n                  >批量阻断</el-button>\n                </el-col>\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  @click=\"handleExport\"\n                  v-hasPermi=\"['system:threadten:export']\"\n                >导出\n                </el-button>\n              </el-col>\n            </el-row>\n          </div>\n        </div>\n        <el-table\n          height=\"100%\"\n          v-loading=\"loading\"\n          ref=\"multipleTable\"\n          @row-click=\"handleRowClick\"\n          @selection-change=\"handleSelectionChange\"\n          :data=\"threatenWarnList\">\n          <el-table-column type=\"selection\" width=\"55\"></el-table-column>\n<!--          <el-table-column type=\"index\" width=\"100\" label=\"序号\"/>-->\n          <el-table-column label=\"最近告警时间\" width=\"200\" prop=\"updateTime\"/>\n          <el-table-column label=\"告警名称\" prop=\"threatenName\" min-width=\"260\"/>\n          <el-table-column label=\"告警类型\" prop=\"threatenType\" width=\"150\"/>\n          <el-table-column label=\"告警等级\" prop=\"alarmLevel\" width=\"150\">\n            <template slot-scope=\"scope\">\n              <dict-tag :options=\"dict.type.threaten_type\" :value=\"scope.row.alarmLevel\"/>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"源IP\" prop=\"srcIp\" width=\"180\">\n            <template slot-scope=\"scope\">\n              <div style=\"display: flex; align-items: center; justify-content: flex-start\">\n                <span>{{ scope.row.srcIp }}</span>\n                <img v-if=\"scope.row.isBlocking\" style=\"width: 24px;margin-left: 10px\" src=\"@/assets/images/block.png\" alt=\"\">\n              </div>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"目标IP/应用\" width=\"150\" prop=\"destIp\">\n          </el-table-column>\n          <el-table-column label=\"处置人\" prop=\"disposer\" width=\"150\" :formatter=\"disposerFormatter\">\n          </el-table-column>\n          <el-table-column label=\"关联业务系统\" prop=\"businessApplicationList\" width=\"200\">\n            <template slot-scope=\"scope\">\n              <el-tooltip placement=\"bottom-end\" effect=\"light\"\n                          v-if=\"scope.row.businessApplications && scope.row.businessApplications.length > 0\">\n                <div slot=\"content\">\n                  <div v-for=\"(item,tagIndex) in scope.row.businessApplications\" :key=\"item.assetId\"\n                       class=\"overflow-tag\" v-if=\"tagIndex <= 5\">\n                    <el-tag type=\"primary\"><span>{{ item.assetName }}</span></el-tag>\n                  </div>\n                  <div v-if=\"scope.row.businessApplications.length > 5\">\n                    <el-tag type=\"primary\"><span>...</span></el-tag>\n                  </div>\n                </div>\n                <el-tag type=\"primary\" class=\"asset-tag\">\n                  <span>{{ handleApplicationTagShow(scope.row.businessApplications) }}</span></el-tag>\n              </el-tooltip>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"通报状态\" prop=\"flowState\" width=\"150\" :formatter=\"flowStateFormatter\"/>\n          <el-table-column label=\"处置状态\" prop=\"handleState\" width=\"150\" :formatter=\"handleStateFormatter\"/>\n          <el-table-column label=\"所属部门\" prop=\"deptName\" width=\"150\"/>\n          <el-table-column label=\"发现次数\" prop=\"alarmNum\" width=\"150\"/>\n          <el-table-column label=\"同步状态\" prop=\"synchronizationStatus\" width=\"150\">\n            <template slot-scope=\"scope\">\n              <span v-if=\"scope.row.synchronizationStatus === '0'\">未同步</span>\n              <span v-else-if=\"scope.row.synchronizationStatus === '1'\">已同步</span>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"操作\" width=\"250\" fixed=\"right\" :show-overflow-tooltip=\"false\"\n                           class-name=\"small-padding fixed-width\">\n            <template slot-scope=\"scope\">\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleDetail(scope.row)\"\n                v-hasPermi=\"['system:threadten:query']\"\n              >详情\n              </el-button>\n              <el-button v-if=\"scope.row.workId==null && !(scope.row.handleState === '1' || scope.row.handleState === '3')\"\n                         size=\"mini\"\n                         type=\"text\"\n                         @click=\"handleEdit(scope.row)\"\n                         v-hasPermi=\"['system:threadten:edit']\"\n              >编辑\n              </el-button>\n              <el-button v-if=\"scope.row.workId==null && !(scope.row.handleState === '3')\"\n                         size=\"mini\"\n                         type=\"text\"\n                         class=\"table-delBtn\"\n                         @click=\"handleDelete(scope.row)\"\n                         v-hasPermi=\"['system:threadten:remove']\"\n              >删除\n              </el-button>\n              <el-button\n                v-if=\"scope.row.workId==null && !(scope.row.handleState === '1' || scope.row.handleState === '3')\"\n                size=\"mini\"\n                type=\"text\"\n                @click=\"showHandle(scope.row)\"\n                v-hasPermi=\"['system:threadten:edit']\"\n              >处置\n              </el-button>\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                v-if=\"!(scope.row.handleState === '1' || scope.row.handleState === '3') && (scope.row.flowState == null || scope.row.flowState === '99')\"\n                @click=\"addOrUpdateFlowHandle(null,null,scope.row)\"\n              >创建通报\n              </el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n        <pagination\n          v-show=\"total>0\"\n          :total=\"total\"\n          :page.sync=\"queryParams.pageNum\"\n          :limit.sync=\"queryParams.pageSize\"\n          @pagination=\"getList\"\n        />\n      </div>\n    </div>\n    <!-- 处置威胁情报对话框! -->\n    <el-dialog\n      title=\"快速处置\"\n      :visible.sync=\"showHandleDialog\"\n      width=\"600px\"\n      append-to-body\n    >\n      <el-form ref=\"handleStateForm\" :model=\"handleForm\" :rules=\"handleRules\" label-width=\"106px\">\n        <el-form-item label=\"处置状态\" prop=\"handleState\">\n          <el-select v-model=\"handleForm.handleState\" clearable placeholder=\"请选择处置状态\">\n            <el-option v-for=\"dict in dict.type.handle_state\"\n                       :key=\"dict.value\" :label=\"dict.label\"\n                       :value=\"parseInt(dict.value)\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"处置说明\" prop=\"handleDesc\">\n          <el-input type=\"textarea\" :rows=\"2\" v-model=\"handleForm.handleDesc\" placeholder=\"请输入处置说明\"></el-input>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitHandleForm\">确 定</el-button>\n        <el-button @click=\"showHandleDialog = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n    <!-- 添加或修改威胁情报对话框! -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"openThrenten\"\n      width=\"80%\"\n      append-to-body\n      :before-close=\"handleClose\"\n    >\n      <el-row>\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"106px\" :disabled=\"!editable\">\n          <el-col :span=\"24\" class=\"mb8\">\n            <el-divider direction=\"vertical\"></el-divider>\n            <div class=\"my-title\">基本信息</div>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"告警名称\" prop=\"threatenName\">\n              <el-input v-model=\"form.threatenName\" placeholder=\"请输入告警名称\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"告警等级\" prop=\"alarmLevel\">\n              <el-select v-model=\"form.alarmLevel\" placeholder=\"请选择告警等级\" clearable>\n                <el-option\n                  v-for=\"dict in dict.type.threaten_type\"\n                  :key=\"dict.value\"\n                  :label=\"dict.label\"\n                  :value=\"dict.value\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"告警类型\" prop=\"threatenType\">\n              <el-cascader v-model=\"form.threatenType\" :options=\"threatenDict\" clearable placeholder=\"请选择告警类型\"\n                           :props=\"{ label: 'dictLabel', value: 'dictValue' }\" style=\"width: 100%\">\n                <template slot-scope=\"{ node, data }\">\n                  <span>{{ data.dictLabel }}</span>\n                  <span v-if=\"!node.isLeaf\"> ({{ data.children.length }}) </span>\n                </template>\n              </el-cascader>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"原始报文\" prop=\"playload\">\n              <el-input v-model=\"form.playload\" :autosize=\"{minRows: 3, maxRows: 8}\" type=\"textarea\"\n                        placeholder=\"\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"处置建议\" prop=\"handSuggest\">\n              <el-input v-model=\"form.handSuggest\" :autosize=\"{minRows: 3, maxRows: 3}\" type=\"textarea\"\n                        placeholder=\"请输入告警建议\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"告警时间\" prop=\"createTime\">\n              <el-date-picker\n                v-model=\"form.createTime\"\n                type=\"date\"\n                placeholder=\"选择告警时间\"\n                format=\"yyyy 年 MM 月 dd 日\"\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"最近告警时间\" prop=\"updateTime\">\n              <el-date-picker\n                v-model=\"form.updateTime\"\n                type=\"date\"\n                placeholder=\"选择最近告警时间\"\n                format=\"yyyy 年 MM 月 dd 日\"\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"威胁标签\" prop=\"label\">\n              <DynamicTag v-model=\"form.label\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"关联设备\" prop=\"associaDevice\">\n              <el-input\n                v-model=\"form.associaDevice\"\n                placeholder=\"请输入关联设备\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\" class=\"mb8\">\n            <el-divider direction=\"vertical\"></el-divider>\n            <div class=\"my-title\">攻击关系</div>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"源IP\" prop=\"srcIp\">\n              <el-input\n                style=\"width: 50%\"\n                v-model=\"form.srcIp\"\n                placeholder=\"请输入源IP\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"源IP端口\" prop=\"srcPort\">\n              <el-input\n                style=\"width: 30%\"\n                v-model=\"form.srcPort\"\n                placeholder=\"请输入源IP端口\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"目标IP/应用\" prop=\"destIp\">\n              <el-input\n                style=\"width: 50%\"\n                v-model=\"form.destIp\"\n                placeholder=\"请输入目标IP\"\n              ></el-input>\n              <!--目标ip查询后有多个及显示资产数据，提交表单传(assetId： 资产id,deptId：部门id)-->\n              <el-select style=\"width: 50%;\" v-show=\"assetInfoList.length >= 2\" v-model=\"form.assetId\"\n                         placeholder=\"请确认疑似资产\">\n                <el-option v-for=\"item in assetInfoList\" :key=\"item.assetId\" :label=\"item.value\"\n                           :value=\"item.assetId\"></el-option>\n              </el-select>\n              <el-select style=\"width: 50%;\" v-show=\"false\" v-model=\"form.deptId\" placeholder=\"请选择资产\">\n                <el-option v-for=\"item in assetInfoList\" :label=\"item.value\" :value=\"item.deptId\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"目标IP端口\" prop=\"destPort\">\n              <el-input\n                style=\"width: 30%\"\n                v-model=\"form.destPort\"\n                placeholder=\"请输入目标IP端口\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col v-if=\"form.fileUrl!=null||editable\">\n            <el-col :span=\"24\" class=\"mb8\">\n              <el-divider direction=\"vertical\"></el-divider>\n              <div class=\"my-title\">文件上传</div>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"上传文件\" prop=\"fileUrl\">\n                <file-upload v-model=\"form.fileUrl\"\n                             :disUpload=\"!editable\"\n                             :limit=\"5\"\n                             :file-type=\"['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-col>\n        </el-form>\n      </el-row>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button v-if=\"editable\" type=\"primary\" @click=\"submitForm\"\n        >确 定\n        </el-button\n        >\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <el-dialog\n      v-if=\"openDialog\"\n      :title=\"title\"\n      :visible.sync=\"openDialog\"\n      width=\"80%\"\n      class=\"my-dialog\"\n      append-to-body\n    >\n      <el-tabs type=\"border-card\" v-model=\"activeName\">\n        <el-tab-pane label=\"事件详情\" name=\"detail\">\n          <alarm-detail\n            v-if=\"openDialog\"\n            @openDetail=\"openDetail\"\n            :data-source=\"7\"\n            :asset-data=\"assetData\"\n          />\n        </el-tab-pane>\n        <el-tab-pane v-if=\"assetData.srcIp\" label=\"攻击IP关联事件\" name=\"attack\">\n          <attack-detail :detail-type=\"'attack'\" :host-ip=\"assetData.srcIp\" :current-asset-data=\"assetData\"/>\n        </el-tab-pane>\n        <el-tab-pane v-if=\"assetData.destIp\" label=\"受害IP关联事件\" name=\"suffer\">\n          <suffer-detail :detail-type=\"'suffer'\" :host-ip=\"assetData.destIp\" :current-asset-data=\"assetData\"/>\n        </el-tab-pane>\n      </el-tabs>\n    </el-dialog>\n\n    <el-dialog title=\"导入威胁告警\" :visible.sync=\"importDialog\" width=\"800px\" append-to-body>\n      <import-threaten @closeDialog=\"closeDialog\" v-if=\"importDialog\"/>\n    </el-dialog>\n\n    <el-dialog title=\"查看服务器资产\" :visible.sync=\"serverOpen\" width=\"80%\" append-to-body>\n      <server-add :asset-id=\"assetId\" @cancel=\"closeAssetDialog()\" :editable=\"editable\" v-if=\"serverOpen\"/>\n    </el-dialog>\n\n    <el-dialog title=\"查看安全设备资产\" :visible.sync=\"safeOpen\" width=\"80%\" append-to-body>\n      <safe-add :asset-id=\"assetId\" @cancel=\"closeAssetDialog()\" :editable=\"editable\" v-if=\"safeOpen\"/>\n    </el-dialog>\n\n    <el-dialog title=\"查看告警策略\" :visible.sync=\"viewStrategy\" width=\"400\" append-to-body>\n      <view-strategy v-if=\"viewStrategy\" @close=\"viewStrategy=false\"/>\n    </el-dialog>\n\n\n    <el-dialog title=\"告警策略配置\" :visible.sync=\"threatenConfigFlag\" width=\"800\" append-to-body>\n      <threaten-config-list v-if=\"threatenConfigFlag\" @close=\"threatenConfigFlag=false\"/>\n    </el-dialog>\n\n    <el-dialog title=\"批量阻断\" :visible.sync=\"blockingDialogVisible\" width=\"400\">\n      <el-form :model=\"blockingForm\" :rules=\"blockingRules\" ref=\"blockingForm\" class=\"blocking-form\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"阻断ip\" prop=\"block_ip\">\n              <span slot=\"label\">\n                阻断ip\n                <template>\n                  <el-tooltip placement=\"top\">\n                    <div slot=\"content\">默认加载选择的事件的源IP，多个则以“;”隔开</div>\n                    <i class=\"el-icon-info\"></i>\n                  </el-tooltip>\n                </template>\n              </span>\n              <el-input v-model=\"blockingForm.block_ip\" placeholder=\"请输入ip\">\n                <el-popover\n                  slot=\"suffix\"\n                  placement=\"bottom\"\n                  width=\"100\"\n                  trigger=\"hover\"\n                >\n                  <ul>\n                    <li v-for=\"(ip, index) in blockingIpList\" :key=\"index\">{{ ip }}</li>\n                  </ul>\n                  <i slot=\"reference\" class=\"el-icon-more\"></i>\n                </el-popover>\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"阻断时长\" prop=\"duration_time\">\n              <el-select v-model=\"blockingForm.duration_time\" placeholder=\"请选择阻断时长\">\n                <el-option\n                  v-for=\"item in blockingDuration\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-form-item label=\"备注\" prop=\"remarks\">\n            <el-input v-model=\"blockingForm.remarks\" type=\"textarea\" maxlength=\"500\" show-word-limit placeholder=\"请输入阻断描述\"></el-input>\n          </el-form-item>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"blockingSubmit\">确 定</el-button>\n        <el-button @click=\"blockingDialogVisible = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <FlowBox v-if=\"flowVisible\" ref=\"FlowBox\" @close=\"closeFlow\"/>\n    <flow-template-select :show.sync=\"flowTemplateSelectVisible\" @change=\"flowTemplateSelectChange\"/>\n\n    <publish-click-dialog\n      :publish-dialog-visible=\"publishDialogVisible\"\n      @updateVisible=\"(val) => { this.publishDialogVisible = val}\"\n      title=\"发布告警事件\"\n      width=\"30%\"/>\n  </div>\n</template>\n\n<script>\nimport {parseTime} from \"@/utils/ruoyi\";\nimport {getMulTypeDict} from \"@/api/system/dict/data\";\nimport {getDeptSystem} from \"@/api/monitor2/applicationAssets\";\nimport {getAlarm, delAlarm, listAlarm, addAlarm, updateAlarm,addBlockIp} from \"@/api/threaten/honeypotAlarm\";\nimport {getAssetInfoByIp} from \"@/api/safe/overview\";\nimport DynamicTag from \"../../../../components/DynamicTag\";\nimport AlarmDetail from \"../../../basis/securityWarn/alarmDetail\";\nimport importThreaten from \"@/views/basis/securityWarn/importThreaten.vue\"\nimport ThreatenConfigList from \"@/views/basis/securityWarn/threatenConfigList.vue\"\nimport ServerAdd from \"../../../hhlCode/component/application/adds/serverAdd\";\nimport SafeAdd from \"../../../hhlCode/component/application/adds/safeAdd\";\nimport ViewStrategy from \"../../../basis/securityWarn/viewStrategy\";\nimport PublishClickDialog from \"../../../basis/securityWarn/publishClickDialog\";\nimport FlowBox from \"../../../zeroCode/workFlow/components/FlowBox\";\nimport FlowTemplateSelect from \"../../../../components/FlowTemplateSelect\";\nimport AttackStage from \"../../../threat/overview/attackStage\";\nimport AttackViewList from \"./attackViewList\";\nimport SufferViewList from \"./sufferViewList\";\nimport attackDetail from \"./detail/index.vue\";\nimport sufferDetail from \"./detail/index.vue\";\nimport DeptSelect from '@/views/components/select/deptSelect.vue'\nimport {uniqueArr} from '@/utils'\nimport {FlowEngineInfo} from \"@/api/lowCode/FlowEngine\";\nimport {listUser} from \"@/api/system/user\";\nimport AttackStageText from '@/views/threat/overview/attackStageText.vue'\nimport {listDeviceConfig} from \"@/api/ffsafe/deviceConfig\";\n\nexport default {\n  name: \"eventList\",\n  components: {\n    AttackStageText,\n    DeptSelect,\n    SufferViewList,\n    AttackViewList,\n    AttackStage,\n    FlowTemplateSelect,\n    FlowBox,\n    PublishClickDialog,\n    attackDetail,\n    sufferDetail,\n    ThreatenConfigList, ViewStrategy, SafeAdd, ServerAdd, importThreaten, AlarmDetail, DynamicTag\n  },\n  dicts: ['threaten_type', 'attack_stage', 'attack_result','handle_state', 'synchronization_status'],\n  props: {\n    propsActiveName: {\n      type: String\n    },\n    propsQueryParams: {\n      type: Object,\n      default: function () {\n        return null\n      }\n    },\n    currentBtn: {\n      type: Number\n    }\n  },\n  data() {\n    let validateBlockIp = (rule, value, callback) => {\n      if (!value) {\n        return callback(new Error('IP不能为空'));\n      }\n      // let pattern = /^((1[0-9]{2}|2[0-4][0-9]|25[0-5]|(\\d){1,2})\\.(1[0-9]{2}|2[0-4][0-9]|25[0-5]|(\\d){1,2}|0)\\.(1[0-9]{2}|2[0-4][0-9]|25[0-5]|(\\d){1,2}|0)\\.(1[0-9]{2}|2[0-4][0-9]|25[0-5]|(\\d){1,2}|0))$/;\n      let pattern = /^\\s*((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(\\s*;\\s*((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))*\\s*$/;\n      if (!pattern.test(value)) {\n        return callback(new Error('请输入正确的IP'));\n      }\n      return callback();\n    };\n    return {\n      deviceConfigList: [],\n      userList: [],\n      showHandleDialog: false,\n      handleForm: {\n        id: '',\n        handleDesc: '',\n        handleState: ''\n      },\n      handleRules: {\n        handleState: [\n          {required: true, message: '请选择处理状态', trigger: 'blur'},\n        ]\n      },\n      showAll: false,\n      threatenDict: [],\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10\n      },\n      deptOptions: [],\n      rangeTime: [],\n      loading: false,\n      threatenWarnList: [],\n      total: 0,\n      title: '',\n      openThrenten: false,\n      form: {},\n      rules: {\n        threatenName: [\n          {required: false, min: 0, max: 500, message: '告警名称不能超过500字符', trigger: 'blur'},\n          {required: true, message: '请输入告警名称', trigger: 'blur'},\n          {\n            required: true,\n            pattern: /^[^\\s]+/,\n            message: '不能以空格开头！',\n            trigger: 'blur'\n          }\n        ],\n        alarmLevel: [\n          {required: true, message: '请输入告警等级', trigger: 'blur'},\n        ],\n        threatenType: [\n          {required: true, message: '请输入告警类型', trigger: 'blur'},\n        ],\n        reason: [\n          {required: false, min: 0, max: 2000, message: '告警原因不能超过2000字符', trigger: 'blur'},\n          {required: true, message: '请输入告警原因', trigger: 'blur'},\n        ],\n        handSuggest: [\n          {required: false, min: 0, max: 2000, message: '告警建议不能超2000字符', trigger: 'blur'},\n          {required: false, message: '请输入告警建议', trigger: 'blur'},\n        ],\n        logTime: [\n          {required: true, message: '请输入日志时间', trigger: 'blur'},\n        ],\n        createTime: [\n          {required: true, message: '请输入告警时间', trigger: 'blur'},\n        ],\n        srcIp: [\n          {required: false, min: 0, max: 30, message: '源IP不能超过30字符', trigger: 'blur'},\n          {\n            required: true,\n            pattern: '^(25[0-5]|2[0-4]\\\\d|[0-1]?\\\\d?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|[0-1]?\\\\d?\\\\d)){3}$',\n            message: \"IP地址不能为空或格式不正确\",\n            trigger: \"blur\"\n          },\n        ],\n        srcPort: [\n          {required: false, min: 0, max: 11, message: '源IP端口不能超过11字符', trigger: 'blur'},\n          {required: true, pattern: '^[0-9]*[1-9][0-9]*$', message: '源IP端口不能为空或格式不正确', trigger: 'blur'},\n        ],\n        destIp: [\n          {required: false, min: 0, max: 30, message: '目标IP不能超过30字符', trigger: 'blur'},\n          {\n            required: true,\n            pattern: '^(25[0-5]|2[0-4]\\\\d|[0-1]?\\\\d?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|[0-1]?\\\\d?\\\\d)){3}$',\n            message: \"IP地址不能为空或格式不正确\",\n            trigger: \"blur\"\n          },\n        ],\n        destPort: [\n          {required: false, min: 0, max: 11, message: '目标IP端口不能超过11字符', trigger: 'blur'},\n          {required: true, pattern: '^[0-9]*[1-9][0-9]*$', message: '目标IP端口不能为空或格式不正确', trigger: 'blur'},\n        ],\n        mateRule: [\n          {required: false, min: 0, max: 200, message: '分析规则不能超过200字符', trigger: 'blur'},\n        ],\n        associaDevice: [\n          {required: false, min: 0, max: 200, message: '关联设备不能超过200字符', trigger: 'blur'},\n        ],\n        attackType: [\n          {required: false, min: 0, max: 100, message: '攻击方式不能超过100字符', trigger: 'blur'},\n          {required: true, message: '请输入攻击方式', trigger: 'blur'},\n        ],\n        attackStage: [\n          {required: false, min: 0, max: 100, message: '攻击链阶段不能超过100字符', trigger: 'blur'},\n          {required: true, message: '请输入攻击链阶段', trigger: 'blur'},\n        ],\n        attackResult: [\n          {required: false, min: 0, max: 100, message: '攻击结果不能超过100字符', trigger: 'blur'},\n          {required: true, message: '请输入攻击结果', trigger: 'blur'},\n        ],\n      },\n      blockingForm: {},\n      blockingRules: {\n        block_ip: [\n          //可同时传多个，用\";\"隔开\n          { validator: validateBlockIp, trigger: 'blur' },\n        ],\n        duration_time: [\n          {required: true, message: '请选择阻断时长', trigger: 'blur'},\n        ],\n        remarks: [\n          {required: false, min: 0, max: 500, message: '备注不能超过500字符', trigger: 'blur'},\n        ]\n      },\n      blockingIpList: [],\n      blockingDialogVisible: false, // 批量阻断弹窗\n      editable: true,\n      assetInfoList: [],\n      openDialog: false,\n      assetData: {},\n      importDialog: false,\n      serverOpen: false,\n      assetId: null,\n      safeOpen: false,\n      threatenConfigFlag: false,\n      viewStrategy: false,\n      publishDialogVisible: false,\n      flowVisible: false,\n      flowTemplateSelectVisible: false,\n      flowStateOptions: [\n        {\n          label: '待审核',\n          value: 0\n        },\n        {\n          label: '待处置',\n          value: 1\n        },\n        {\n          label: \"待反馈审核\",\n          value: 2,\n        },\n        {\n          label: '待验证',\n          value: 3\n        },\n        {\n          label: '已完成',\n          value: 4\n        },\n        {\n          label: '待提交',\n          value: -1\n        },\n        {\n          label: '未分配',\n          value: 99\n        }\n      ],\n      handleStateOptions: [\n        {\n          label: '未处置',\n          value: '0'\n        },\n        {\n          label: '已处置',\n          value: '1'\n        },\n        {\n          label: '忽略',\n          value: '2'\n        },\n        {\n          label: '处置中',\n          value: '3'\n        }\n      ],\n      activeName: 'detail',\n      syncStateOptions: [\n        {\n          label: '未同步',\n          value: 0\n        },\n        {\n          label: '已同步',\n          value: 1\n        }\n      ],\n      blockingDuration: [\n        {\n          label: '30分钟',\n          value: '30m'\n        },\n        {\n          label: '24小时',\n          value: '24h'\n        },\n        {\n          label: '48小时',\n          value: '48h'\n        },\n        {\n          label: '7天',\n          value: '168h'\n        },\n        {\n          label: '永久',\n          value: '永久'\n        }\n      ],\n      multipleSelection: []\n    }\n  },\n  watch: {\n    // 监听目标ip\n    'form.destIp'(value, oldValue) {\n      var rg = /^(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)(\\.(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)){3}$/;\n      var reg = rg.test(value);\n      if (reg) {\n        // 根据ip获取资产数据\n        getAssetInfoByIp(value).then(response => {\n          if (response.data.length) {\n            let assetData = response.data;\n            assetData.forEach(item => item.value = item.assetName + '-' + item.assetTypeDesc);\n            if (value !== oldValue && oldValue) {\n              this.form.assetId = ''\n              this.form.deptId = ''\n            }\n            // 资产数据有多条显示下拉框，只有一条不显示\n            if (assetData.length === 1) {\n              this.form.assetId = assetData[0].assetId\n              this.form.deptId = assetData[0].deptId\n            }\n            if (assetData.length > 1 && !this.form.assetId) {\n              this.form.assetId = ''\n              this.form.deptId = ''\n            }\n            this.assetInfoList = assetData;\n          } else {\n            this.assetInfoList = [];\n            return this.$message.warning('未查询到资产数据');\n          }\n        })\n      } else {\n        this.assetInfoList = [];\n        this.form.assetId = '';\n        this.form.deptId = '';\n      }\n    },\n    propsActiveName() {\n      this.init()\n    },\n    propsQueryParams: {\n      handler(val) {\n        this.handlePropsQuery(val);\n      }\n    },\n    /*rangeTime(val) {\n      console.log(val)\n    },*/\n    'blockingForm.block_ip': {\n      handler(value) {\n        if (value) {\n          this.blockingIpList = value.split(';').map(ip => ip.trim()).filter(ip => ip);\n        }\n      },\n      immediate: true\n    }\n  },\n  created() {\n    this.getDeviceConfigList();\n  },\n  mounted() {\n    if (!this.$route.query || Object.keys(this.$route.query).length < 1) {\n      this.init()\n    } else {\n      this.handlePropsQuery(this.$route.query);\n    }\n  },\n  methods: {\n    getDeviceConfigList(){\n      listDeviceConfig({queryAllData: true}).then(res => {\n        this.deviceConfigList = res.rows;\n      })\n    },\n    init() {\n      //this.resetQuery()\n      this.getThreatenDict()\n      this.handleQuery()\n      this.getDeptsData()\n      this.getUserList()\n    },\n    getUserList(){\n      listUser({pageNum:1,pageSize:1000}).then(res=>{\n        if (res.rows){\n          this.userList = res.rows\n        }\n      })\n    },\n    handleQuery() {\n      this.propsQueryParams.alarmLevel = this.queryParams.alarmLevel\n      this.$emit('update:currentBtn',this.queryParams.alarmLevel?parseInt(this.queryParams.alarmLevel) : null)\n      this.queryParams = {...this.queryParams,...this.propsQueryParams};\n      if (this.rangeTime != null) {\n        this.queryParams.startTime = parseTime(this.rangeTime[0]);\n        this.queryParams.endTime = parseTime(this.rangeTime[1]);\n      } else {\n        this.queryParams.startTime = null;\n        this.queryParams.endTime = null;\n      }\n      this.queryParams.pageNum = 1;\n      this.queryParams.pageSize = 10;\n\n      if(!this.queryParams.startTime){\n        this.queryParams.startTime = parseTime(new Date().setHours(-168, 0, 0, 0), '{y}-{m}-{d} 00:00:00'); // 一周前，时间部分为 00:00:00\n      }\n      if(!this.queryParams.endTime){\n        this.queryParams.endTime = parseTime(new Date().setHours(23, 59, 59, 999), '{y}-{m}-{d} 23:59:59'); // 当前日期，时间部分为 23:59:59\n      }\n      this.rangeTime = [this.queryParams.startTime, this.queryParams.endTime];\n      this.total = 0;\n      this.getList();\n      this.$nextTick(() => {\n        const data = JSON.parse(JSON.stringify(this.queryParams))\n        if (data.threatenType != null) {\n          data.threatenType = data.threatenType.join('/');\n        }\n        this.$refs.atcAge.initAttackStage(data)\n      })\n    },\n    // 获取告警类型多级字典数据\n    getThreatenDict() {\n      getMulTypeDict({\n        dictType: 'threaten_alarm_type'\n      }).then(res => {\n        this.threatenDict = res.data;\n      })\n    },\n    // 获取部门数据\n    getDeptsData() {\n      getDeptSystem().then(res => this.deptOptions = res.data)\n    },\n    handleChange(val) {\n      // 获取所属部门最后id\n      if (val) {\n        this.queryParams.deptId = val[val.length - 1];\n      } else {\n        this.queryParams.deptId = '';\n      }\n    },\n    resetQuery() {\n      this.queryParams = {\n        threatenName: null,\n        threatenType: null,\n        alarmLevel: null,\n        srcIp: null,\n        destIp: null,\n        handleState: null,\n        flowState: null,\n        updateTime: null,\n        pageNum: 1,\n        pageSize: 10\n      };\n      let atcAge = this.$refs.atcAge;\n      if (atcAge) {\n        atcAge.currentSelectedCard = null;\n      }\n      this.rangeTime = null;\n      this.handleQuery();\n    },\n    //新增威胁情报\n    handleAdd() {\n      this.openThrenten = true;\n      this.form = {};\n      this.editable = true;\n      this.title = \"新增威胁情报\";\n      this.$set(this.form, 'assetId', ''); // 解决el-select无法视图与数据的更新\n    },\n    // 导入功能\n    handleImport() {\n      this.importDialog = true;\n    },\n    handleExport() {\n      this.download(\n        \"/system/honeypotAlarm/export\",\n        {\n          ...this.queryParams,\n        },\n        `威胁告警_${new Date().getTime()}.xlsx`\n      );\n    },\n\n    // 获取列表数据查询\n    handleRowClick(row, column, event) {\n      // 获取告警详情单个单元格数据进行筛选\n      if (row && row.id) {\n        if (column.property) {\n          if (column.property === 'flowState') {\n            this.queryParams[column.property] = !row[column.property] ? 99 : Number(row[column.property]);\n            listAlarm({\n              [column.property]: !row[column.property] ? 99 : Number(row[column.property]),\n              pageNum: 1,\n              pageSize: 10\n            }).then(response => {\n              this.threatenWarnList = response.rows;\n              this.threatenWarnList.forEach(item => {\n                item.assetType = item.assetClassDesc + '-' + item.assetTypeDesc;\n                if (item.assetType == 'null-null') {\n                  item.assetType = null;\n                }\n              });\n              this.total = response.total;\n              this.loading = false;\n            });\n            return;\n          } else if (column.property === 'threatenType') {\n            this.queryParams[column.property] = row[column.property].split('/');\n          } else if (column.property === 'alarmLevel') {\n            this.queryParams[column.property] = row[column.property].toString();\n          } else {\n            this.queryParams[column.property] = row[column.property];\n          }\n          listAlarm({\n            [column.property]: row[column.property],\n            pageNum: 1,\n            pageSize: 10\n          }).then(response => {\n            this.threatenWarnList = response.rows;\n            this.threatenWarnList.forEach(item => {\n              item.assetType = item.assetClassDesc + '-' + item.assetTypeDesc;\n              if (item.assetType == 'null-null') {\n                item.assetType = null;\n              }\n            });\n            this.total = response.total;\n            this.loading = false;\n          });\n        }\n      }\n    },\n\n    // 多选\n    handleSelectionChange(val) {\n      this.multipleSelection = val;\n    },\n\n    flowStateFormatter(row, column, cellValue, index) {\n      let name = '未分配';\n      let match = this.flowStateOptions.find(item => item.value == cellValue);\n      if (match) {\n        name = match.label;\n      }\n      return name;\n    },\n    disposerFormatter(row, column, cellValue, index){\n      let name = '';\n      if (cellValue){\n        this.userList.forEach(e => {\n          if (e.userId == cellValue){\n            name = e.nickName\n          }\n        })\n        return name;\n      }\n      return name;\n    },\n\n\n    handleStateFormatter(row, column, cellValue, index) {\n      let name = '未处置';\n      let match = this.handleStateOptions.find(item => item.value == cellValue);\n      if (match) {\n        name = match.label;\n      }\n      return name;\n    },\n    handleDetail(row) {\n      this.assetData = {...row};\n      this.title = \"查看告警详情\";\n      this.openDetail(true);\n    },\n    showHandle(row) {\n      // 获取事件详情单个单元格数据进行筛选\n      if (row.handleState === '1' || row.handleState === '2' ) {\n        this.handleForm.handleState = parseInt(row.handleState);\n        this.handleForm.handleDesc = row.handleDesc;\n      }else {\n        this.handleForm = {\n          handleDesc: '',\n          handleState: ''\n        }\n      }\n      this.handleForm.id = row.id;\n      this.showHandleDialog = true;\n    },\n    handleEdit(row) {\n      getAlarm(row.id).then(res => {\n        this.form = {...res.data};\n        if (this.form.alarmLevel != null) {\n          this.form.alarmLevel = (this.form.alarmLevel).toString();\n        }\n        if (this.form.threatenType != null) {\n          this.form.threatenType = this.form.threatenType.split('/');\n        }\n        if (this.form.attackNum != null) {\n          this.form.attackNum = (this.form.attackNum).toString();\n        }\n        if (this.form.srcPort != null) {\n          this.form.srcPort = (this.form.srcPort).toString();\n        }\n        if (this.form.destPort != null) {\n          this.form.destPort = (this.form.destPort).toString();\n        }\n        this.title = \"修改威胁情报\";\n        this.openThrenten = true;\n      })\n    },\n    handleDelete(row) {\n      const ids = row.id;\n      const title = row.threatenName;\n      this.$modal.confirm('是否确认删除告警名称为【' + title + '】的数据项?').then(() => {\n        return delAlarm(ids);\n      }).then(() => {\n        this.$message.success(\"删除成功\");\n        this.getList();\n      }).catch(() => {\n\n      })\n    },\n    addOrUpdateFlowHandle(id, flowState, row) {\n      let data = {\n        id: id || '',\n        formType: 1,\n        opType: flowState ? 0 : '-1',\n        status: flowState,\n        row: row,\n        isWork: true\n      }\n      data.row.workType = '2';\n      data.row.eventType = 3;\n      data.originType = 'event';\n      this.currentFlowData = data;\n      this.loading = true;\n      this.getConfigKey(\"default.flowTemplateId\").then(res => {\n        let flowId = res.msg;\n        if (flowId) {\n          this.getFlowEngineInfo(flowId);\n        } else {\n          this.flowTemplateSelectVisible = true;\n        }\n      }).finally(() => {\n        this.loading = false;\n      })\n    },\n    getFlowEngineInfo(val) {\n      FlowEngineInfo(val).then(res => {\n        if (res.data && res.data.flowTemplateJson) {\n          let data = JSON.parse(res.data.flowTemplateJson);\n          if (!data[0].flowId) {\n            this.$message.error('该流程模板异常,请重新选择');\n          } else {\n            this.currentFlowData.flowId = data[0].flowId;\n            this.flowVisible = true;\n            this.$nextTick(() => {\n              this.$refs.FlowBox.init(this.currentFlowData);\n            });\n          }\n        }\n      }).finally(() => {\n        this.loading = false;\n      })\n    },\n    getList() {\n      this.loading = true;\n      let queryParams = {\n        ...this.queryParams\n      };\n      if (queryParams.threatenType != null) {\n        queryParams.threatenType = queryParams.threatenType.join('/');\n      }\n      //同步请求类型统计数据\n      this.$emit('getList',{...queryParams});\n      listAlarm(queryParams).then(response => {\n        this.threatenWarnList = response.rows;\n        this.threatenWarnList.forEach(item => {\n          item.assetType = item.assetClassDesc + '-' + item.assetTypeDesc;\n          if (item.assetType == 'null-null') {\n            item.assetType = null;\n          }\n          if (item.deptName) {\n            let deptNameArr = uniqueArr(item.deptName.split(','));\n            item.deptName = deptNameArr.join(',');\n          }\n        });\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    handleClose(done) {\n      done();\n      this.form = {};\n      this.$refs.form.resetFields();\n    },\n    submitHandleForm() {\n      this.$refs[\"handleStateForm\"].validate(valid => {\n        if (valid) {\n          updateAlarm(this.handleForm).then(res => {\n            this.$message.success(\"处置成功\");\n            this.handleForm = {};\n            this.showHandleDialog = false;\n            this.getList();\n          })\n        }\n      });\n    },\n    submitForm() {\n      if (this.form.threatenType != null) {\n        this.form.threatenType = this.form.threatenType.join('/');\n      }\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.id == null) {\n            addAlarm(this.form).then(res => {\n              this.$message.success(\"新增成功\");\n              this.form = {};\n              this.openThrenten = false;\n              this.getList();\n            })\n          } else {\n            updateAlarm(this.form).then(res => {\n              this.$message.success(\"修改成功\");\n              this.form = {};\n              this.openThrenten = false;\n              this.getList();\n            })\n          }\n        }\n      })\n    },\n    cancel() {\n      this.openThrenten = false;\n      this.$refs.form.resetFields();\n    },\n    openDetail(val) {\n      this.openDialog = val;\n    },\n    closeDialog() {\n      this.importDialog = false;\n      this.handleQuery();\n    },\n    closeAssetDialog() {\n      this.serverOpen = false;\n      this.safeOpen = false;\n      this.networkOpen = false;\n    },\n    closeFlow(isrRefresh) {\n      this.flowVisible = false\n      if (isrRefresh) this.getList();\n    },\n    flowTemplateSelectChange(val) {\n      this.flowTemplateSelectVisible = false;\n      this.flowVisible = true;\n      this.currentFlowData.flowId = val;\n      this.$nextTick(() => {\n        this.$refs.FlowBox.init(this.currentFlowData)\n      })\n    },\n    handleAtcAgeClick(atcAge) {\n      this.queryParams.attackSeg = atcAge;\n      this.handleQuery();\n    },\n    handlePropsQuery(val) {\n      if (val && Object.keys(val).length > 0) {\n        if (val.attackSeg && this.$refs.atcAge) {\n          this.$refs.atcAge.currentSelectedCard = val.attackSeg;\n        }\n        this.queryParams = val;\n        if (val.startTime && val.endTime) {\n          this.rangeTime = [val.startTime, val.endTime];\n        }\n        if (val.handle == '1') {\n          this.queryParams.handleState = '1'\n        } else if (val.handle == '0') {\n          this.queryParams.handleState = '0'\n        }\n        if (val.datasource) {\n          this.queryParams.dataSource = parseInt(val.datasource);\n        }\n        this.getThreatenDict()\n        this.getDeptsData()\n        this.handleQuery()\n      }\n    },\n    handleApplicationTagShow(applicationList) {\n      if (!applicationList || applicationList.length < 1) {\n        return '';\n      }\n      let result = applicationList[0].assetName;\n      if (applicationList.length > 1) {\n        result += '...';\n      }\n      return result;\n    },\n\n    handleBlocking() {\n      if (this.multipleSelection.length < 1) return this.$message.warning('请选择要阻断的ip');\n      this.blockingDialogVisible = true;\n      let arr = this.multipleSelection.map(item => item.srcIp);\n      arr = Array.from(new Set(arr));\n      this.$set(this.blockingForm,'block_ip',arr.join(';'));\n    },\n    blockingSubmit() {\n      this.$refs[\"blockingForm\"].validate(valid => {\n        if (valid) {\n          addBlockIp(this.blockingForm).then(res => {\n            this.$message.success('添加成功');\n          }).finally(() => {\n            this.blockingDialogVisible = false;\n            this.$refs.multipleTable.clearSelection();\n            this.multipleSelection = [];\n          })\n        }\n      })\n    },\n  }\n}\n</script>\n\n<style scoped>\n.el-divider {\n  background: #0E94EA;\n}\n\n.el-divider--vertical {\n  display: inline-block;\n  width: 5px;\n  height: 2em;\n  margin: 0 8px 0 0;\n  vertical-align: middle;\n  position: relative;\n}\n\n.my-title {\n  display: inline-block;\n  vertical-align: center;\n}\n\n\n.asset-tag {\n  margin-left: 5px;\n}\n</style>\n<style lang=\"scss\" scoped>\n.asset-tag {\n  max-width: 100%;\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  vertical-align: middle;\n}\n\n.el-tooltip__popper {\n  font-size: 12px;\n  max-width: 300px;\n}\n\n.overflow-tag:not(:first-child) {\n  margin-top: 5px;\n}\n.blocking-form {\n  ::v-deep .el-form-item__label {\n    float: none;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6lBA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,kBAAA,GAAAF,OAAA;AACA,IAAAG,cAAA,GAAAH,OAAA;AACA,IAAAI,SAAA,GAAAJ,OAAA;AACA,IAAAK,WAAA,GAAAC,sBAAA,CAAAN,OAAA;AACA,IAAAO,YAAA,GAAAD,sBAAA,CAAAN,OAAA;AACA,IAAAQ,eAAA,GAAAF,sBAAA,CAAAN,OAAA;AACA,IAAAS,mBAAA,GAAAH,sBAAA,CAAAN,OAAA;AACA,IAAAU,UAAA,GAAAJ,sBAAA,CAAAN,OAAA;AACA,IAAAW,QAAA,GAAAL,sBAAA,CAAAN,OAAA;AACA,IAAAY,aAAA,GAAAN,sBAAA,CAAAN,OAAA;AACA,IAAAa,mBAAA,GAAAP,sBAAA,CAAAN,OAAA;AACA,IAAAc,QAAA,GAAAR,sBAAA,CAAAN,OAAA;AACA,IAAAe,mBAAA,GAAAT,sBAAA,CAAAN,OAAA;AACA,IAAAgB,YAAA,GAAAV,sBAAA,CAAAN,OAAA;AACA,IAAAiB,eAAA,GAAAX,sBAAA,CAAAN,OAAA;AACA,IAAAkB,eAAA,GAAAZ,sBAAA,CAAAN,OAAA;AACA,IAAAmB,MAAA,GAAAb,sBAAA,CAAAN,OAAA;AAEA,IAAAoB,WAAA,GAAAd,sBAAA,CAAAN,OAAA;AACA,IAAAqB,MAAA,GAAArB,OAAA;AACA,IAAAsB,WAAA,GAAAtB,OAAA;AACA,IAAAuB,KAAA,GAAAvB,OAAA;AACA,IAAAwB,gBAAA,GAAAlB,sBAAA,CAAAN,OAAA;AACA,IAAAyB,aAAA,GAAAzB,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACA0B,IAAA;EACAC,UAAA;IACAC,eAAA,EAAAA,wBAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,cAAA,EAAAA,uBAAA;IACAC,cAAA,EAAAA,uBAAA;IACAC,WAAA,EAAAA,oBAAA;IACAC,kBAAA,EAAAA,2BAAA;IACAC,OAAA,EAAAA,gBAAA;IACAC,kBAAA,EAAAA,2BAAA;IACAC,YAAA,EAAAA,cAAA;IACAC,YAAA,EAAAA,cAAA;IACAC,kBAAA,EAAAA,2BAAA;IAAAC,YAAA,EAAAA,qBAAA;IAAAC,OAAA,EAAAA,gBAAA;IAAAC,SAAA,EAAAA,kBAAA;IAAAC,cAAA,EAAAA,uBAAA;IAAAC,WAAA,EAAAA,oBAAA;IAAAC,UAAA,EAAAA;EACA;EACAC,KAAA;EACAC,KAAA;IACAC,eAAA;MACAC,IAAA,EAAAC;IACA;IACAC,gBAAA;MACAF,IAAA,EAAAG,MAAA;MACAC,OAAA,WAAAA,SAAA;QACA;MACA;IACA;IACAC,UAAA;MACAL,IAAA,EAAAM;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA,IAAAC,eAAA,YAAAA,gBAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,KAAAD,KAAA;QACA,OAAAC,QAAA,KAAAC,KAAA;MACA;MACA;MACA,IAAAC,OAAA;MACA,KAAAA,OAAA,CAAAC,IAAA,CAAAJ,KAAA;QACA,OAAAC,QAAA,KAAAC,KAAA;MACA;MACA,OAAAD,QAAA;IACA;IACA;MACAI,gBAAA;MACAC,QAAA;MACAC,gBAAA;MACAC,UAAA;QACAC,EAAA;QACAC,UAAA;QACAC,WAAA;MACA;MACAC,WAAA;QACAD,WAAA,GACA;UAAAE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,OAAA;MACAC,YAAA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACAC,WAAA;MACAC,SAAA;MACAC,OAAA;MACAC,gBAAA;MACAC,KAAA;MACAC,KAAA;MACAC,YAAA;MACAC,IAAA;MACAC,KAAA;QACAC,YAAA,GACA;UAAAjB,QAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAlB,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UACAF,QAAA;UACAV,OAAA;UACAW,OAAA;UACAC,OAAA;QACA,EACA;QACAkB,UAAA,GACA;UAAApB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAmB,YAAA,GACA;UAAArB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAoB,MAAA,GACA;UAAAtB,QAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAlB,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAqB,WAAA,GACA;UAAAvB,QAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAlB,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAsB,OAAA,GACA;UAAAxB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAuB,UAAA,GACA;UAAAzB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAwB,KAAA,GACA;UAAA1B,QAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAlB,OAAA;UAAAC,OAAA;QAAA,GACA;UACAF,QAAA;UACAV,OAAA;UACAW,OAAA;UACAC,OAAA;QACA,EACA;QACAyB,OAAA,GACA;UAAA3B,QAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAlB,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAF,QAAA;UAAAV,OAAA;UAAAW,OAAA;UAAAC,OAAA;QAAA,EACA;QACA0B,MAAA,GACA;UAAA5B,QAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAlB,OAAA;UAAAC,OAAA;QAAA,GACA;UACAF,QAAA;UACAV,OAAA;UACAW,OAAA;UACAC,OAAA;QACA,EACA;QACA2B,QAAA,GACA;UAAA7B,QAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAlB,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAF,QAAA;UAAAV,OAAA;UAAAW,OAAA;UAAAC,OAAA;QAAA,EACA;QACA4B,QAAA,GACA;UAAA9B,QAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAlB,OAAA;UAAAC,OAAA;QAAA,EACA;QACA6B,aAAA,GACA;UAAA/B,QAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAlB,OAAA;UAAAC,OAAA;QAAA,EACA;QACA8B,UAAA,GACA;UAAAhC,QAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAlB,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA+B,WAAA,GACA;UAAAjC,QAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAlB,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAgC,YAAA,GACA;UAAAlC,QAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAlB,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAF,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAiC,YAAA;MACAC,aAAA;QACAC,QAAA;QACA;QACA;UAAAC,SAAA,EAAArD,eAAA;UAAAiB,OAAA;QAAA,EACA;QACAqC,aAAA,GACA;UAAAvC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAsC,OAAA,GACA;UAAAxC,QAAA;UAAAkB,GAAA;UAAAC,GAAA;UAAAlB,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAuC,cAAA;MACAC,qBAAA;MAAA;MACAC,QAAA;MACAC,aAAA;MACAC,UAAA;MACAC,SAAA;MACAC,YAAA;MACAC,UAAA;MACAC,OAAA;MACAC,QAAA;MACAC,kBAAA;MACAC,YAAA;MACAC,oBAAA;MACAC,WAAA;MACAC,yBAAA;MACAC,gBAAA,GACA;QACAC,KAAA;QACAtE,KAAA;MACA,GACA;QACAsE,KAAA;QACAtE,KAAA;MACA,GACA;QACAsE,KAAA;QACAtE,KAAA;MACA,GACA;QACAsE,KAAA;QACAtE,KAAA;MACA,GACA;QACAsE,KAAA;QACAtE,KAAA;MACA,GACA;QACAsE,KAAA;QACAtE,KAAA;MACA,GACA;QACAsE,KAAA;QACAtE,KAAA;MACA,EACA;MACAuE,kBAAA,GACA;QACAD,KAAA;QACAtE,KAAA;MACA,GACA;QACAsE,KAAA;QACAtE,KAAA;MACA,GACA;QACAsE,KAAA;QACAtE,KAAA;MACA,GACA;QACAsE,KAAA;QACAtE,KAAA;MACA,EACA;MACAwE,UAAA;MACAC,gBAAA,GACA;QACAH,KAAA;QACAtE,KAAA;MACA,GACA;QACAsE,KAAA;QACAtE,KAAA;MACA,EACA;MACA0E,gBAAA,GACA;QACAJ,KAAA;QACAtE,KAAA;MACA,GACA;QACAsE,KAAA;QACAtE,KAAA;MACA,GACA;QACAsE,KAAA;QACAtE,KAAA;MACA,GACA;QACAsE,KAAA;QACAtE,KAAA;MACA,GACA;QACAsE,KAAA;QACAtE,KAAA;MACA,EACA;MACA2E,iBAAA;IACA;EACA;EACAC,KAAA;IACA;IACA,wBAAAC,WAAA7E,KAAA,EAAA8E,QAAA;MAAA,IAAAC,KAAA;MACA,IAAAC,EAAA;MACA,IAAAC,GAAA,GAAAD,EAAA,CAAA5E,IAAA,CAAAJ,KAAA;MACA,IAAAiF,GAAA;QACA;QACA,IAAAC,0BAAA,EAAAlF,KAAA,EAAAmF,IAAA,WAAAC,QAAA;UACA,IAAAA,QAAA,CAAAvF,IAAA,CAAAwF,MAAA;YACA,IAAA1B,SAAA,GAAAyB,QAAA,CAAAvF,IAAA;YACA8D,SAAA,CAAA2B,OAAA,WAAAC,IAAA;cAAA,OAAAA,IAAA,CAAAvF,KAAA,GAAAuF,IAAA,CAAAC,SAAA,SAAAD,IAAA,CAAAE,aAAA;YAAA;YACA,IAAAzF,KAAA,KAAA8E,QAAA,IAAAA,QAAA;cACAC,KAAA,CAAAnD,IAAA,CAAAkC,OAAA;cACAiB,KAAA,CAAAnD,IAAA,CAAA8D,MAAA;YACA;YACA;YACA,IAAA/B,SAAA,CAAA0B,MAAA;cACAN,KAAA,CAAAnD,IAAA,CAAAkC,OAAA,GAAAH,SAAA,IAAAG,OAAA;cACAiB,KAAA,CAAAnD,IAAA,CAAA8D,MAAA,GAAA/B,SAAA,IAAA+B,MAAA;YACA;YACA,IAAA/B,SAAA,CAAA0B,MAAA,SAAAN,KAAA,CAAAnD,IAAA,CAAAkC,OAAA;cACAiB,KAAA,CAAAnD,IAAA,CAAAkC,OAAA;cACAiB,KAAA,CAAAnD,IAAA,CAAA8D,MAAA;YACA;YACAX,KAAA,CAAAtB,aAAA,GAAAE,SAAA;UACA;YACAoB,KAAA,CAAAtB,aAAA;YACA,OAAAsB,KAAA,CAAAY,QAAA,CAAAC,OAAA;UACA;QACA;MACA;QACA,KAAAnC,aAAA;QACA,KAAA7B,IAAA,CAAAkC,OAAA;QACA,KAAAlC,IAAA,CAAA8D,MAAA;MACA;IACA;IACArG,eAAA,WAAAA,gBAAA;MACA,KAAAwG,IAAA;IACA;IACArG,gBAAA;MACAsG,OAAA,WAAAA,QAAAC,GAAA;QACA,KAAAC,gBAAA,CAAAD,GAAA;MACA;IACA;IACA;AACA;AACA;IACA;MACAD,OAAA,WAAAA,QAAA9F,KAAA;QACA,IAAAA,KAAA;UACA,KAAAsD,cAAA,GAAAtD,KAAA,CAAAiG,KAAA,MAAAC,GAAA,WAAAC,EAAA;YAAA,OAAAA,EAAA,CAAAC,IAAA;UAAA,GAAAC,MAAA,WAAAF,EAAA;YAAA,OAAAA,EAAA;UAAA;QACA;MACA;MACAG,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,mBAAA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,UAAAC,MAAA,CAAAC,KAAA,IAAAlH,MAAA,CAAAmH,IAAA,MAAAF,MAAA,CAAAC,KAAA,EAAAtB,MAAA;MACA,KAAAQ,IAAA;IACA;MACA,KAAAG,gBAAA,MAAAU,MAAA,CAAAC,KAAA;IACA;EACA;EACAE,OAAA;IACAL,mBAAA,WAAAA,oBAAA;MAAA,IAAAM,MAAA;MACA,IAAAC,8BAAA;QAAAC,YAAA;MAAA,GAAA7B,IAAA,WAAA8B,GAAA;QACAH,MAAA,CAAAzG,gBAAA,GAAA4G,GAAA,CAAAC,IAAA;MACA;IACA;IACArB,IAAA,WAAAA,KAAA;MACA;MACA,KAAAsB,eAAA;MACA,KAAAC,WAAA;MACA,KAAAC,YAAA;MACA,KAAAC,WAAA;IACA;IACAA,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,cAAA;QAAArG,OAAA;QAAAC,QAAA;MAAA,GAAA+D,IAAA,WAAA8B,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAK,MAAA,CAAAjH,QAAA,GAAA2G,GAAA,CAAAC,IAAA;QACA;MACA;IACA;IACAE,WAAA,WAAAA,YAAA;MAAA,IAAAK,MAAA;MACA,KAAAjI,gBAAA,CAAAyC,UAAA,QAAAf,WAAA,CAAAe,UAAA;MACA,KAAAyF,KAAA,2BAAAxG,WAAA,CAAAe,UAAA,GAAA0F,QAAA,MAAAzG,WAAA,CAAAe,UAAA;MACA,KAAAf,WAAA,OAAA0G,cAAA,CAAAlI,OAAA,MAAAkI,cAAA,CAAAlI,OAAA,WAAAwB,WAAA,QAAA1B,gBAAA;MACA,SAAA8B,SAAA;QACA,KAAAJ,WAAA,CAAA2G,SAAA,OAAAC,gBAAA,OAAAxG,SAAA;QACA,KAAAJ,WAAA,CAAA6G,OAAA,OAAAD,gBAAA,OAAAxG,SAAA;MACA;QACA,KAAAJ,WAAA,CAAA2G,SAAA;QACA,KAAA3G,WAAA,CAAA6G,OAAA;MACA;MACA,KAAA7G,WAAA,CAAAC,OAAA;MACA,KAAAD,WAAA,CAAAE,QAAA;MAEA,UAAAF,WAAA,CAAA2G,SAAA;QACA,KAAA3G,WAAA,CAAA2G,SAAA,OAAAC,gBAAA,MAAAE,IAAA,GAAAC,QAAA;MACA;MACA,UAAA/G,WAAA,CAAA6G,OAAA;QACA,KAAA7G,WAAA,CAAA6G,OAAA,OAAAD,gBAAA,MAAAE,IAAA,GAAAC,QAAA;MACA;MACA,KAAA3G,SAAA,SAAAJ,WAAA,CAAA2G,SAAA,OAAA3G,WAAA,CAAA6G,OAAA;MACA,KAAAtG,KAAA;MACA,KAAAyG,OAAA;MACA,KAAAC,SAAA;QACA,IAAAtI,IAAA,GAAAuI,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAb,MAAA,CAAAvG,WAAA;QACA,IAAArB,IAAA,CAAAqC,YAAA;UACArC,IAAA,CAAAqC,YAAA,GAAArC,IAAA,CAAAqC,YAAA,CAAAqG,IAAA;QACA;QACAd,MAAA,CAAAe,KAAA,CAAAC,MAAA,CAAAC,eAAA,CAAA7I,IAAA;MACA;IACA;IACA;IACAsH,eAAA,WAAAA,gBAAA;MAAA,IAAAwB,MAAA;MACA,IAAAC,oBAAA;QACAC,QAAA;MACA,GAAA1D,IAAA,WAAA8B,GAAA;QACA0B,MAAA,CAAA1H,YAAA,GAAAgG,GAAA,CAAApH,IAAA;MACA;IACA;IACA;IACAwH,YAAA,WAAAA,aAAA;MAAA,IAAAyB,MAAA;MACA,IAAAC,gCAAA,IAAA5D,IAAA,WAAA8B,GAAA;QAAA,OAAA6B,MAAA,CAAAzH,WAAA,GAAA4F,GAAA,CAAApH,IAAA;MAAA;IACA;IACAmJ,YAAA,WAAAA,aAAAjD,GAAA;MACA;MACA,IAAAA,GAAA;QACA,KAAA7E,WAAA,CAAAwE,MAAA,GAAAK,GAAA,CAAAA,GAAA,CAAAV,MAAA;MACA;QACA,KAAAnE,WAAA,CAAAwE,MAAA;MACA;IACA;IACAuD,UAAA,WAAAA,WAAA;MACA,KAAA/H,WAAA;QACAY,YAAA;QACAI,YAAA;QACAD,UAAA;QACAM,KAAA;QACAE,MAAA;QACA9B,WAAA;QACAuI,SAAA;QACAC,UAAA;QACAhI,OAAA;QACAC,QAAA;MACA;MACA,IAAAqH,MAAA,QAAAD,KAAA,CAAAC,MAAA;MACA,IAAAA,MAAA;QACAA,MAAA,CAAAW,mBAAA;MACA;MACA,KAAA9H,SAAA;MACA,KAAA8F,WAAA;IACA;IACA;IACAiC,SAAA,WAAAA,UAAA;MACA,KAAA1H,YAAA;MACA,KAAAC,IAAA;MACA,KAAA4B,QAAA;MACA,KAAA9B,KAAA;MACA,KAAA4H,IAAA,MAAA1H,IAAA;IACA;IACA;IACA2H,YAAA,WAAAA,aAAA;MACA,KAAA3F,YAAA;IACA;IACA4F,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,CACA,oCAAA7B,cAAA,CAAAlI,OAAA,MAEA,KAAAwB,WAAA,+BAAAwI,MAAA,CAEA,IAAA1B,IAAA,GAAA2B,OAAA,YACA;IACA;IAEA;IACAC,cAAA,WAAAA,eAAAC,GAAA,EAAAC,MAAA,EAAAC,KAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAH,GAAA,IAAAA,GAAA,CAAApJ,EAAA;QACA,IAAAqJ,MAAA,CAAAG,QAAA;UACA,IAAAH,MAAA,CAAAG,QAAA;YACA,KAAA/I,WAAA,CAAA4I,MAAA,CAAAG,QAAA,KAAAJ,GAAA,CAAAC,MAAA,CAAAG,QAAA,SAAArK,MAAA,CAAAiK,GAAA,CAAAC,MAAA,CAAAG,QAAA;YACA,IAAAC,wBAAA,MAAAC,gBAAA,CAAAzK,OAAA,MAAAyK,gBAAA,CAAAzK,OAAA,MAAAyK,gBAAA,CAAAzK,OAAA,MACAoK,MAAA,CAAAG,QAAA,GAAAJ,GAAA,CAAAC,MAAA,CAAAG,QAAA,SAAArK,MAAA,CAAAiK,GAAA,CAAAC,MAAA,CAAAG,QAAA,gBACA,gBACA,GACA,EAAA9E,IAAA,WAAAC,QAAA;cACA4E,MAAA,CAAAxI,gBAAA,GAAA4D,QAAA,CAAA8B,IAAA;cACA8C,MAAA,CAAAxI,gBAAA,CAAA8D,OAAA,WAAAC,IAAA;gBACAA,IAAA,CAAA6E,SAAA,GAAA7E,IAAA,CAAA8E,cAAA,SAAA9E,IAAA,CAAAE,aAAA;gBACA,IAAAF,IAAA,CAAA6E,SAAA;kBACA7E,IAAA,CAAA6E,SAAA;gBACA;cACA;cACAJ,MAAA,CAAAvI,KAAA,GAAA2D,QAAA,CAAA3D,KAAA;cACAuI,MAAA,CAAAzI,OAAA;YACA;YACA;UACA,WAAAuI,MAAA,CAAAG,QAAA;YACA,KAAA/I,WAAA,CAAA4I,MAAA,CAAAG,QAAA,IAAAJ,GAAA,CAAAC,MAAA,CAAAG,QAAA,EAAAhE,KAAA;UACA,WAAA6D,MAAA,CAAAG,QAAA;YACA,KAAA/I,WAAA,CAAA4I,MAAA,CAAAG,QAAA,IAAAJ,GAAA,CAAAC,MAAA,CAAAG,QAAA,EAAAK,QAAA;UACA;YACA,KAAApJ,WAAA,CAAA4I,MAAA,CAAAG,QAAA,IAAAJ,GAAA,CAAAC,MAAA,CAAAG,QAAA;UACA;UACA,IAAAC,wBAAA,MAAAC,gBAAA,CAAAzK,OAAA,MAAAyK,gBAAA,CAAAzK,OAAA,MAAAyK,gBAAA,CAAAzK,OAAA,MACAoK,MAAA,CAAAG,QAAA,EAAAJ,GAAA,CAAAC,MAAA,CAAAG,QAAA,eACA,gBACA,GACA,EAAA9E,IAAA,WAAAC,QAAA;YACA4E,MAAA,CAAAxI,gBAAA,GAAA4D,QAAA,CAAA8B,IAAA;YACA8C,MAAA,CAAAxI,gBAAA,CAAA8D,OAAA,WAAAC,IAAA;cACAA,IAAA,CAAA6E,SAAA,GAAA7E,IAAA,CAAA8E,cAAA,SAAA9E,IAAA,CAAAE,aAAA;cACA,IAAAF,IAAA,CAAA6E,SAAA;gBACA7E,IAAA,CAAA6E,SAAA;cACA;YACA;YACAJ,MAAA,CAAAvI,KAAA,GAAA2D,QAAA,CAAA3D,KAAA;YACAuI,MAAA,CAAAzI,OAAA;UACA;QACA;MACA;IACA;IAEA;IACAgJ,qBAAA,WAAAA,sBAAAxE,GAAA;MACA,KAAApB,iBAAA,GAAAoB,GAAA;IACA;IAEAyE,kBAAA,WAAAA,mBAAAX,GAAA,EAAAC,MAAA,EAAAW,SAAA,EAAAC,KAAA;MACA,IAAA1M,IAAA;MACA,IAAA2M,KAAA,QAAAtG,gBAAA,CAAAuG,IAAA,WAAArF,IAAA;QAAA,OAAAA,IAAA,CAAAvF,KAAA,IAAAyK,SAAA;MAAA;MACA,IAAAE,KAAA;QACA3M,IAAA,GAAA2M,KAAA,CAAArG,KAAA;MACA;MACA,OAAAtG,IAAA;IACA;IACA6M,iBAAA,WAAAA,kBAAAhB,GAAA,EAAAC,MAAA,EAAAW,SAAA,EAAAC,KAAA;MACA,IAAA1M,IAAA;MACA,IAAAyM,SAAA;QACA,KAAAnK,QAAA,CAAAgF,OAAA,WAAAwF,CAAA;UACA,IAAAA,CAAA,CAAAC,MAAA,IAAAN,SAAA;YACAzM,IAAA,GAAA8M,CAAA,CAAAE,QAAA;UACA;QACA;QACA,OAAAhN,IAAA;MACA;MACA,OAAAA,IAAA;IACA;IAGAiN,oBAAA,WAAAA,qBAAApB,GAAA,EAAAC,MAAA,EAAAW,SAAA,EAAAC,KAAA;MACA,IAAA1M,IAAA;MACA,IAAA2M,KAAA,QAAApG,kBAAA,CAAAqG,IAAA,WAAArF,IAAA;QAAA,OAAAA,IAAA,CAAAvF,KAAA,IAAAyK,SAAA;MAAA;MACA,IAAAE,KAAA;QACA3M,IAAA,GAAA2M,KAAA,CAAArG,KAAA;MACA;MACA,OAAAtG,IAAA;IACA;IACAkN,YAAA,WAAAA,aAAArB,GAAA;MACA,KAAAlG,SAAA,OAAAiE,cAAA,CAAAlI,OAAA,MAAAmK,GAAA;MACA,KAAAnI,KAAA;MACA,KAAAyJ,UAAA;IACA;IACAC,UAAA,WAAAA,WAAAvB,GAAA;MACA;MACA,IAAAA,GAAA,CAAAlJ,WAAA,YAAAkJ,GAAA,CAAAlJ,WAAA;QACA,KAAAH,UAAA,CAAAG,WAAA,GAAAgH,QAAA,CAAAkC,GAAA,CAAAlJ,WAAA;QACA,KAAAH,UAAA,CAAAE,UAAA,GAAAmJ,GAAA,CAAAnJ,UAAA;MACA;QACA,KAAAF,UAAA;UACAE,UAAA;UACAC,WAAA;QACA;MACA;MACA,KAAAH,UAAA,CAAAC,EAAA,GAAAoJ,GAAA,CAAApJ,EAAA;MACA,KAAAF,gBAAA;IACA;IACA8K,UAAA,WAAAA,WAAAxB,GAAA;MAAA,IAAAyB,MAAA;MACA,IAAAC,uBAAA,EAAA1B,GAAA,CAAApJ,EAAA,EAAA0E,IAAA,WAAA8B,GAAA;QACAqE,MAAA,CAAA1J,IAAA,OAAAgG,cAAA,CAAAlI,OAAA,MAAAuH,GAAA,CAAApH,IAAA;QACA,IAAAyL,MAAA,CAAA1J,IAAA,CAAAK,UAAA;UACAqJ,MAAA,CAAA1J,IAAA,CAAAK,UAAA,GAAAqJ,MAAA,CAAA1J,IAAA,CAAAK,UAAA,CAAAqI,QAAA;QACA;QACA,IAAAgB,MAAA,CAAA1J,IAAA,CAAAM,YAAA;UACAoJ,MAAA,CAAA1J,IAAA,CAAAM,YAAA,GAAAoJ,MAAA,CAAA1J,IAAA,CAAAM,YAAA,CAAA+D,KAAA;QACA;QACA,IAAAqF,MAAA,CAAA1J,IAAA,CAAA4J,SAAA;UACAF,MAAA,CAAA1J,IAAA,CAAA4J,SAAA,GAAAF,MAAA,CAAA1J,IAAA,CAAA4J,SAAA,CAAAlB,QAAA;QACA;QACA,IAAAgB,MAAA,CAAA1J,IAAA,CAAAY,OAAA;UACA8I,MAAA,CAAA1J,IAAA,CAAAY,OAAA,GAAA8I,MAAA,CAAA1J,IAAA,CAAAY,OAAA,CAAA8H,QAAA;QACA;QACA,IAAAgB,MAAA,CAAA1J,IAAA,CAAAc,QAAA;UACA4I,MAAA,CAAA1J,IAAA,CAAAc,QAAA,GAAA4I,MAAA,CAAA1J,IAAA,CAAAc,QAAA,CAAA4H,QAAA;QACA;QACAgB,MAAA,CAAA5J,KAAA;QACA4J,MAAA,CAAA3J,YAAA;MACA;IACA;IACA8J,YAAA,WAAAA,aAAA5B,GAAA;MAAA,IAAA6B,MAAA;MACA,IAAAC,GAAA,GAAA9B,GAAA,CAAApJ,EAAA;MACA,IAAAiB,KAAA,GAAAmI,GAAA,CAAA/H,YAAA;MACA,KAAA8J,MAAA,CAAAC,OAAA,kBAAAnK,KAAA,aAAAyD,IAAA;QACA,WAAA2G,uBAAA,EAAAH,GAAA;MACA,GAAAxG,IAAA;QACAuG,MAAA,CAAA/F,QAAA,CAAAoG,OAAA;QACAL,MAAA,CAAAxD,OAAA;MACA,GAAA8D,KAAA,cAEA;IACA;IACAC,qBAAA,WAAAA,sBAAAxL,EAAA,EAAAyI,SAAA,EAAAW,GAAA;MAAA,IAAAqC,OAAA;MACA,IAAArM,IAAA;QACAY,EAAA,EAAAA,EAAA;QACA0L,QAAA;QACAC,MAAA,EAAAlD,SAAA;QACAmD,MAAA,EAAAnD,SAAA;QACAW,GAAA,EAAAA,GAAA;QACAyC,MAAA;MACA;MACAzM,IAAA,CAAAgK,GAAA,CAAA0C,QAAA;MACA1M,IAAA,CAAAgK,GAAA,CAAA2C,SAAA;MACA3M,IAAA,CAAA4M,UAAA;MACA,KAAAC,eAAA,GAAA7M,IAAA;MACA,KAAA0B,OAAA;MACA,KAAAoL,YAAA,2BAAAxH,IAAA,WAAA8B,GAAA;QACA,IAAA2F,MAAA,GAAA3F,GAAA,CAAA4F,GAAA;QACA,IAAAD,MAAA;UACAV,OAAA,CAAAY,iBAAA,CAAAF,MAAA;QACA;UACAV,OAAA,CAAA9H,yBAAA;QACA;MACA,GAAA2I,OAAA;QACAb,OAAA,CAAA3K,OAAA;MACA;IACA;IACAuL,iBAAA,WAAAA,kBAAA/G,GAAA;MAAA,IAAAiH,OAAA;MACA,IAAAC,0BAAA,EAAAlH,GAAA,EAAAZ,IAAA,WAAA8B,GAAA;QACA,IAAAA,GAAA,CAAApH,IAAA,IAAAoH,GAAA,CAAApH,IAAA,CAAAqN,gBAAA;UACA,IAAArN,IAAA,GAAAuI,IAAA,CAAAC,KAAA,CAAApB,GAAA,CAAApH,IAAA,CAAAqN,gBAAA;UACA,KAAArN,IAAA,IAAA+M,MAAA;YACAI,OAAA,CAAArH,QAAA,CAAAwH,KAAA;UACA;YACAH,OAAA,CAAAN,eAAA,CAAAE,MAAA,GAAA/M,IAAA,IAAA+M,MAAA;YACAI,OAAA,CAAA7I,WAAA;YACA6I,OAAA,CAAA7E,SAAA;cACA6E,OAAA,CAAAxE,KAAA,CAAAhK,OAAA,CAAAqH,IAAA,CAAAmH,OAAA,CAAAN,eAAA;YACA;UACA;QACA;MACA,GAAAK,OAAA;QACAC,OAAA,CAAAzL,OAAA;MACA;IACA;IACA2G,OAAA,WAAAA,QAAA;MAAA,IAAAkF,OAAA;MACA,KAAA7L,OAAA;MACA,IAAAL,WAAA,OAAA0G,cAAA,CAAAlI,OAAA,MACA,KAAAwB,WAAA,CACA;MACA,IAAAA,WAAA,CAAAgB,YAAA;QACAhB,WAAA,CAAAgB,YAAA,GAAAhB,WAAA,CAAAgB,YAAA,CAAAqG,IAAA;MACA;MACA;MACA,KAAAb,KAAA,gBAAAE,cAAA,CAAAlI,OAAA,MAAAwB,WAAA;MACA,IAAAgJ,wBAAA,EAAAhJ,WAAA,EAAAiE,IAAA,WAAAC,QAAA;QACAgI,OAAA,CAAA5L,gBAAA,GAAA4D,QAAA,CAAA8B,IAAA;QACAkG,OAAA,CAAA5L,gBAAA,CAAA8D,OAAA,WAAAC,IAAA;UACAA,IAAA,CAAA6E,SAAA,GAAA7E,IAAA,CAAA8E,cAAA,SAAA9E,IAAA,CAAAE,aAAA;UACA,IAAAF,IAAA,CAAA6E,SAAA;YACA7E,IAAA,CAAA6E,SAAA;UACA;UACA,IAAA7E,IAAA,CAAA8H,QAAA;YACA,IAAAC,WAAA,OAAAC,gBAAA,EAAAhI,IAAA,CAAA8H,QAAA,CAAApH,KAAA;YACAV,IAAA,CAAA8H,QAAA,GAAAC,WAAA,CAAA/E,IAAA;UACA;QACA;QACA6E,OAAA,CAAA3L,KAAA,GAAA2D,QAAA,CAAA3D,KAAA;QACA2L,OAAA,CAAA7L,OAAA;MACA;IACA;IACAiM,WAAA,WAAAA,YAAAC,IAAA;MACAA,IAAA;MACA,KAAA7L,IAAA;MACA,KAAA4G,KAAA,CAAA5G,IAAA,CAAA8L,WAAA;IACA;IACAC,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,OAAA;MACA,KAAApF,KAAA,oBAAAqF,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAC,0BAAA,EAAAH,OAAA,CAAApN,UAAA,EAAA2E,IAAA,WAAA8B,GAAA;YACA2G,OAAA,CAAAjI,QAAA,CAAAoG,OAAA;YACA6B,OAAA,CAAApN,UAAA;YACAoN,OAAA,CAAArN,gBAAA;YACAqN,OAAA,CAAA1F,OAAA;UACA;QACA;MACA;IACA;IACA8F,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MACA,SAAArM,IAAA,CAAAM,YAAA;QACA,KAAAN,IAAA,CAAAM,YAAA,QAAAN,IAAA,CAAAM,YAAA,CAAAqG,IAAA;MACA;MACA,KAAAC,KAAA,SAAAqF,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAG,OAAA,CAAArM,IAAA,CAAAnB,EAAA;YACA,IAAAyN,uBAAA,EAAAD,OAAA,CAAArM,IAAA,EAAAuD,IAAA,WAAA8B,GAAA;cACAgH,OAAA,CAAAtI,QAAA,CAAAoG,OAAA;cACAkC,OAAA,CAAArM,IAAA;cACAqM,OAAA,CAAAtM,YAAA;cACAsM,OAAA,CAAA/F,OAAA;YACA;UACA;YACA,IAAA6F,0BAAA,EAAAE,OAAA,CAAArM,IAAA,EAAAuD,IAAA,WAAA8B,GAAA;cACAgH,OAAA,CAAAtI,QAAA,CAAAoG,OAAA;cACAkC,OAAA,CAAArM,IAAA;cACAqM,OAAA,CAAAtM,YAAA;cACAsM,OAAA,CAAA/F,OAAA;YACA;UACA;QACA;MACA;IACA;IACAiG,MAAA,WAAAA,OAAA;MACA,KAAAxM,YAAA;MACA,KAAA6G,KAAA,CAAA5G,IAAA,CAAA8L,WAAA;IACA;IACAvC,UAAA,WAAAA,WAAApF,GAAA;MACA,KAAArC,UAAA,GAAAqC,GAAA;IACA;IACAqI,WAAA,WAAAA,YAAA;MACA,KAAAxK,YAAA;MACA,KAAAwD,WAAA;IACA;IACAiH,gBAAA,WAAAA,iBAAA;MACA,KAAAxK,UAAA;MACA,KAAAE,QAAA;MACA,KAAAuK,WAAA;IACA;IACAC,SAAA,WAAAA,UAAAC,UAAA;MACA,KAAArK,WAAA;MACA,IAAAqK,UAAA,OAAAtG,OAAA;IACA;IACAuG,wBAAA,WAAAA,yBAAA1I,GAAA;MAAA,IAAA2I,OAAA;MACA,KAAAtK,yBAAA;MACA,KAAAD,WAAA;MACA,KAAAuI,eAAA,CAAAE,MAAA,GAAA7G,GAAA;MACA,KAAAoC,SAAA;QACAuG,OAAA,CAAAlG,KAAA,CAAAhK,OAAA,CAAAqH,IAAA,CAAA6I,OAAA,CAAAhC,eAAA;MACA;IACA;IACAiC,iBAAA,WAAAA,kBAAAlG,MAAA;MACA,KAAAvH,WAAA,CAAA0N,SAAA,GAAAnG,MAAA;MACA,KAAArB,WAAA;IACA;IACApB,gBAAA,WAAAA,iBAAAD,GAAA;MACA,IAAAA,GAAA,IAAAtG,MAAA,CAAAmH,IAAA,CAAAb,GAAA,EAAAV,MAAA;QACA,IAAAU,GAAA,CAAA6I,SAAA,SAAApG,KAAA,CAAAC,MAAA;UACA,KAAAD,KAAA,CAAAC,MAAA,CAAAW,mBAAA,GAAArD,GAAA,CAAA6I,SAAA;QACA;QACA,KAAA1N,WAAA,GAAA6E,GAAA;QACA,IAAAA,GAAA,CAAA8B,SAAA,IAAA9B,GAAA,CAAAgC,OAAA;UACA,KAAAzG,SAAA,IAAAyE,GAAA,CAAA8B,SAAA,EAAA9B,GAAA,CAAAgC,OAAA;QACA;QACA,IAAAhC,GAAA,CAAA8I,MAAA;UACA,KAAA3N,WAAA,CAAAP,WAAA;QACA,WAAAoF,GAAA,CAAA8I,MAAA;UACA,KAAA3N,WAAA,CAAAP,WAAA;QACA;QACA,IAAAoF,GAAA,CAAA+I,UAAA;UACA,KAAA5N,WAAA,CAAA6N,UAAA,GAAApH,QAAA,CAAA5B,GAAA,CAAA+I,UAAA;QACA;QACA,KAAA3H,eAAA;QACA,KAAAE,YAAA;QACA,KAAAD,WAAA;MACA;IACA;IACA4H,wBAAA,WAAAA,yBAAAC,eAAA;MACA,KAAAA,eAAA,IAAAA,eAAA,CAAA5J,MAAA;QACA;MACA;MACA,IAAA6J,MAAA,GAAAD,eAAA,IAAAzJ,SAAA;MACA,IAAAyJ,eAAA,CAAA5J,MAAA;QACA6J,MAAA;MACA;MACA,OAAAA,MAAA;IACA;IAEAC,cAAA,WAAAA,eAAA;MACA,SAAAxK,iBAAA,CAAAU,MAAA,kBAAAM,QAAA,CAAAC,OAAA;MACA,KAAArC,qBAAA;MACA,IAAA6L,GAAA,QAAAzK,iBAAA,CAAAuB,GAAA,WAAAX,IAAA;QAAA,OAAAA,IAAA,CAAAhD,KAAA;MAAA;MACA6M,GAAA,GAAAC,KAAA,CAAAC,IAAA,KAAAC,GAAA,CAAAH,GAAA;MACA,KAAA9F,IAAA,MAAAtG,YAAA,cAAAoM,GAAA,CAAA7G,IAAA;IACA;IACAiH,cAAA,WAAAA,eAAA;MAAA,IAAAC,OAAA;MACA,KAAAjH,KAAA,iBAAAqF,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAA4B,yBAAA,EAAAD,OAAA,CAAAzM,YAAA,EAAAmC,IAAA,WAAA8B,GAAA;YACAwI,OAAA,CAAA9J,QAAA,CAAAoG,OAAA;UACA,GAAAgB,OAAA;YACA0C,OAAA,CAAAlM,qBAAA;YACAkM,OAAA,CAAAjH,KAAA,CAAAmH,aAAA,CAAAC,cAAA;YACAH,OAAA,CAAA9K,iBAAA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}