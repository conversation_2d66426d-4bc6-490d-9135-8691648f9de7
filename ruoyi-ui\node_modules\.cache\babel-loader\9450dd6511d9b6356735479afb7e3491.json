{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\monitor\\leakyRecord.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\monitor\\leakyRecord.vue", "mtime": 1755591292557}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\babel.config.js", "mtime": 1751890080904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_monitor", "require", "_questResultDetails", "_interopRequireDefault", "_LeakScanDialog", "_ffJobTasks", "_log", "name", "components", "JobLog", "FfJobTasks", "LeakScanDialog", "QuestResultDetails", "dicts", "props", "toParams", "type", "Object", "default", "listType", "Number", "data", "jobType", "undefined", "openCron", "loading", "jobId", "totalScan", "ids", "single", "multiple", "showSearch", "total", "jobList", "open", "openView", "editForm", "queryParams", "pageNum", "pageSize", "isDisabled", "cronText", "rows", "getListInterval", "reportRecordDialogVisible", "reportLoading", "reportList", "reportTotal", "reportQueryParams", "taskType", "watch", "handler", "newVal", "id", "handleJobLog", "immediate", "created", "_this", "getList", "setInterval", "loopGetList", "destroyed", "clearInterval", "methods", "_this2", "getListWithDetails", "then", "response", "_this3", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleSelectionChange", "selection", "map", "item", "length", "handleCreateReport", "row", "_this4", "batchGenerateReport", "res", "$modal", "msgSuccess", "catch", "err", "msgError", "msgWarning", "jobIds", "handleReportRecord", "getReportList", "_this5", "handleView", "_objectSpread2"], "sources": ["src/views/frailty/monitor/leakyRecord.vue"], "sourcesContent": ["<template>\n  <div class=\"custom-container\">\n    <div class=\"custom-content-container-right\">\n      <div class=\"custom-content-search-box\">\n        <el-form\n          :model=\"queryParams\"\n          ref=\"queryForm\"\n          size=\"small\"\n          :inline=\"true\"\n          label-position=\"right\"\n          label-width=\"70px\"\n        >\n          <el-row :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"任务名称\" prop=\"jobName\">\n                <el-input\n                  v-model=\"queryParams.jobName\"\n                  placeholder=\"请输入任务名称\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"扫描目标\" prop=\"invokeTarget\">\n                <el-input\n                  v-model=\"queryParams.scanTagert\"\n                  placeholder=\"扫描目标\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n<!--            <el-col :span=\"6\">\n              <el-form-item label=\"任务状态\" prop=\"status\">\n                <el-select v-model=\"queryParams.status\" placeholder=\"请选择任务状态\" clearable>\n                  <el-option\n                    v-for=\"dict in dict.type.sys_job_status\"\n                    :key=\"dict.value\"\n                    :label=\"dict.label\"\n                    :value=\"dict.value\"\n                  />\n                </el-select>\n              </el-form-item>\n            </el-col>-->\n            <el-col :span=\"12\">\n              <el-form-item class=\"custom-search-btn\">\n                <el-button class=\"btn1\" size=\"small\" @click=\"handleQuery\">查询</el-button>\n                <el-button class=\"btn2\" size=\"small\" @click=\"resetQuery\">重置</el-button>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n      </div>\n\n      <div class=\"custom-content-container\">\n        <div class=\"common-header\">\n          <div><span class=\"common-head-title\">{{ listType === 4 ? '主机' : 'Web' }}漏扫记录列表</span></div>\n          <div class=\"common-head-right\">\n            <el-row :gutter=\"10\">\n              <el-col :span=\"1.5\">\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  @click=\"handleReportRecord\"\n                >报告生成记录\n                </el-button>\n              </el-col>\n              <el-col :span=\"1.5\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  :disabled=\"multiple\"\n                  @click=\"handleCreateReport\"\n                >批量生成报告\n                </el-button>\n              </el-col>\n            </el-row>\n          </div>\n        </div>\n        <el-table height=\"100%\" v-loading=\"loading\" :data=\"jobList\" ref=\"multipleTable\" @selection-change=\"handleSelectionChange\">\n          <el-table-column type=\"selection\" width=\"55\" />\n          <el-table-column label=\"任务名称\" align=\"left\" prop=\"jobName\"/>\n          <el-table-column label=\"扫描目标\" align=\"left\" prop=\"scanTarget\" width=\"150px\" :show-overflow-tooltip=\"false\"/>\n          <el-table-column label=\"扫描状态\" align=\"left\" prop=\"taskStatus\">\n            <template slot-scope=\"scope\">\n              <el-tag v-if=\"scope.row.taskStatus === 1\">正在调度</el-tag>\n              <el-tag type=\"primary\" v-else-if=\"scope.row.taskStatus === 2\">运行中</el-tag>\n              <el-tag type=\"danger\" v-else-if=\"scope.row.taskStatus === 3\">任务异常</el-tag>\n              <el-tag type=\"success\" v-else-if=\"scope.row.taskStatus === 4\">扫描完成</el-tag>\n            </template>\n          </el-table-column>\n          <el-table-column\n            label=\"扫描进度\"\n            prop=\"finishRate\"\n            width=\"120\"\n            align=\"left\"\n          >\n            <template slot-scope=\"scope\">\n              <el-progress :text-inside=\"true\" :stroke-width=\"18\" :percentage=\"scope.row.finishRate\"></el-progress>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"存活主机\" align=\"left\" prop=\"hostNum\"/>\n          <el-table-column label=\"弱口令\" align=\"left\" prop=\"pwNum\"/>\n          <el-table-column label=\"可入侵漏洞\" align=\"left\" prop=\"pocRiskNum\"/>\n          <el-table-column label=\"高危漏洞\" align=\"left\" prop=\"highRiskNum\"/>\n          <el-table-column label=\"中危漏洞\" align=\"left\" prop=\"lowRiskNum\"/>\n          <el-table-column label=\"低危漏洞\" align=\"left\" prop=\"lowRiskNum\"/>\n          <el-table-column label=\"开始时间\" align=\"left\" prop=\"startTime\"/>\n          <el-table-column label=\"结束时间\" align=\"left\" prop=\"endTime\"/>\n          <el-table-column label=\"操作\" width=\"150\" fixed=\"right\" :show-overflow-tooltip=\"false\">\n            <template slot-scope=\"scope\">\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleView(scope.row)\"\n                v-hasPermi=\"['monitor:ipschedule:query']\"\n              >详情\n              </el-button>\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                :disabled=\"scope.row.currentStatus === 1\"\n                @click=\"handleCreateReport(scope.row)\"\n              >生成报告\n              </el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n        <pagination\n          v-show=\"total>0\"\n          :total=\"total\"\n          :page.sync=\"queryParams.pageNum\"\n          :limit.sync=\"queryParams.pageSize\"\n          @pagination=\"getList\"\n        />\n      </div>\n    </div>\n\n    <el-dialog title=\"报告生成记录\" :visible.sync=\"reportRecordDialogVisible\" width=\"80%\" append-to-body>\n      <div class=\"custom-content-container\">\n        <el-table height=\"100%\" v-loading=\"reportLoading\" :data=\"reportList\" ref=\"reportTable\">\n          <el-table-column label=\"扫描目标\" align=\"left\" prop=\"scanTarget\" />\n          <el-table-column label=\"创建时间\" align=\"left\" prop=\"createTime\" />\n          <el-table-column label=\"生成时间\" align=\"left\" prop=\"generateTime\" />\n          <el-table-column label=\"生成状态\" align=\"left\" prop=\"reportStatus\">\n            <template slot-scope=\"scope\">\n              <el-tag v-if=\"scope.row.reportStatus === 0\">生成中</el-tag>\n              <el-tag type=\"success\" v-else-if=\"scope.row.reportStatus === 1\">已完成</el-tag>\n              <el-tag type=\"danger\" v-else-if=\"scope.row.reportStatus === 2\">失败</el-tag>\n            </template>\n          </el-table-column>\n<!--          <el-table-column label=\"操作\" width=\"150\" fixed=\"right\">\n            <template slot-scope=\"scope\">\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                :disabled=\"scope.row.reportStatus !== 1\"\n                @click=\"downloadReport(scope.row)\"\n              >下载\n              </el-button>\n            </template>\n          </el-table-column>-->\n        </el-table>\n        <pagination\n          v-show=\"reportTotal>0\"\n          :total=\"reportTotal\"\n          :page.sync=\"reportQueryParams.pageNum\"\n          :limit.sync=\"reportQueryParams.pageSize\"\n          @pagination=\"getReportList\"\n        />\n      </div>\n    </el-dialog>\n\n    <!-- 任务日志详细 -->\n    <el-dialog title=\"任务详细\" v-if=\"openView\" :visible.sync=\"openView\" v-dialog-drag width=\"1200px\" append-to-body>\n      <ff-job-tasks  v-if=\"openView\" :jobId=\"jobId\" :job-type=\"jobType\" :job-row=\"editForm\" />\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {batchGenerateReport, getListWithDetails, getReportList} from \"@/api/safe/monitor\";\nimport QuestResultDetails from '../../safe/server/questResultDetails'\nimport LeakScanDialog from '../../safe/server/components/LeakScanDialog'\nimport FfJobTasks from './ffJobTasks'\nimport JobLog from '../../monitor/job/log'\n\nexport default {\n  name: \"hostLeakyRecord\",\n  components: { JobLog, FfJobTasks, LeakScanDialog, QuestResultDetails },\n  dicts: ['sys_job_group', 'sys_job_status'],\n  props: {\n    toParams: {\n      type: Object,\n      default: () => {}\n    },\n    listType: {\n      type: Number,\n      default: 4\n    }\n  },\n  data() {\n    return {\n      jobType: undefined,\n      // 是否显示Cron表达式弹出层\n      openCron: false,\n      // 展示最近一次运行结果\n      // 遮罩层\n      loading: true,\n      // 任务ID\n      jobId: '',\n      totalScan: 0,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 定时任务表格数据\n      jobList: [],\n\n      // 是否显示弹出层\n      open: false,\n      // 是否显示详细弹出层\n      openView: false,\n      editForm: {},\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n      },\n      isDisabled: false,\n      // 周期转换文字\n      cronText: '',\n      rows: [],\n      getListInterval: null,\n      // 报告生成记录相关数据\n      reportRecordDialogVisible: false,\n      reportLoading: false,\n      reportList: [],\n      reportTotal: 0,\n      reportQueryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        taskType: undefined\n      }\n    };\n  },\n  watch: {\n    toParams: {\n      handler(newVal) {\n        if(newVal && newVal.id){\n          this.handleJobLog({\n            jobId: newVal.id\n          });\n        }\n      },\n      immediate: true\n    }\n  },\n  created() {\n    this.getList()\n    this.getListInterval = setInterval(() => {\n      this.loopGetList()\n    }, 5000)\n  },\n  destroyed() {\n    if(this.getListInterval){\n      clearInterval(this.getListInterval);\n    }\n  },\n  methods: {\n    /** 查询主机漏扫记录列表 */\n    getList() {\n      this.loading = true;\n      this.queryParams.taskType = this.listType === 4 ? 2 : 1;\n      getListWithDetails(this.queryParams).then(response => {\n        this.jobList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    loopGetList() {\n      this.queryParams.taskType = this.listType === 4 ? 2 : 1;\n      getListWithDetails(this.queryParams).then(response => {\n        this.jobList = response.rows;\n        this.total = response.total;\n      });\n    },\n\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.jobId);\n      this.single = selection.length != 1;\n      this.multiple = !selection.length;\n      this.rows = selection;\n    },\n\n    /** 批量生成报告 */\n    handleCreateReport(row) {\n      // 单个生成报告\n      if (row.id) {\n        // 单个生成报告\n        batchGenerateReport({\n          ids: [row.id],\n          taskType: this.listType === 4 ? 2 : 1\n        }).then(res => {\n          this.$modal.msgSuccess(\"报告生成任务已提交\");\n        }).catch(err => {\n          this.$modal.msgError(\"报告生成失败\");\n        });\n      } else {\n        // 批量生成报告\n        if (this.rows.length === 0) {\n          this.$modal.msgWarning(\"请先选择要生成报告的记录\");\n          return;\n        }\n        const jobIds = this.rows.map(item => item.id);\n        batchGenerateReport({\n          ids: jobIds,\n          taskType: this.listType === 4 ? 2 : 1\n        }).then(res => {\n          this.$modal.msgSuccess(\"批量报告生成任务已提交\");\n        }).catch(err => {\n          this.$modal.msgError(\"批量报告生成失败\");\n        });\n      }\n\n    },\n\n    /** 报告生成记录 */\n    handleReportRecord() {\n      this.reportRecordDialogVisible = true;\n      this.reportQueryParams.taskType = this.listType === 4 ? 2 : 1;\n      this.getReportList();\n    },\n\n    /** 获取报告生成记录列表 */\n    getReportList() {\n      this.reportLoading = true;\n      getReportList(this.reportQueryParams).then(response => {\n        this.reportList = response.rows;\n        this.reportTotal = response.total;\n        this.reportLoading = false;\n      }).catch(() => {\n        this.reportLoading = false;\n      });\n    },\n\n    /** 任务详细信息 */\n    handleView(row) {\n      this.openView = true;\n      this.jobType = 2;\n      this.jobId = row.jobId;\n      this.editForm = {...row}\n    },\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n@import \"@/assets/styles/assetIndex.scss\";\n.policyCol {\n  min-width: 330px;\n  margin-top: 10px;\n}\n\n.policyDesc {\n  display: flex;\n  height: 80px;\n}\n\n.policyTxt {\n  margin-left: 10px;\n  line-height: 20px;\n}\n\n.policyTitle {\n  height: 40px;\n  line-height: 40px;\n}\n\n.oneLine {\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n}\n\n::v-deep .el-table {\n  display: flex;\n  flex-direction: column;\n}\n\n::v-deep .el-table__body-wrapper {\n  overflow-y: auto;\n  flex: 1;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;AAsLA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,mBAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,eAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,WAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,IAAA,GAAAH,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAM,IAAA;EACAC,UAAA;IAAAC,MAAA,EAAAA,YAAA;IAAAC,UAAA,EAAAA,mBAAA;IAAAC,cAAA,EAAAA,uBAAA;IAAAC,kBAAA,EAAAA;EAAA;EACAC,KAAA;EACAC,KAAA;IACAC,QAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAA,SAAA;IACA;IACAC,QAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA,EAAAC,SAAA;MACA;MACAC,QAAA;MACA;MACA;MACAC,OAAA;MACA;MACAC,KAAA;MACAC,SAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,OAAA;MAEA;MACAC,IAAA;MACA;MACAC,QAAA;MACAC,QAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,QAAA;MACAC,IAAA;MACAC,eAAA;MACA;MACAC,yBAAA;MACAC,aAAA;MACAC,UAAA;MACAC,WAAA;MACAC,iBAAA;QACAV,OAAA;QACAC,QAAA;QACAU,QAAA,EAAA1B;MACA;IACA;EACA;EACA2B,KAAA;IACAnC,QAAA;MACAoC,OAAA,WAAAA,QAAAC,MAAA;QACA,IAAAA,MAAA,IAAAA,MAAA,CAAAC,EAAA;UACA,KAAAC,YAAA;YACA5B,KAAA,EAAA0B,MAAA,CAAAC;UACA;QACA;MACA;MACAE,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,OAAA;IACA,KAAAf,eAAA,GAAAgB,WAAA;MACAF,KAAA,CAAAG,WAAA;IACA;EACA;EACAC,SAAA,WAAAA,UAAA;IACA,SAAAlB,eAAA;MACAmB,aAAA,MAAAnB,eAAA;IACA;EACA;EACAoB,OAAA;IACA,iBACAL,OAAA,WAAAA,QAAA;MAAA,IAAAM,MAAA;MACA,KAAAvC,OAAA;MACA,KAAAY,WAAA,CAAAY,QAAA,QAAA9B,QAAA;MACA,IAAA8C,2BAAA,OAAA5B,WAAA,EAAA6B,IAAA,WAAAC,QAAA;QACAH,MAAA,CAAA/B,OAAA,GAAAkC,QAAA,CAAAzB,IAAA;QACAsB,MAAA,CAAAhC,KAAA,GAAAmC,QAAA,CAAAnC,KAAA;QACAgC,MAAA,CAAAvC,OAAA;MACA;IACA;IACAmC,WAAA,WAAAA,YAAA;MAAA,IAAAQ,MAAA;MACA,KAAA/B,WAAA,CAAAY,QAAA,QAAA9B,QAAA;MACA,IAAA8C,2BAAA,OAAA5B,WAAA,EAAA6B,IAAA,WAAAC,QAAA;QACAC,MAAA,CAAAnC,OAAA,GAAAkC,QAAA,CAAAzB,IAAA;QACA0B,MAAA,CAAApC,KAAA,GAAAmC,QAAA,CAAAnC,KAAA;MACA;IACA;IAEA,aACAqC,WAAA,WAAAA,YAAA;MACA,KAAAhC,WAAA,CAAAC,OAAA;MACA,KAAAoB,OAAA;IACA;IACA,aACAY,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAAF,WAAA;IACA;IACA;IACAG,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA7C,GAAA,GAAA6C,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAjD,KAAA;MAAA;MACA,KAAAG,MAAA,GAAA4C,SAAA,CAAAG,MAAA;MACA,KAAA9C,QAAA,IAAA2C,SAAA,CAAAG,MAAA;MACA,KAAAlC,IAAA,GAAA+B,SAAA;IACA;IAEA,aACAI,kBAAA,WAAAA,mBAAAC,GAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAD,GAAA,CAAAzB,EAAA;QACA;QACA,IAAA2B,4BAAA;UACApD,GAAA,GAAAkD,GAAA,CAAAzB,EAAA;UACAJ,QAAA,OAAA9B,QAAA;QACA,GAAA+C,IAAA,WAAAe,GAAA;UACAF,MAAA,CAAAG,MAAA,CAAAC,UAAA;QACA,GAAAC,KAAA,WAAAC,GAAA;UACAN,MAAA,CAAAG,MAAA,CAAAI,QAAA;QACA;MACA;QACA;QACA,SAAA5C,IAAA,CAAAkC,MAAA;UACA,KAAAM,MAAA,CAAAK,UAAA;UACA;QACA;QACA,IAAAC,MAAA,QAAA9C,IAAA,CAAAgC,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAtB,EAAA;QAAA;QACA,IAAA2B,4BAAA;UACApD,GAAA,EAAA4D,MAAA;UACAvC,QAAA,OAAA9B,QAAA;QACA,GAAA+C,IAAA,WAAAe,GAAA;UACAF,MAAA,CAAAG,MAAA,CAAAC,UAAA;QACA,GAAAC,KAAA,WAAAC,GAAA;UACAN,MAAA,CAAAG,MAAA,CAAAI,QAAA;QACA;MACA;IAEA;IAEA,aACAG,kBAAA,WAAAA,mBAAA;MACA,KAAA7C,yBAAA;MACA,KAAAI,iBAAA,CAAAC,QAAA,QAAA9B,QAAA;MACA,KAAAuE,aAAA;IACA;IAEA,iBACAA,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,KAAA9C,aAAA;MACA,IAAA6C,sBAAA,OAAA1C,iBAAA,EAAAkB,IAAA,WAAAC,QAAA;QACAwB,MAAA,CAAA7C,UAAA,GAAAqB,QAAA,CAAAzB,IAAA;QACAiD,MAAA,CAAA5C,WAAA,GAAAoB,QAAA,CAAAnC,KAAA;QACA2D,MAAA,CAAA9C,aAAA;MACA,GAAAuC,KAAA;QACAO,MAAA,CAAA9C,aAAA;MACA;IACA;IAEA,aACA+C,UAAA,WAAAA,WAAAd,GAAA;MACA,KAAA3C,QAAA;MACA,KAAAb,OAAA;MACA,KAAAI,KAAA,GAAAoD,GAAA,CAAApD,KAAA;MACA,KAAAU,QAAA,OAAAyD,cAAA,CAAA3E,OAAA,MAAA4D,GAAA;IACA;EACA;AACA", "ignoreList": []}]}