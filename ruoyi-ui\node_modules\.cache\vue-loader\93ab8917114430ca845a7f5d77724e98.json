{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\index\\index.vue?vue&type=template&id=4f88bc19&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\index\\index.vue", "mtime": 1755591292555}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751956544249}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgY2xhc3M9ImN1c3RvbS1jb250YWluZXIiPgogIDxkaXYgY2xhc3M9ImN1c3RvbS1jb250ZW50LWNvbnRhaW5lci1yaWdodCI+CiAgICA8ZGl2IGNsYXNzPSJkaXYtbWFpbi10b3AiPgogICAgICA8ZGl2IGNsYXNzPSJkaXYtbWFpbi10b3Atb25lIj4KICAgICAgICA8ZGl2IGNsYXNzPSJtYWluLXRvcC1vbmUtbGVmdCI+PGltZyBjbGFzcz0iaW1nLXN0eWxlIiBzcmM9Ii4uLy4uLy4uL2Fzc2V0cy9pbWFnZXMvZmVuZ3hpYW56b25nc2h1LnBuZyI+PC9kaXY+CiAgICAgICAgPGRpdiBjbGFzcz0ibWFpbi10b3Atb25lLXJpZ2h0Ij4KICAgICAgICAgIDxkaXYgY2xhc3M9Im1haW4tdG9wLW9uZS1yaWdodC10b3AiPnt7IHRvdGFsTnVtYmVyT2ZSaXNrcyB9fTwvZGl2PgogICAgICAgICAgPGRpdiBjbGFzcz0ibWFpbi10b3Atb25lLXJpZ2h0LWJvdHRvbSI+6aOO6Zmp5oC75pWwPC9kaXY+CiAgICAgICAgPC9kaXY+CiAgICAgIDwvZGl2PgogICAgICA8ZGl2IGNsYXNzPSJkaXYtbWFpbi10b3AtdHdvIiA6c3R5bGU9Intib3JkZXJDb2xvcjogYm9yZGVyTnVtID09PSAxID8gJyM2MzdmZWYnIDogJyNmZmYnfSIgdGFiaW5kZXg9IjAiIHJvbGU9ImJ1dHRvbiIgaWQ9ImF1dG9Gb2N1c0JveCIgQGNsaWNrPSJ0b0xvb3Bob2xlKCkiPgogICAgICAgIDxkaXYgY2xhc3M9Im1haW4tdG9wLXR3by10aHJlZS1mb3VyIj4KICAgICAgICAgIDxkaXYgY2xhc3M9InRvcC10aXRsZS1sZWZ0Ij7kuLvmnLrpo47pmak8L2Rpdj4KICAgICAgICAgIDxkaXYgY2xhc3M9InRvcC10aXRsZS1yaWdodCI+e3tob3N0Umlzay5ob3N0Umlza051bX19PC9kaXY+CiAgICAgICAgPC9kaXY+CiAgICAgICAgPGRpdiBjbGFzcz0ibWFpbi10b3AtdHdvLWJvdHRvbSI+CiAgICAgICAgICA8ZGl2IDpjbGFzcz0iY3VycmVudEluZGV4ID09PSAnaXAnK2luZGV4ID8gJ2ljb25zLXRpdGxlLWNvdW50IGljb25zLXRpdGxlLWNvdW50LWFjdGl2ZSc6J2ljb25zLXRpdGxlLWNvdW50JyIgdi1mb3I9IihpdGVtLGluZGV4KSBpbiBob3N0Umlza0xpc3QiIEBjbGljay5zdG9wPSJ0b0xvb3Bob2xlQnlUeXBlKGl0ZW0uc2V2ZXJpdHksaW5kZXgpIj4KICAgICAgICAgICAgPGRpdiBjbGFzcz0iaWNvbnMtdGl0bGUtY291bnQtaW1nIj48aW1nIGNsYXNzPSJ0aXRsZS1jb3VudC1pbWctc3R5bGUiIDpzcmM9Iml0ZW0uaW1nIj48L2Rpdj4KICAgICAgICAgICAgPGRpdiBjbGFzcz0iaWNvbnMtdGl0bGUtY291bnQtcmlnaHQiPgogICAgICAgICAgICAgIDxkaXYgY2xhc3M9Imljb25zLXRpdGxlLWNvdW50LXRvcCI+e3sgaXRlbS5udW0gfX08L2Rpdj4KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJpY29ucy10aXRsZS1jb3VudC1ib3R0b20iPnt7IGl0ZW0udGl0bGUgfX08L2Rpdj4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICA8L2Rpdj4KICAgICAgICAgIDxkaXYgOmNsYXNzPSJjdXJyZW50SW5kZXggPT0gJ2lwJysnNCcgPyAnaWNvbnMtdGl0bGUtY291bnQgaWNvbnMtdGl0bGUtY291bnQtYWN0aXZlJzonaWNvbnMtdGl0bGUtY291bnQnIiBAY2xpY2suc3RvcD0idG9XZWFrUGFzc3dvcmQoKSI+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9Imljb25zLXRpdGxlLWNvdW50LWltZyI+PGltZyBjbGFzcz0idGl0bGUtY291bnQtaW1nLXN0eWxlIiBzcmM9Ii4uLy4uLy4uL2Fzc2V0cy9pbWFnZXMvcnVva29saW5nLnBuZyI+PC9kaXY+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9Imljb25zLXRpdGxlLWNvdW50LXJpZ2h0Ij4KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJpY29ucy10aXRsZS1jb3VudC10b3AiPnt7IGhvc3RSaXNrLndlYWtQYXNzd29yZHNOdW0gfX08L2Rpdj4KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJpY29ucy10aXRsZS1jb3VudC1ib3R0b20iPuW8seWPo+S7pDwvZGl2PgogICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgIDwvZGl2PgogICAgICAgIDwvZGl2PgogICAgICA8L2Rpdj4KICAgICAgPGRpdiBjbGFzcz0iZGl2LW1haW4tdG9wLXRocmVlIiA6c3R5bGU9Intib3JkZXJDb2xvcjogYm9yZGVyTnVtID09PSAyID8gJyM2MzdmZWYnIDogJyNmZmYnfSIgdGFiaW5kZXg9IjAiIEBjbGljaz0idG9XZWJ2dWxuUGFnZSgpIj4KICAgICAgICA8ZGl2IGNsYXNzPSJtYWluLXRvcC10d28tdGhyZWUtZm91ciI+CiAgICAgICAgICA8ZGl2IGNsYXNzPSJ0b3AtdGl0bGUtbGVmdCI+V2Vi6aOO6ZmpPC9kaXY+CiAgICAgICAgICA8ZGl2IGNsYXNzPSJ0b3AtdGl0bGUtcmlnaHQiPnt7IHdlYlJpc2sud2ViUmlza051bSB9fTwvZGl2PgogICAgICAgIDwvZGl2PgogICAgICAgIDxkaXYgY2xhc3M9Im1haW4tdG9wLXRocmVlLWJvdHRvbSI+CiAgICAgICAgICA8ZGl2IDpjbGFzcz0iY3VycmVudEluZGV4ID09PSAnd2ViJytpbmRleCA/ICd0b3AtdGhyZWUtYm90dG9tLWJvZHkgaWNvbnMtdGl0bGUtY291bnQgaWNvbnMtdGl0bGUtY291bnQtYWN0aXZlJyA6ICd0b3AtdGhyZWUtYm90dG9tLWJvZHkgaWNvbnMtdGl0bGUtY291bnQnIiB2LWZvcj0iKGl0ZW0saW5kZXgpIGluIHdlYlJpc2tMaXN0IiBAY2xpY2suc3RvcD0idG9XZWJ2dWxuQnlUeXBlKGl0ZW0uc2V2ZXJpdHksaW5kZXgpIj4KICAgICAgICAgICAgPGRpdiBjbGFzcz0iaWNvbnMtdGl0bGUtY291bnQtaW1nIj48aW1nIGNsYXNzPSJ0aXRsZS1jb3VudC1pbWctc3R5bGUiIDpzcmM9Iml0ZW0uaW1nIj48L2Rpdj4KICAgICAgICAgICAgPGRpdiBjbGFzcz0iaWNvbnMtdGl0bGUtY291bnQtcmlnaHQiPgogICAgICAgICAgICAgIDxkaXYgY2xhc3M9Imljb25zLXRpdGxlLWNvdW50LXRvcCI+e3sgaXRlbS5udW0gfX08L2Rpdj4KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJpY29ucy10aXRsZS1jb3VudC1ib3R0b20iPnt7IGl0ZW0udGl0bGUgfX08L2Rpdj4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICA8L2Rpdj4KICAgICAgICA8L2Rpdj4KICAgICAgPC9kaXY+CiAgICAgIDxkaXYgY2xhc3M9ImRpdi1tYWluLXRvcC1mb3VyIiA6c3R5bGU9Intib3JkZXJDb2xvcjogYm9yZGVyTnVtID09PSAzID8gJyM2MzdmZWYnIDogJyNmZmYnfSIgdGFiaW5kZXg9IjAiIEBjbGljaz0idG9Nb25pdG9ySXBQYWdlKDQpIj4KICAgICAgICA8ZGl2IGNsYXNzPSJtYWluLXRvcC10d28tdGhyZWUtZm91ciI+CiAgICAgICAgICA8ZGl2IGNsYXNzPSJ0b3AtdGl0bGUtbGVmdCI+5ryP5omr5Lu75YqhPC9kaXY+CiAgICAgICAgICA8ZGl2IGNsYXNzPSJ0b3AtdGl0bGUtcmlnaHQiPnt7IHZ1bG5lcmFiaWxpdHlTY2FubmluZy52dWxuZXJhYmlsaXR5U2Nhbm5pbmdOdW0gfX08L2Rpdj4KICAgICAgICA8L2Rpdj4KICAgICAgICA8ZGl2IGNsYXNzPSJtYWluLXRvcC1mb3VyLWJvdHRvbSI+CiAgICAgICAgICA8ZGl2IDpjbGFzcz0iY3VycmVudEluZGV4ID09PSAnbW9uaXRvcklwJys0ID8gJ3RvcC1mb3VyLWJvdHRvbS1ib2R5IGljb25zLXRpdGxlLWNvdW50IGljb25zLXRpdGxlLWNvdW50LWFjdGl2ZScgOiAndG9wLWZvdXItYm90dG9tLWJvZHkgaWNvbnMtdGl0bGUtY291bnQnIiBAY2xpY2suc3RvcD0idG9Nb25pdG9ySXBQYWdlKDQpIj4KICAgICAgICAgICAgPGRpdiBjbGFzcz0iaWNvbnMtdGl0bGUtY291bnQtaW1nIj48aW1nIGNsYXNzPSJ0aXRsZS1jb3VudC1pbWctc3R5bGUiIHNyYz0iLi4vLi4vLi4vYXNzZXRzL2ltYWdlcy96aHVqaS5wbmciPjwvZGl2PgogICAgICAgICAgICA8ZGl2IGNsYXNzPSJpY29ucy10aXRsZS1jb3VudC1yaWdodCI+CiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iaWNvbnMtdGl0bGUtY291bnQtdG9wIj57eyB2dWxuZXJhYmlsaXR5U2Nhbm5pbmcuaG9zdFNjYW5uaW5nTnVtIH19PC9kaXY+CiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iaWNvbnMtdGl0bGUtY291bnQtYm90dG9tIj7kuLvmnLrmvI/mias8L2Rpdj4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICA8L2Rpdj4KICAgICAgICAgIDxkaXYgOmNsYXNzPSJjdXJyZW50SW5kZXggPT09ICdtb25pdG9yV2ViJys1ID8gJ3RvcC1mb3VyLWJvdHRvbS1ib2R5IGljb25zLXRpdGxlLWNvdW50IGljb25zLXRpdGxlLWNvdW50LWFjdGl2ZScgOiAndG9wLWZvdXItYm90dG9tLWJvZHkgaWNvbnMtdGl0bGUtY291bnQnIiBAY2xpY2suc3RvcD0idG9Nb25pdG9yV2ViKDUpIj4KICAgICAgICAgICAgPGRpdiBjbGFzcz0iaWNvbnMtdGl0bGUtY291bnQtaW1nIj48aW1nIGNsYXNzPSJ0aXRsZS1jb3VudC1pbWctc3R5bGUiIHNyYz0iLi4vLi4vLi4vYXNzZXRzL2ltYWdlcy93ZWJsb3VzYW8ucG5nIj48L2Rpdj4KICAgICAgICAgICAgPGRpdiBjbGFzcz0iaWNvbnMtdGl0bGUtY291bnQtcmlnaHQiPgogICAgICAgICAgICAgIDxkaXYgY2xhc3M9Imljb25zLXRpdGxlLWNvdW50LXRvcCI+e3sgdnVsbmVyYWJpbGl0eVNjYW5uaW5nLndlYlZ1bG5lcmFiaWxpdHlTY2FubmluZ051bSB9fTwvZGl2PgogICAgICAgICAgICAgIDxkaXYgY2xhc3M9Imljb25zLXRpdGxlLWNvdW50LWJvdHRvbSI+V2Vi5ryP5omrPC9kaXY+CiAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgPC9kaXY+CiAgICAgICAgPC9kaXY+CiAgICAgIDwvZGl2PgogICAgPC9kaXY+CiAgICA8ZWwtdGFicyB2LWlmPSJsaXN0VHlwZSA9PT0gNCB8fCBsaXN0VHlwZSA9PT0gNSIgdi1tb2RlbD0iYWN0aXZlTmFtZSIgY2xhc3M9InRhYnMtc3R5bGUiPgogICAgICA8ZWwtdGFiLXBhbmUgOmxhYmVsPSJsaXN0VHlwZSA9PT0gNCA/ICfkuLvmnLrmvI/miavku7vliqEnIDogJ1dlYua8j+aJq+S7u+WKoSciIG5hbWU9InRhc2siIC8+CiAgICAgIDxlbC10YWItcGFuZSA6bGFiZWw9Imxpc3RUeXBlID09PSA0ID8gJ+S4u+acuua8j+aJq+iusOW9lScgOiAnV2Vi5ryP5omr6K6w5b2VJyIgbmFtZT0icmVjb3JkIiAvPgogICAgPC9lbC10YWJzPgogICAgPGRpdiA6Y2xhc3M9IihsaXN0VHlwZSA9PT0gNCB8fCBsaXN0VHlwZSA9PT0gNSkgPyAnZGl2LW1haW4tY29udGFpbmVyLXRhYnMnIDogJ2Rpdi1tYWluLWNvbnRhaW5lciciPgogICAgICA8aW5kZXggdi1pZj0ibGlzdFR5cGUgPT09IDEiIDpzZXZlcml0eT0ibG9vcGhvbGVTZXZlcml0eSIgOnRvUGFyYW1zPSJ0b1BhcmFtcyIvPgogICAgICA8d2VidnVsbiB2LWlmPSJsaXN0VHlwZSA9PT0gMiIgOnNldmVyaXR5PSJ3ZWJ2dWxuU2V2ZXJpdHkiIDp0b1BhcmFtcz0idG9QYXJhbXMiLz4KICAgICAgPEluZGV4MiB2LWlmPSJsaXN0VHlwZSAgPT09IDMiIDp0b1BhcmFtcz0idG9QYXJhbXMiLz4KICAgICAgPEpvYiB2LWlmPSJsaXN0VHlwZSA9PT0gNCAmJiBhY3RpdmVOYW1lID09PSAndGFzayciIDp0b1BhcmFtcz0idG9QYXJhbXMiLz4KICAgICAgPE1vbml0b3JXZWIgdi1pZj0ibGlzdFR5cGUgPT09IDUgJiYgYWN0aXZlTmFtZSA9PT0gJ3Rhc2snIiA6dG9QYXJhbXM9InRvUGFyYW1zIi8+CiAgICAgIDxMZWFreVJlY29yZAogICAgICAgIHYtaWY9IihsaXN0VHlwZSA9PT0gNCB8fCBsaXN0VHlwZSA9PT0gNSkgJiYgYWN0aXZlTmFtZSA9PT0gJ3JlY29yZCciCiAgICAgICAgOmxpc3QtdHlwZT0ibGlzdFR5cGUiCiAgICAgICAgOnRvUGFyYW1zPSJ0b1BhcmFtcyIvPgogICAgPC9kaXY+CiAgPC9kaXY+CjwvZGl2Pgo="}, null]}